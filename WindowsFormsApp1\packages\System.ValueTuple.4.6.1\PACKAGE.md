## About

Provides implementations for various value tuple variants. A value tuple is a data structure that has a specific number and sequence of values.

## Main Types

The main types provided by this library are:

- `System.ValueTuple`
- `System.ValueTuple<T1>`
- `System.ValueTuple<T1,T2>`
- `System.ValueTuple<T1,T2,T3>`
- `System.ValueTuple<T1,T2,T3,T4>`
- `System.ValueTuple<T1,T2,T3,T4,T5>`
- `System.ValueTuple<T1,T2,T3,T4,T5,T6>`
- `System.ValueTuple<T1,T2,T3,T4,T5,T6,T7>`
- `System.ValueTuple<T1,T2,T3,T4,T5,T6,T7,TRest>`
- `System.TupleExtensions`
- `System.Runtime.CompilerServices.TupleElementNamesAttribute`

## Additional Documentation

- For more information about these APIs, see [Value tuples](https://learn.microsoft.com/en-us/dotnet/standard/value-tuples).

## License

`System.ValueTuple` is released as open source under the [MIT license](https://licenses.nuget.org/MIT).