<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.Extensions.Configuration.Binder</name>
    </assembly>
    <members>
        <member name="T:Microsoft.Extensions.Configuration.BinderOptions">
            <summary>
            Specifies options used by the <see cref="T:Microsoft.Extensions.Configuration.ConfigurationBinder"/>.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Configuration.BinderOptions.BindNonPublicProperties">
            <summary>
            Gets or sets a value that indicates whether the binder attempts to set all properties or only public properties.
            </summary>
            <value>
            <see langword="true" /> if the binder attempts to set all non-read-only properties; <see langword="false" /> if only public properties are set.
            </value>
        </member>
        <member name="P:Microsoft.Extensions.Configuration.BinderOptions.ErrorOnUnknownConfiguration">
            <summary>
            Gets or sets a value that indicates whether exceptions are thrown when converting a value or when a configuration
            key is found for which the provided model object doesn't have an appropriate property that matches the key's name.
            </summary>
            <value>
            <see langword="true" /> if an <see cref="T:System.InvalidOperationException"/> is thrown with a description; <see langword="false" /> if no exceptions are thrown. The default is <see langword="false" />.
            </value>
        </member>
        <member name="T:Microsoft.Extensions.Configuration.ConfigurationBinder">
            <summary>
            Static helper class that allows binding strongly typed objects to configuration values.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.ConfigurationBinder.Get``1(Microsoft.Extensions.Configuration.IConfiguration)">
            <summary>
            Attempts to bind the configuration instance to a new instance of type T.
            If this configuration section has a value, that will be used.
            Otherwise binding by matching property names against configuration keys recursively.
            </summary>
            <typeparam name="T">The type of the new instance to bind.</typeparam>
            <param name="configuration">The configuration instance to bind.</param>
            <returns>The new instance of T if successful, default(T) otherwise.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.ConfigurationBinder.Get``1(Microsoft.Extensions.Configuration.IConfiguration,System.Action{Microsoft.Extensions.Configuration.BinderOptions})">
            <summary>
            Attempts to bind the configuration instance to a new instance of type T.
            If this configuration section has a value, that will be used.
            Otherwise binding by matching property names against configuration keys recursively.
            </summary>
            <typeparam name="T">The type of the new instance to bind.</typeparam>
            <param name="configuration">The configuration instance to bind.</param>
            <param name="configureOptions">Configures the binder options.</param>
            <returns>The new instance of T if successful, default(T) otherwise.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.ConfigurationBinder.Get(Microsoft.Extensions.Configuration.IConfiguration,System.Type)">
            <summary>
            Attempts to bind the configuration instance to a new instance of type T.
            If this configuration section has a value, that will be used.
            Otherwise binding by matching property names against configuration keys recursively.
            </summary>
            <param name="configuration">The configuration instance to bind.</param>
            <param name="type">The type of the new instance to bind.</param>
            <returns>The new instance if successful, null otherwise.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.ConfigurationBinder.Get(Microsoft.Extensions.Configuration.IConfiguration,System.Type,System.Action{Microsoft.Extensions.Configuration.BinderOptions})">
            <summary>
            Attempts to bind the configuration instance to a new instance of type T.
            If this configuration section has a value, that will be used.
            Otherwise binding by matching property names against configuration keys recursively.
            </summary>
            <param name="configuration">The configuration instance to bind.</param>
            <param name="type">The type of the new instance to bind.</param>
            <param name="configureOptions">Configures the binder options.</param>
            <returns>The new instance if successful, null otherwise.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.ConfigurationBinder.Bind(Microsoft.Extensions.Configuration.IConfiguration,System.String,System.Object)">
            <summary>
            Attempts to bind the given object instance to the configuration section specified by the key by matching property names against configuration keys recursively.
            </summary>
            <param name="configuration">The configuration instance to bind.</param>
            <param name="key">The key of the configuration section to bind.</param>
            <param name="instance">The object to bind.</param>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.ConfigurationBinder.Bind(Microsoft.Extensions.Configuration.IConfiguration,System.Object)">
            <summary>
            Attempts to bind the given object instance to configuration values by matching property names against configuration keys recursively.
            </summary>
            <param name="configuration">The configuration instance to bind.</param>
            <param name="instance">The object to bind.</param>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.ConfigurationBinder.Bind(Microsoft.Extensions.Configuration.IConfiguration,System.Object,System.Action{Microsoft.Extensions.Configuration.BinderOptions})">
            <summary>
            Attempts to bind the given object instance to configuration values by matching property names against configuration keys recursively.
            </summary>
            <param name="configuration">The configuration instance to bind.</param>
            <param name="instance">The object to bind.</param>
            <param name="configureOptions">Configures the binder options.</param>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.ConfigurationBinder.GetValue``1(Microsoft.Extensions.Configuration.IConfiguration,System.String)">
            <summary>
            Extracts the value with the specified key and converts it to type T.
            </summary>
            <typeparam name="T">The type to convert the value to.</typeparam>
            <param name="configuration">The configuration.</param>
            <param name="key">The key of the configuration section's value to convert.</param>
            <returns>The converted value.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.ConfigurationBinder.GetValue``1(Microsoft.Extensions.Configuration.IConfiguration,System.String,``0)">
            <summary>
            Extracts the value with the specified key and converts it to type T.
            </summary>
            <typeparam name="T">The type to convert the value to.</typeparam>
            <param name="configuration">The configuration.</param>
            <param name="key">The key of the configuration section's value to convert.</param>
            <param name="defaultValue">The default value to use if no value is found.</param>
            <returns>The converted value.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.ConfigurationBinder.GetValue(Microsoft.Extensions.Configuration.IConfiguration,System.Type,System.String)">
            <summary>
            Extracts the value with the specified key and converts it to the specified type.
            </summary>
            <param name="configuration">The configuration.</param>
            <param name="type">The type to convert the value to.</param>
            <param name="key">The key of the configuration section's value to convert.</param>
            <returns>The converted value.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.ConfigurationBinder.GetValue(Microsoft.Extensions.Configuration.IConfiguration,System.Type,System.String,System.Object)">
            <summary>
            Extracts the value with the specified key and converts it to the specified type.
            </summary>
            <param name="configuration">The configuration.</param>
            <param name="type">The type to convert the value to.</param>
            <param name="key">The key of the configuration section's value to convert.</param>
            <param name="defaultValue">The default value to use if no value is found.</param>
            <returns>The converted value.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.ConfigurationBinder.ResetPropertyValue(System.Reflection.PropertyInfo,System.Object,Microsoft.Extensions.Configuration.BinderOptions)">
            <summary>
            Reset the property value to the value from the property getter. This is useful for properties that have a getter or setters that perform some logic changing the object state.
            </summary>
            <param name="property">The property to reset.</param>
            <param name="instance">The instance to reset the property on.</param>
            <param name="options">The binder options.</param>
            <remarks>
            This method doesn't do any configuration binding. It just resets the property value to the value from the property getter.
            This method called only when creating an instance using a primary constructor with parameters names match properties names.
            </remarks>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.ConfigurationBinder.CreateInstance(System.Type,Microsoft.Extensions.Configuration.IConfiguration,Microsoft.Extensions.Configuration.BinderOptions,System.Reflection.ParameterInfo[]@)">
            <summary>
            Create an instance of the specified type.
            </summary>
            <param name="type">The type to create an instance of.</param>
            <param name="config">The configuration to bind to the instance.</param>
            <param name="options">The binder options.</param>
            <param name="constructorParameters">The parameters of the constructor used to create the instance.</param>
            <returns>The created instance.</returns>
            <exception cref="T:System.InvalidOperationException">If the type cannot be created.</exception>
            <remarks>
            constructorParameters will not be null only when using a constructor with a parameters which get their values from the configuration
            This happen when using types having properties match the constructor parameter names. `record` types are an example.
            In such cases we need to carry the parameters list to avoid binding the properties again during BindProperties.
            </remarks>
        </member>
        <member name="M:System.ThrowHelper.ThrowIfNull(System.Object,System.String)">
            <summary>Throws an <see cref="T:System.ArgumentNullException"/> if <paramref name="argument"/> is null.</summary>
            <param name="argument">The reference type argument to validate as non-null.</param>
            <param name="paramName">The name of the parameter with which <paramref name="argument"/> corresponds.</param>
        </member>
        <member name="M:System.ThrowHelper.IfNullOrWhitespace(System.String,System.String)">
            <summary>
            Throws either an <see cref="T:System.ArgumentNullException"/> or an <see cref="T:System.ArgumentException"/>
            if the specified string is <see langword="null"/> or whitespace respectively.
            </summary>
            <param name="argument">String to be checked for <see langword="null"/> or whitespace.</param>
            <param name="paramName">The name of the parameter being checked.</param>
            <returns>The original value of <paramref name="argument"/>.</returns>
        </member>
        <member name="T:System.Runtime.InteropServices.LibraryImportAttribute">
            <summary>
            Attribute used to indicate a source generator should create a function for marshalling
            arguments instead of relying on the runtime to generate an equivalent marshalling function at run-time.
            </summary>
            <remarks>
            This attribute is meaningless if the source generator associated with it is not enabled.
            The current built-in source generator only supports C# and only supplies an implementation when
            applied to static, partial, non-generic methods.
            </remarks>
        </member>
        <member name="M:System.Runtime.InteropServices.LibraryImportAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Runtime.InteropServices.LibraryImportAttribute"/>.
            </summary>
            <param name="libraryName">Name of the library containing the import.</param>
        </member>
        <member name="P:System.Runtime.InteropServices.LibraryImportAttribute.LibraryName">
            <summary>
            Gets the name of the library containing the import.
            </summary>
        </member>
        <member name="P:System.Runtime.InteropServices.LibraryImportAttribute.EntryPoint">
            <summary>
            Gets or sets the name of the entry point to be called.
            </summary>
        </member>
        <member name="P:System.Runtime.InteropServices.LibraryImportAttribute.StringMarshalling">
            <summary>
            Gets or sets how to marshal string arguments to the method.
            </summary>
            <remarks>
            If this field is set to a value other than <see cref="F:System.Runtime.InteropServices.StringMarshalling.Custom" />,
            <see cref="P:System.Runtime.InteropServices.LibraryImportAttribute.StringMarshallingCustomType" /> must not be specified.
            </remarks>
        </member>
        <member name="P:System.Runtime.InteropServices.LibraryImportAttribute.StringMarshallingCustomType">
            <summary>
            Gets or sets the <see cref="T:System.Type"/> used to control how string arguments to the method are marshalled.
            </summary>
            <remarks>
            If this field is specified, <see cref="P:System.Runtime.InteropServices.LibraryImportAttribute.StringMarshalling" /> must not be specified
            or must be set to <see cref="F:System.Runtime.InteropServices.StringMarshalling.Custom" />.
            </remarks>
        </member>
        <member name="P:System.Runtime.InteropServices.LibraryImportAttribute.SetLastError">
            <summary>
            Gets or sets whether the callee sets an error (SetLastError on Windows or errno
            on other platforms) before returning from the attributed method.
            </summary>
        </member>
        <member name="T:System.Runtime.InteropServices.StringMarshalling">
            <summary>
            Specifies how strings should be marshalled for generated p/invokes
            </summary>
        </member>
        <member name="F:System.Runtime.InteropServices.StringMarshalling.Custom">
            <summary>
            Indicates the user is supplying a specific marshaller in <see cref="P:System.Runtime.InteropServices.LibraryImportAttribute.StringMarshallingCustomType"/>.
            </summary>
        </member>
        <member name="F:System.Runtime.InteropServices.StringMarshalling.Utf8">
            <summary>
            Use the platform-provided UTF-8 marshaller.
            </summary>
        </member>
        <member name="F:System.Runtime.InteropServices.StringMarshalling.Utf16">
            <summary>
            Use the platform-provided UTF-16 marshaller.
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMembersAttribute">
             <summary>
             Indicates that certain members on a specified <see cref="T:System.Type"/> are accessed dynamically,
             for example through <see cref="N:System.Reflection"/>.
             </summary>
             <remarks>
             This allows tools to understand which members are being accessed during the execution
             of a program.
            
             This attribute is valid on members whose type is <see cref="T:System.Type"/> or <see cref="T:System.String"/>.
            
             When this attribute is applied to a location of type <see cref="T:System.String"/>, the assumption is
             that the string represents a fully qualified type name.
            
             When this attribute is applied to a class, interface, or struct, the members specified
             can be accessed dynamically on <see cref="T:System.Type"/> instances returned from calling
             <see cref="M:System.Object.GetType"/> on instances of that class, interface, or struct.
            
             If the attribute is applied to a method it's treated as a special case and it implies
             the attribute should be applied to the "this" parameter of the method. As such the attribute
             should only be used on instance methods of types assignable to System.Type (or string, but no methods
             will use it there).
             </remarks>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMembersAttribute.#ctor(System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMembersAttribute"/> class
            with the specified member types.
            </summary>
            <param name="memberTypes">The types of members dynamically accessed.</param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMembersAttribute.MemberTypes">
            <summary>
            Gets the <see cref="T:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes"/> which specifies the type
            of members dynamically accessed.
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes">
             <summary>
             Specifies the types of members that are dynamically accessed.
            
             This enumeration has a <see cref="T:System.FlagsAttribute"/> attribute that allows a
             bitwise combination of its member values.
             </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.None">
            <summary>
            Specifies no members.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.PublicParameterlessConstructor">
            <summary>
            Specifies the default, parameterless public constructor.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.PublicConstructors">
            <summary>
            Specifies all public constructors.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.NonPublicConstructors">
            <summary>
            Specifies all non-public constructors.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.PublicMethods">
            <summary>
            Specifies all public methods.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.NonPublicMethods">
            <summary>
            Specifies all non-public methods.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.PublicFields">
            <summary>
            Specifies all public fields.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.NonPublicFields">
            <summary>
            Specifies all non-public fields.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.PublicNestedTypes">
            <summary>
            Specifies all public nested types.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.NonPublicNestedTypes">
            <summary>
            Specifies all non-public nested types.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.PublicProperties">
            <summary>
            Specifies all public properties.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.NonPublicProperties">
            <summary>
            Specifies all non-public properties.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.PublicEvents">
            <summary>
            Specifies all public events.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.NonPublicEvents">
            <summary>
            Specifies all non-public events.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.Interfaces">
            <summary>
            Specifies all interfaces implemented by the type.
            </summary>
        </member>
        <member name="F:System.Diagnostics.CodeAnalysis.DynamicallyAccessedMemberTypes.All">
            <summary>
            Specifies all members.
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.RequiresDynamicCodeAttribute">
            <summary>
            Indicates that the specified method requires the ability to generate new code at runtime,
            for example through <see cref="N:System.Reflection"/>.
            </summary>
            <remarks>
            This allows tools to understand which methods are unsafe to call when compiling ahead of time.
            </remarks>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.RequiresDynamicCodeAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Diagnostics.CodeAnalysis.RequiresDynamicCodeAttribute"/> class
            with the specified message.
            </summary>
            <param name="message">
            A message that contains information about the usage of dynamic code.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.RequiresDynamicCodeAttribute.Message">
            <summary>
            Gets a message that contains information about the usage of dynamic code.
            </summary>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.RequiresDynamicCodeAttribute.Url">
            <summary>
            Gets or sets an optional URL that contains more information about the method,
            why it requires dynamic code, and what options a consumer has to deal with it.
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.RequiresUnreferencedCodeAttribute">
            <summary>
            Indicates that the specified method requires dynamic access to code that is not referenced
            statically, for example through <see cref="N:System.Reflection"/>.
            </summary>
            <remarks>
            This allows tools to understand which methods are unsafe to call when removing unreferenced
            code from an application.
            </remarks>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.RequiresUnreferencedCodeAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Diagnostics.CodeAnalysis.RequiresUnreferencedCodeAttribute"/> class
            with the specified message.
            </summary>
            <param name="message">
            A message that contains information about the usage of unreferenced code.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.RequiresUnreferencedCodeAttribute.Message">
            <summary>
            Gets a message that contains information about the usage of unreferenced code.
            </summary>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.RequiresUnreferencedCodeAttribute.Url">
            <summary>
            Gets or sets an optional URL that contains more information about the method,
            why it requires unreferenced code, and what options a consumer has to deal with it.
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute">
            <summary>
            Suppresses reporting of a specific rule violation, allowing multiple suppressions on a
            single code artifact.
            </summary>
            <remarks>
            <see cref="T:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute"/> is different than
            <see cref="T:System.Diagnostics.CodeAnalysis.SuppressMessageAttribute"/> in that it doesn't have a
            <see cref="T:System.Diagnostics.ConditionalAttribute"/>. So it is always preserved in the compiled assembly.
            </remarks>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute"/>
            class, specifying the category of the tool and the identifier for an analysis rule.
            </summary>
            <param name="category">The category for the attribute.</param>
            <param name="checkId">The identifier of the analysis rule the attribute applies to.</param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.Category">
            <summary>
            Gets the category identifying the classification of the attribute.
            </summary>
            <remarks>
            The <see cref="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.Category"/> property describes the tool or tool analysis category
            for which a message suppression attribute applies.
            </remarks>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.CheckId">
            <summary>
            Gets the identifier of the analysis tool rule to be suppressed.
            </summary>
            <remarks>
            Concatenated together, the <see cref="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.Category"/> and <see cref="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.CheckId"/>
            properties form a unique check identifier.
            </remarks>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.Scope">
            <summary>
            Gets or sets the scope of the code that is relevant for the attribute.
            </summary>
            <remarks>
            The Scope property is an optional argument that specifies the metadata scope for which
            the attribute is relevant.
            </remarks>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.Target">
            <summary>
            Gets or sets a fully qualified path that represents the target of the attribute.
            </summary>
            <remarks>
            The <see cref="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.Target"/> property is an optional argument identifying the analysis target
            of the attribute. An example value is "System.IO.Stream.ctor():System.Void".
            Because it is fully qualified, it can be long, particularly for targets such as parameters.
            The analysis tool user interface should be capable of automatically formatting the parameter.
            </remarks>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.MessageId">
            <summary>
            Gets or sets an optional argument expanding on exclusion criteria.
            </summary>
            <remarks>
            The <see cref="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.MessageId"/> property is an optional argument that specifies additional
            exclusion where the literal metadata target is not sufficiently precise. For example,
            the <see cref="T:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute"/> cannot be applied within a method,
            and it may be desirable to suppress a violation against a statement in the method that will
            give a rule violation, but not against all statements in the method.
            </remarks>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.UnconditionalSuppressMessageAttribute.Justification">
            <summary>
            Gets or sets the justification for suppressing the code analysis message.
            </summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.AllowNullAttribute">
            <summary>Specifies that null is allowed as an input even if the corresponding type disallows it.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DisallowNullAttribute">
            <summary>Specifies that null is disallowed as an input even if the corresponding type allows it.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MaybeNullAttribute">
            <summary>Specifies that an output may be null even if the corresponding type disallows it.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullAttribute">
            <summary>Specifies that an output will not be null even if the corresponding type allows it. Specifies that an input argument was not null when the call returns.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute">
            <summary>Specifies that when a method returns <see cref="P:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute.ReturnValue"/>, the parameter may be null even if the corresponding type disallows it.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute.#ctor(System.Boolean)">
            <summary>Initializes the attribute with the specified return value condition.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated parameter may be null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute.ReturnValue">
            <summary>Gets the return value condition.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute">
            <summary>Specifies that when a method returns <see cref="P:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.ReturnValue"/>, the parameter will not be null even if the corresponding type allows it.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.#ctor(System.Boolean)">
            <summary>Initializes the attribute with the specified return value condition.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated parameter will not be null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.ReturnValue">
            <summary>Gets the return value condition.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute">
            <summary>Specifies that the output will be non-null if the named parameter is non-null.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute.#ctor(System.String)">
            <summary>Initializes the attribute with the associated parameter name.</summary>
            <param name="parameterName">
            The associated parameter name.  The output will be non-null if the argument to the parameter specified is non-null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute.ParameterName">
            <summary>Gets the associated parameter name.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DoesNotReturnAttribute">
            <summary>Applied to a method that will never return under any circumstance.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DoesNotReturnIfAttribute">
            <summary>Specifies that the method will not return if the associated Boolean parameter is passed the specified value.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.DoesNotReturnIfAttribute.#ctor(System.Boolean)">
            <summary>Initializes the attribute with the specified parameter value.</summary>
            <param name="parameterValue">
            The condition parameter value. Code after the method will be considered unreachable by diagnostics if the argument to
            the associated parameter matches this value.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.DoesNotReturnIfAttribute.ParameterValue">
            <summary>Gets the condition parameter value.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute">
            <summary>Specifies that the method or property will ensure that the listed field and property members have not-null values.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute.#ctor(System.String)">
            <summary>Initializes the attribute with a field or property member.</summary>
            <param name="member">
            The field or property member that is promised to be not-null.
            </param>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute.#ctor(System.String[])">
            <summary>Initializes the attribute with the list of field and property members.</summary>
            <param name="members">
            The list of field and property members that are promised to be not-null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute.Members">
            <summary>Gets field or property member names.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute">
            <summary>Specifies that the method or property will ensure that the listed field and property members have not-null values when returning with the specified return value condition.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.#ctor(System.Boolean,System.String)">
            <summary>Initializes the attribute with the specified return value condition and a field or property member.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated field or property member will not be null.
            </param>
            <param name="member">
            The field or property member that is promised to be not-null.
            </param>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.#ctor(System.Boolean,System.String[])">
            <summary>Initializes the attribute with the specified return value condition and list of field and property members.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated field and property members will not be null.
            </param>
            <param name="members">
            The list of field and property members that are promised to be not-null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.ReturnValue">
            <summary>Gets the return value condition.</summary>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.Members">
            <summary>Gets field or property member names.</summary>
        </member>
        <member name="P:System.SR.Error_CannotActivateAbstractOrInterface">
            <summary>Cannot create instance of type '{0}' because it is either abstract or an interface.</summary>
        </member>
        <member name="P:System.SR.Error_CannotBindToConstructorParameter">
            <summary>Cannot create instance of type '{0}' because one or more parameters cannot be bound to. Constructor parameters cannot be declared as in, out, or ref. Invalid parameters are: '{1}'</summary>
        </member>
        <member name="P:System.SR.Error_ConstructorParametersDoNotMatchProperties">
            <summary>Cannot create instance of type '{0}' because one or more parameters cannot be bound to. Constructor parameters must have corresponding properties. Fields are not supported. Missing properties are: '{1}'</summary>
        </member>
        <member name="P:System.SR.Error_FailedBinding">
            <summary>Failed to convert configuration value at '{0}' to type '{1}'.</summary>
        </member>
        <member name="P:System.SR.Error_FailedToActivate">
            <summary>Failed to create instance of type '{0}'.</summary>
        </member>
        <member name="P:System.SR.Error_GeneralErrorWhenBinding">
            <summary>'{0}' was set and binding has failed. The likely cause is an invalid configuration value.</summary>
        </member>
        <member name="P:System.SR.Error_MissingConfig">
            <summary>'{0}' was set on the provided {1}, but the following properties were not found on the instance of {2}: {3}</summary>
        </member>
        <member name="P:System.SR.Error_MissingPublicInstanceConstructor">
            <summary>Cannot create instance of type '{0}' because it is missing a public instance constructor.</summary>
        </member>
        <member name="P:System.SR.Error_MultipleParameterizedConstructors">
            <summary>Cannot create instance of type '{0}' because it has multiple public parameterized constructors.</summary>
        </member>
        <member name="P:System.SR.Error_ParameterBeingBoundToIsUnnamed">
            <summary>Cannot create instance of type '{0}' because one or more parameters are unnamed.</summary>
        </member>
        <member name="P:System.SR.Error_ParameterHasNoMatchingConfig">
            <summary>Cannot create instance of type '{0}' because parameter '{1}' has no matching config. Each parameter in the constructor that does not have a default value must have a corresponding config entry.</summary>
        </member>
        <member name="P:System.SR.Error_UnsupportedMultidimensionalArray">
            <summary>Cannot create instance of type '{0}' because multidimensional arrays are not supported.</summary>
        </member>
    </members>
</doc>
