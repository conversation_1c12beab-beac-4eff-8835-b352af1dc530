﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="BouncyCastle.Cryptography" version="2.6.1" targetFramework="net481" />
  <package id="EntityFramework" version="6.5.1" targetFramework="net481" />
  <package id="Google.Protobuf" version="3.31.0" targetFramework="net481" />
  <package id="K4os.Compression.LZ4" version="1.3.8" targetFramework="net481" />
  <package id="K4os.Compression.LZ4.Streams" version="1.3.8" targetFramework="net481" />
  <package id="K4os.Hash.xxHash" version="1.0.8" targetFramework="net481" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="9.0.5" targetFramework="net481" />
  <package id="Microsoft.Extensions.Configuration" version="9.0.5" targetFramework="net481" />
  <package id="Microsoft.Extensions.Configuration.Abstractions" version="9.0.5" targetFramework="net481" />
  <package id="Microsoft.Extensions.Configuration.Binder" version="9.0.5" targetFramework="net481" />
  <package id="Microsoft.Extensions.DependencyInjection" version="9.0.5" targetFramework="net481" />
  <package id="Microsoft.Extensions.DependencyInjection.Abstractions" version="9.0.5" targetFramework="net481" />
  <package id="Microsoft.Extensions.Logging" version="9.0.5" targetFramework="net481" />
  <package id="Microsoft.Extensions.Logging.Abstractions" version="9.0.5" targetFramework="net481" />
  <package id="Microsoft.Extensions.Options" version="9.0.5" targetFramework="net481" />
  <package id="Microsoft.Extensions.Primitives" version="9.0.5" targetFramework="net481" />
  <package id="MySql.Data" version="9.1.0" targetFramework="net481" />
  <package id="MySql.Data.EntityFramework" version="9.1.0" targetFramework="net481" />
  <package id="Portable.BouncyCastle" version="1.9.0" targetFramework="net481" />
  <package id="SSH.NET" version="2025.0.0" targetFramework="net481" />
  <package id="System.Buffers" version="4.6.1" targetFramework="net481" />
  <package id="System.Collections.Immutable" version="9.0.5" targetFramework="net481" />
  <package id="System.ComponentModel.Annotations" version="5.0.0" targetFramework="net481" />
  <package id="System.Configuration.ConfigurationManager" version="9.0.5" targetFramework="net481" />
  <package id="System.Data.SqlClient" version="4.9.0" targetFramework="net481" />
  <package id="System.Diagnostics.DiagnosticSource" version="9.0.5" targetFramework="net481" />
  <package id="System.Formats.Asn1" version="9.0.5" targetFramework="net481" />
  <package id="System.IO.Pipelines" version="9.0.5" targetFramework="net481" />
  <package id="System.Memory" version="4.6.3" targetFramework="net481" />
  <package id="System.Numerics.Vectors" version="4.6.1" targetFramework="net481" />
  <package id="System.Runtime" version="4.3.1" targetFramework="net481" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="6.1.2" targetFramework="net481" />
  <package id="System.Security.AccessControl" version="6.0.0" targetFramework="net481" />
  <package id="System.Security.Permissions" version="9.0.5" targetFramework="net481" />
  <package id="System.Security.Principal.Windows" version="5.0.0" targetFramework="net481" />
  <package id="System.Threading.Tasks.Extensions" version="4.6.3" targetFramework="net481" />
  <package id="System.ValueTuple" version="4.6.1" targetFramework="net481" />
  <package id="ZstdSharp.Port" version="0.8.5" targetFramework="net481" />
</packages>