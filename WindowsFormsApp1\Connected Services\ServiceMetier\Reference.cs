﻿//------------------------------------------------------------------------------
// <auto-generated>
//     Ce code a été généré par un outil.
//     Version du runtime :4.0.30319.42000
//
//     Les modifications apportées à ce fichier peuvent provoquer un comportement incorrect et seront perdues si
//     le code est régénéré.
// </auto-generated>
//------------------------------------------------------------------------------

namespace WindowsFormsApp1.ServiceMetier {
    using System.Runtime.Serialization;
    using System;
    
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="CompositeType", Namespace="http://schemas.datacontract.org/2004/07/MetierRvMedical")]
    [System.SerializableAttribute()]
    public partial class CompositeType : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private bool BoolValueField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string StringValueField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool BoolValue {
            get {
                return this.BoolValueField;
            }
            set {
                if ((this.BoolValueField.Equals(value) != true)) {
                    this.BoolValueField = value;
                    this.RaisePropertyChanged("BoolValue");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string StringValue {
            get {
                return this.StringValueField;
            }
            set {
                if ((object.ReferenceEquals(this.StringValueField, value) != true)) {
                    this.StringValueField = value;
                    this.RaisePropertyChanged("StringValue");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Personne", Namespace="http://schemas.datacontract.org/2004/07/MetierRvMedical.Model")]
    [System.SerializableAttribute()]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(WindowsFormsApp1.ServiceMetier.Utilisateur))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(WindowsFormsApp1.ServiceMetier.Medecin))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(WindowsFormsApp1.ServiceMetier.Patient))]
    public partial class Personne : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string AdresseField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string EmailField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private int IdUField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string NomPrenomField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string TELField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Adresse {
            get {
                return this.AdresseField;
            }
            set {
                if ((object.ReferenceEquals(this.AdresseField, value) != true)) {
                    this.AdresseField = value;
                    this.RaisePropertyChanged("Adresse");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Email {
            get {
                return this.EmailField;
            }
            set {
                if ((object.ReferenceEquals(this.EmailField, value) != true)) {
                    this.EmailField = value;
                    this.RaisePropertyChanged("Email");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int IdU {
            get {
                return this.IdUField;
            }
            set {
                if ((this.IdUField.Equals(value) != true)) {
                    this.IdUField = value;
                    this.RaisePropertyChanged("IdU");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string NomPrenom {
            get {
                return this.NomPrenomField;
            }
            set {
                if ((object.ReferenceEquals(this.NomPrenomField, value) != true)) {
                    this.NomPrenomField = value;
                    this.RaisePropertyChanged("NomPrenom");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string TEL {
            get {
                return this.TELField;
            }
            set {
                if ((object.ReferenceEquals(this.TELField, value) != true)) {
                    this.TELField = value;
                    this.RaisePropertyChanged("TEL");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Utilisateur", Namespace="http://schemas.datacontract.org/2004/07/MetierRvMedical.Model")]
    [System.SerializableAttribute()]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(WindowsFormsApp1.ServiceMetier.Medecin))]
    public partial class Utilisateur : WindowsFormsApp1.ServiceMetier.Personne {
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private System.Nullable<int> IdRoleField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string MotDePasseField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private WindowsFormsApp1.ServiceMetier.Role RoleField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string identifiantField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private System.Nullable<bool> statutField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> IdRole {
            get {
                return this.IdRoleField;
            }
            set {
                if ((this.IdRoleField.Equals(value) != true)) {
                    this.IdRoleField = value;
                    this.RaisePropertyChanged("IdRole");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string MotDePasse {
            get {
                return this.MotDePasseField;
            }
            set {
                if ((object.ReferenceEquals(this.MotDePasseField, value) != true)) {
                    this.MotDePasseField = value;
                    this.RaisePropertyChanged("MotDePasse");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public WindowsFormsApp1.ServiceMetier.Role Role {
            get {
                return this.RoleField;
            }
            set {
                if ((object.ReferenceEquals(this.RoleField, value) != true)) {
                    this.RoleField = value;
                    this.RaisePropertyChanged("Role");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string identifiant {
            get {
                return this.identifiantField;
            }
            set {
                if ((object.ReferenceEquals(this.identifiantField, value) != true)) {
                    this.identifiantField = value;
                    this.RaisePropertyChanged("identifiant");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<bool> statut {
            get {
                return this.statutField;
            }
            set {
                if ((this.statutField.Equals(value) != true)) {
                    this.statutField = value;
                    this.RaisePropertyChanged("statut");
                }
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Medecin", Namespace="http://schemas.datacontract.org/2004/07/MetierRvMedical.Model")]
    [System.SerializableAttribute()]
    public partial class Medecin : WindowsFormsApp1.ServiceMetier.Utilisateur {
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private WindowsFormsApp1.ServiceMetier.Agenda[] AgendasField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private System.Nullable<int> IdSpecialiteField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string NumeroOrdreField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private WindowsFormsApp1.ServiceMetier.Specialite SpecialiteField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public WindowsFormsApp1.ServiceMetier.Agenda[] Agendas {
            get {
                return this.AgendasField;
            }
            set {
                if ((object.ReferenceEquals(this.AgendasField, value) != true)) {
                    this.AgendasField = value;
                    this.RaisePropertyChanged("Agendas");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> IdSpecialite {
            get {
                return this.IdSpecialiteField;
            }
            set {
                if ((this.IdSpecialiteField.Equals(value) != true)) {
                    this.IdSpecialiteField = value;
                    this.RaisePropertyChanged("IdSpecialite");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string NumeroOrdre {
            get {
                return this.NumeroOrdreField;
            }
            set {
                if ((object.ReferenceEquals(this.NumeroOrdreField, value) != true)) {
                    this.NumeroOrdreField = value;
                    this.RaisePropertyChanged("NumeroOrdre");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public WindowsFormsApp1.ServiceMetier.Specialite Specialite {
            get {
                return this.SpecialiteField;
            }
            set {
                if ((object.ReferenceEquals(this.SpecialiteField, value) != true)) {
                    this.SpecialiteField = value;
                    this.RaisePropertyChanged("Specialite");
                }
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Patient", Namespace="http://schemas.datacontract.org/2004/07/MetierRvMedical.Model")]
    [System.SerializableAttribute()]
    public partial class Patient : WindowsFormsApp1.ServiceMetier.Personne {
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private System.Nullable<System.DateTime> DateNaissanceField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private WindowsFormsApp1.ServiceMetier.GroupeSanguin GroupeSanguinField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private int IdGroupeSanguinField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private System.Nullable<float> PoidsField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private System.Nullable<float> TailleField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> DateNaissance {
            get {
                return this.DateNaissanceField;
            }
            set {
                if ((this.DateNaissanceField.Equals(value) != true)) {
                    this.DateNaissanceField = value;
                    this.RaisePropertyChanged("DateNaissance");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public WindowsFormsApp1.ServiceMetier.GroupeSanguin GroupeSanguin {
            get {
                return this.GroupeSanguinField;
            }
            set {
                if ((object.ReferenceEquals(this.GroupeSanguinField, value) != true)) {
                    this.GroupeSanguinField = value;
                    this.RaisePropertyChanged("GroupeSanguin");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int IdGroupeSanguin {
            get {
                return this.IdGroupeSanguinField;
            }
            set {
                if ((this.IdGroupeSanguinField.Equals(value) != true)) {
                    this.IdGroupeSanguinField = value;
                    this.RaisePropertyChanged("IdGroupeSanguin");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<float> Poids {
            get {
                return this.PoidsField;
            }
            set {
                if ((this.PoidsField.Equals(value) != true)) {
                    this.PoidsField = value;
                    this.RaisePropertyChanged("Poids");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<float> Taille {
            get {
                return this.TailleField;
            }
            set {
                if ((this.TailleField.Equals(value) != true)) {
                    this.TailleField = value;
                    this.RaisePropertyChanged("Taille");
                }
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="GroupeSanguin", Namespace="http://schemas.datacontract.org/2004/07/MetierRvMedical.Model")]
    [System.SerializableAttribute()]
    public partial class GroupeSanguin : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string CodeGroupeSanguinField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private int IdGroupeSanguinField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string NomGroupeSanguinField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string CodeGroupeSanguin {
            get {
                return this.CodeGroupeSanguinField;
            }
            set {
                if ((object.ReferenceEquals(this.CodeGroupeSanguinField, value) != true)) {
                    this.CodeGroupeSanguinField = value;
                    this.RaisePropertyChanged("CodeGroupeSanguin");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int IdGroupeSanguin {
            get {
                return this.IdGroupeSanguinField;
            }
            set {
                if ((this.IdGroupeSanguinField.Equals(value) != true)) {
                    this.IdGroupeSanguinField = value;
                    this.RaisePropertyChanged("IdGroupeSanguin");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string NomGroupeSanguin {
            get {
                return this.NomGroupeSanguinField;
            }
            set {
                if ((object.ReferenceEquals(this.NomGroupeSanguinField, value) != true)) {
                    this.NomGroupeSanguinField = value;
                    this.RaisePropertyChanged("NomGroupeSanguin");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Role", Namespace="http://schemas.datacontract.org/2004/07/MetierRvMedical.Model")]
    [System.SerializableAttribute()]
    public partial class Role : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string CodeField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string DescriptionField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private int IdField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Code {
            get {
                return this.CodeField;
            }
            set {
                if ((object.ReferenceEquals(this.CodeField, value) != true)) {
                    this.CodeField = value;
                    this.RaisePropertyChanged("Code");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Description {
            get {
                return this.DescriptionField;
            }
            set {
                if ((object.ReferenceEquals(this.DescriptionField, value) != true)) {
                    this.DescriptionField = value;
                    this.RaisePropertyChanged("Description");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Id {
            get {
                return this.IdField;
            }
            set {
                if ((this.IdField.Equals(value) != true)) {
                    this.IdField = value;
                    this.RaisePropertyChanged("Id");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Specialite", Namespace="http://schemas.datacontract.org/2004/07/MetierRvMedical.Model")]
    [System.SerializableAttribute()]
    public partial class Specialite : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string CodeSpecialiteField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private int IdField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string NomSpecialiteField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string CodeSpecialite {
            get {
                return this.CodeSpecialiteField;
            }
            set {
                if ((object.ReferenceEquals(this.CodeSpecialiteField, value) != true)) {
                    this.CodeSpecialiteField = value;
                    this.RaisePropertyChanged("CodeSpecialite");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Id {
            get {
                return this.IdField;
            }
            set {
                if ((this.IdField.Equals(value) != true)) {
                    this.IdField = value;
                    this.RaisePropertyChanged("Id");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string NomSpecialite {
            get {
                return this.NomSpecialiteField;
            }
            set {
                if ((object.ReferenceEquals(this.NomSpecialiteField, value) != true)) {
                    this.NomSpecialiteField = value;
                    this.RaisePropertyChanged("NomSpecialite");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Agenda", Namespace="http://schemas.datacontract.org/2004/07/MetierRvMedical.Model")]
    [System.SerializableAttribute()]
    public partial class Agenda : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private int CreneauField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private WindowsFormsApp1.ServiceMetier.Creneau[] CreneauxField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private System.DateTime DatePlanifieField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string HeureDebutField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string HeureFinField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private int IdAgendaField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private int IdMedecinField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string LieuField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private WindowsFormsApp1.ServiceMetier.Medecin MedecinField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private WindowsFormsApp1.ServiceMetier.RendezVous[] RendezVousField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string TitreField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string statutField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Creneau {
            get {
                return this.CreneauField;
            }
            set {
                if ((this.CreneauField.Equals(value) != true)) {
                    this.CreneauField = value;
                    this.RaisePropertyChanged("Creneau");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public WindowsFormsApp1.ServiceMetier.Creneau[] Creneaux {
            get {
                return this.CreneauxField;
            }
            set {
                if ((object.ReferenceEquals(this.CreneauxField, value) != true)) {
                    this.CreneauxField = value;
                    this.RaisePropertyChanged("Creneaux");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime DatePlanifie {
            get {
                return this.DatePlanifieField;
            }
            set {
                if ((this.DatePlanifieField.Equals(value) != true)) {
                    this.DatePlanifieField = value;
                    this.RaisePropertyChanged("DatePlanifie");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string HeureDebut {
            get {
                return this.HeureDebutField;
            }
            set {
                if ((object.ReferenceEquals(this.HeureDebutField, value) != true)) {
                    this.HeureDebutField = value;
                    this.RaisePropertyChanged("HeureDebut");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string HeureFin {
            get {
                return this.HeureFinField;
            }
            set {
                if ((object.ReferenceEquals(this.HeureFinField, value) != true)) {
                    this.HeureFinField = value;
                    this.RaisePropertyChanged("HeureFin");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int IdAgenda {
            get {
                return this.IdAgendaField;
            }
            set {
                if ((this.IdAgendaField.Equals(value) != true)) {
                    this.IdAgendaField = value;
                    this.RaisePropertyChanged("IdAgenda");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int IdMedecin {
            get {
                return this.IdMedecinField;
            }
            set {
                if ((this.IdMedecinField.Equals(value) != true)) {
                    this.IdMedecinField = value;
                    this.RaisePropertyChanged("IdMedecin");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Lieu {
            get {
                return this.LieuField;
            }
            set {
                if ((object.ReferenceEquals(this.LieuField, value) != true)) {
                    this.LieuField = value;
                    this.RaisePropertyChanged("Lieu");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public WindowsFormsApp1.ServiceMetier.Medecin Medecin {
            get {
                return this.MedecinField;
            }
            set {
                if ((object.ReferenceEquals(this.MedecinField, value) != true)) {
                    this.MedecinField = value;
                    this.RaisePropertyChanged("Medecin");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public WindowsFormsApp1.ServiceMetier.RendezVous[] RendezVous {
            get {
                return this.RendezVousField;
            }
            set {
                if ((object.ReferenceEquals(this.RendezVousField, value) != true)) {
                    this.RendezVousField = value;
                    this.RaisePropertyChanged("RendezVous");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Titre {
            get {
                return this.TitreField;
            }
            set {
                if ((object.ReferenceEquals(this.TitreField, value) != true)) {
                    this.TitreField = value;
                    this.RaisePropertyChanged("Titre");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string statut {
            get {
                return this.statutField;
            }
            set {
                if ((object.ReferenceEquals(this.statutField, value) != true)) {
                    this.statutField = value;
                    this.RaisePropertyChanged("statut");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Creneau", Namespace="http://schemas.datacontract.org/2004/07/MetierRvMedical.Model")]
    [System.SerializableAttribute()]
    public partial class Creneau : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private WindowsFormsApp1.ServiceMetier.Agenda AgendaField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private System.DateTime DateField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private bool DisponibleField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string HeureDebutField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string HeureFinField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private int IdAgendaField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private int IdCreneauField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public WindowsFormsApp1.ServiceMetier.Agenda Agenda {
            get {
                return this.AgendaField;
            }
            set {
                if ((object.ReferenceEquals(this.AgendaField, value) != true)) {
                    this.AgendaField = value;
                    this.RaisePropertyChanged("Agenda");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime Date {
            get {
                return this.DateField;
            }
            set {
                if ((this.DateField.Equals(value) != true)) {
                    this.DateField = value;
                    this.RaisePropertyChanged("Date");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool Disponible {
            get {
                return this.DisponibleField;
            }
            set {
                if ((this.DisponibleField.Equals(value) != true)) {
                    this.DisponibleField = value;
                    this.RaisePropertyChanged("Disponible");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string HeureDebut {
            get {
                return this.HeureDebutField;
            }
            set {
                if ((object.ReferenceEquals(this.HeureDebutField, value) != true)) {
                    this.HeureDebutField = value;
                    this.RaisePropertyChanged("HeureDebut");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string HeureFin {
            get {
                return this.HeureFinField;
            }
            set {
                if ((object.ReferenceEquals(this.HeureFinField, value) != true)) {
                    this.HeureFinField = value;
                    this.RaisePropertyChanged("HeureFin");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int IdAgenda {
            get {
                return this.IdAgendaField;
            }
            set {
                if ((this.IdAgendaField.Equals(value) != true)) {
                    this.IdAgendaField = value;
                    this.RaisePropertyChanged("IdAgenda");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int IdCreneau {
            get {
                return this.IdCreneauField;
            }
            set {
                if ((this.IdCreneauField.Equals(value) != true)) {
                    this.IdCreneauField = value;
                    this.RaisePropertyChanged("IdCreneau");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="RendezVous", Namespace="http://schemas.datacontract.org/2004/07/MetierRvMedical.Model")]
    [System.SerializableAttribute()]
    public partial class RendezVous : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private WindowsFormsApp1.ServiceMetier.Creneau CreneauField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private System.DateTime DateRvField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private System.Nullable<int> IdCreneauField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private System.Nullable<int> IdMedecinField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private System.Nullable<int> IdPatientField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private int IdRvField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private System.Nullable<int> IdSoinField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private WindowsFormsApp1.ServiceMetier.Medecin MedecinField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private WindowsFormsApp1.ServiceMetier.Patient PatientField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private WindowsFormsApp1.ServiceMetier.Soin SoinField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string StatutField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public WindowsFormsApp1.ServiceMetier.Creneau Creneau {
            get {
                return this.CreneauField;
            }
            set {
                if ((object.ReferenceEquals(this.CreneauField, value) != true)) {
                    this.CreneauField = value;
                    this.RaisePropertyChanged("Creneau");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime DateRv {
            get {
                return this.DateRvField;
            }
            set {
                if ((this.DateRvField.Equals(value) != true)) {
                    this.DateRvField = value;
                    this.RaisePropertyChanged("DateRv");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> IdCreneau {
            get {
                return this.IdCreneauField;
            }
            set {
                if ((this.IdCreneauField.Equals(value) != true)) {
                    this.IdCreneauField = value;
                    this.RaisePropertyChanged("IdCreneau");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> IdMedecin {
            get {
                return this.IdMedecinField;
            }
            set {
                if ((this.IdMedecinField.Equals(value) != true)) {
                    this.IdMedecinField = value;
                    this.RaisePropertyChanged("IdMedecin");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> IdPatient {
            get {
                return this.IdPatientField;
            }
            set {
                if ((this.IdPatientField.Equals(value) != true)) {
                    this.IdPatientField = value;
                    this.RaisePropertyChanged("IdPatient");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int IdRv {
            get {
                return this.IdRvField;
            }
            set {
                if ((this.IdRvField.Equals(value) != true)) {
                    this.IdRvField = value;
                    this.RaisePropertyChanged("IdRv");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> IdSoin {
            get {
                return this.IdSoinField;
            }
            set {
                if ((this.IdSoinField.Equals(value) != true)) {
                    this.IdSoinField = value;
                    this.RaisePropertyChanged("IdSoin");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public WindowsFormsApp1.ServiceMetier.Medecin Medecin {
            get {
                return this.MedecinField;
            }
            set {
                if ((object.ReferenceEquals(this.MedecinField, value) != true)) {
                    this.MedecinField = value;
                    this.RaisePropertyChanged("Medecin");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public WindowsFormsApp1.ServiceMetier.Patient Patient {
            get {
                return this.PatientField;
            }
            set {
                if ((object.ReferenceEquals(this.PatientField, value) != true)) {
                    this.PatientField = value;
                    this.RaisePropertyChanged("Patient");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public WindowsFormsApp1.ServiceMetier.Soin Soin {
            get {
                return this.SoinField;
            }
            set {
                if ((object.ReferenceEquals(this.SoinField, value) != true)) {
                    this.SoinField = value;
                    this.RaisePropertyChanged("Soin");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Statut {
            get {
                return this.StatutField;
            }
            set {
                if ((object.ReferenceEquals(this.StatutField, value) != true)) {
                    this.StatutField = value;
                    this.RaisePropertyChanged("Statut");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Soin", Namespace="http://schemas.datacontract.org/2004/07/MetierRvMedical.Model")]
    [System.SerializableAttribute()]
    public partial class Soin : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string CategoryField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string DurationField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private int IdSoinField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string NameSoinField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private int PriceField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Category {
            get {
                return this.CategoryField;
            }
            set {
                if ((object.ReferenceEquals(this.CategoryField, value) != true)) {
                    this.CategoryField = value;
                    this.RaisePropertyChanged("Category");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Duration {
            get {
                return this.DurationField;
            }
            set {
                if ((object.ReferenceEquals(this.DurationField, value) != true)) {
                    this.DurationField = value;
                    this.RaisePropertyChanged("Duration");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int IdSoin {
            get {
                return this.IdSoinField;
            }
            set {
                if ((this.IdSoinField.Equals(value) != true)) {
                    this.IdSoinField = value;
                    this.RaisePropertyChanged("IdSoin");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string NameSoin {
            get {
                return this.NameSoinField;
            }
            set {
                if ((object.ReferenceEquals(this.NameSoinField, value) != true)) {
                    this.NameSoinField = value;
                    this.RaisePropertyChanged("NameSoin");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Price {
            get {
                return this.PriceField;
            }
            set {
                if ((this.PriceField.Equals(value) != true)) {
                    this.PriceField = value;
                    this.RaisePropertyChanged("Price");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ServiceModel.ServiceContractAttribute(ConfigurationName="ServiceMetier.IService1")]
    public interface IService1 {
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService1/GetData", ReplyAction="http://tempuri.org/IService1/GetDataResponse")]
        string GetData(int value);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService1/GetData", ReplyAction="http://tempuri.org/IService1/GetDataResponse")]
        System.Threading.Tasks.Task<string> GetDataAsync(int value);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService1/GetDataUsingDataContract", ReplyAction="http://tempuri.org/IService1/GetDataUsingDataContractResponse")]
        WindowsFormsApp1.ServiceMetier.CompositeType GetDataUsingDataContract(WindowsFormsApp1.ServiceMetier.CompositeType composite);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService1/GetDataUsingDataContract", ReplyAction="http://tempuri.org/IService1/GetDataUsingDataContractResponse")]
        System.Threading.Tasks.Task<WindowsFormsApp1.ServiceMetier.CompositeType> GetDataUsingDataContractAsync(WindowsFormsApp1.ServiceMetier.CompositeType composite);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService1/UpdatePatient", ReplyAction="http://tempuri.org/IService1/UpdatePatientResponse")]
        bool UpdatePatient(WindowsFormsApp1.ServiceMetier.Patient patient);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService1/UpdatePatient", ReplyAction="http://tempuri.org/IService1/UpdatePatientResponse")]
        System.Threading.Tasks.Task<bool> UpdatePatientAsync(WindowsFormsApp1.ServiceMetier.Patient patient);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService1/RemovePatient", ReplyAction="http://tempuri.org/IService1/RemovePatientResponse")]
        bool RemovePatient(WindowsFormsApp1.ServiceMetier.Patient patient);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService1/RemovePatient", ReplyAction="http://tempuri.org/IService1/RemovePatientResponse")]
        System.Threading.Tasks.Task<bool> RemovePatientAsync(WindowsFormsApp1.ServiceMetier.Patient patient);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService1/AddPatient", ReplyAction="http://tempuri.org/IService1/AddPatientResponse")]
        bool AddPatient(WindowsFormsApp1.ServiceMetier.Patient patient);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService1/AddPatient", ReplyAction="http://tempuri.org/IService1/AddPatientResponse")]
        System.Threading.Tasks.Task<bool> AddPatientAsync(WindowsFormsApp1.ServiceMetier.Patient patient);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService1/GetListeAgendas", ReplyAction="http://tempuri.org/IService1/GetListeAgendasResponse")]
        WindowsFormsApp1.ServiceMetier.Agenda[] GetListeAgendas();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService1/GetListeAgendas", ReplyAction="http://tempuri.org/IService1/GetListeAgendasResponse")]
        System.Threading.Tasks.Task<WindowsFormsApp1.ServiceMetier.Agenda[]> GetListeAgendasAsync();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService1/GetListeGroupesSanguins", ReplyAction="http://tempuri.org/IService1/GetListeGroupesSanguinsResponse")]
        WindowsFormsApp1.ServiceMetier.GroupeSanguin[] GetListeGroupesSanguins();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IService1/GetListeGroupesSanguins", ReplyAction="http://tempuri.org/IService1/GetListeGroupesSanguinsResponse")]
        System.Threading.Tasks.Task<WindowsFormsApp1.ServiceMetier.GroupeSanguin[]> GetListeGroupesSanguinsAsync();
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    public interface IService1Channel : WindowsFormsApp1.ServiceMetier.IService1, System.ServiceModel.IClientChannel {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    public partial class Service1Client : System.ServiceModel.ClientBase<WindowsFormsApp1.ServiceMetier.IService1>, WindowsFormsApp1.ServiceMetier.IService1 {
        
        public Service1Client() {
        }
        
        public Service1Client(string endpointConfigurationName) : 
                base(endpointConfigurationName) {
        }
        
        public Service1Client(string endpointConfigurationName, string remoteAddress) : 
                base(endpointConfigurationName, remoteAddress) {
        }
        
        public Service1Client(string endpointConfigurationName, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(endpointConfigurationName, remoteAddress) {
        }
        
        public Service1Client(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(binding, remoteAddress) {
        }
        
        public string GetData(int value) {
            return base.Channel.GetData(value);
        }
        
        public System.Threading.Tasks.Task<string> GetDataAsync(int value) {
            return base.Channel.GetDataAsync(value);
        }
        
        public WindowsFormsApp1.ServiceMetier.CompositeType GetDataUsingDataContract(WindowsFormsApp1.ServiceMetier.CompositeType composite) {
            return base.Channel.GetDataUsingDataContract(composite);
        }
        
        public System.Threading.Tasks.Task<WindowsFormsApp1.ServiceMetier.CompositeType> GetDataUsingDataContractAsync(WindowsFormsApp1.ServiceMetier.CompositeType composite) {
            return base.Channel.GetDataUsingDataContractAsync(composite);
        }
        
        public bool UpdatePatient(WindowsFormsApp1.ServiceMetier.Patient patient) {
            return base.Channel.UpdatePatient(patient);
        }
        
        public System.Threading.Tasks.Task<bool> UpdatePatientAsync(WindowsFormsApp1.ServiceMetier.Patient patient) {
            return base.Channel.UpdatePatientAsync(patient);
        }
        
        public bool RemovePatient(WindowsFormsApp1.ServiceMetier.Patient patient) {
            return base.Channel.RemovePatient(patient);
        }
        
        public System.Threading.Tasks.Task<bool> RemovePatientAsync(WindowsFormsApp1.ServiceMetier.Patient patient) {
            return base.Channel.RemovePatientAsync(patient);
        }
        
        public bool AddPatient(WindowsFormsApp1.ServiceMetier.Patient patient) {
            return base.Channel.AddPatient(patient);
        }
        
        public System.Threading.Tasks.Task<bool> AddPatientAsync(WindowsFormsApp1.ServiceMetier.Patient patient) {
            return base.Channel.AddPatientAsync(patient);
        }
        
        public WindowsFormsApp1.ServiceMetier.Agenda[] GetListeAgendas() {
            return base.Channel.GetListeAgendas();
        }
        
        public System.Threading.Tasks.Task<WindowsFormsApp1.ServiceMetier.Agenda[]> GetListeAgendasAsync() {
            return base.Channel.GetListeAgendasAsync();
        }
        
        public WindowsFormsApp1.ServiceMetier.GroupeSanguin[] GetListeGroupesSanguins() {
            return base.Channel.GetListeGroupesSanguins();
        }
        
        public System.Threading.Tasks.Task<WindowsFormsApp1.ServiceMetier.GroupeSanguin[]> GetListeGroupesSanguinsAsync() {
            return base.Channel.GetListeGroupesSanguinsAsync();
        }
    }
}
