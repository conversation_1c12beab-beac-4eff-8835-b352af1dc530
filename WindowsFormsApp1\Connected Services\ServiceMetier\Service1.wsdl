<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:wsam="http://www.w3.org/2007/05/addressing/metadata" xmlns:wsx="http://schemas.xmlsoap.org/ws/2004/09/mex" xmlns:wsap="http://schemas.xmlsoap.org/ws/2004/08/addressing/policy" xmlns:msc="http://schemas.microsoft.com/ws/2005/12/wsdl/contract" xmlns:wsp="http://schemas.xmlsoap.org/ws/2004/09/policy" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:tns="http://tempuri.org/" xmlns:wsa10="http://www.w3.org/2005/08/addressing" xmlns:wsaw="http://www.w3.org/2006/05/addressing/wsdl" xmlns:wsa="http://schemas.xmlsoap.org/ws/2004/08/addressing" name="Service1" targetNamespace="http://tempuri.org/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types>
    <xsd:schema targetNamespace="http://tempuri.org/Imports">
      <xsd:import schemaLocation="http://localhost:60827/Service1.svc?xsd=xsd0" namespace="http://tempuri.org/" />
      <xsd:import schemaLocation="http://localhost:60827/Service1.svc?xsd=xsd1" namespace="http://schemas.microsoft.com/2003/10/Serialization/" />
      <xsd:import schemaLocation="http://localhost:60827/Service1.svc?xsd=xsd2" namespace="http://schemas.datacontract.org/2004/07/MetierRvMedical" />
      <xsd:import schemaLocation="http://localhost:60827/Service1.svc?xsd=xsd3" namespace="http://schemas.datacontract.org/2004/07/MetierRvMedical.Model" />
    </xsd:schema>
  </wsdl:types>
  <wsdl:message name="IService1_GetData_InputMessage">
    <wsdl:part name="parameters" element="tns:GetData" />
  </wsdl:message>
  <wsdl:message name="IService1_GetData_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetDataResponse" />
  </wsdl:message>
  <wsdl:message name="IService1_GetDataUsingDataContract_InputMessage">
    <wsdl:part name="parameters" element="tns:GetDataUsingDataContract" />
  </wsdl:message>
  <wsdl:message name="IService1_GetDataUsingDataContract_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetDataUsingDataContractResponse" />
  </wsdl:message>
  <wsdl:message name="IService1_UpdatePatient_InputMessage">
    <wsdl:part name="parameters" element="tns:UpdatePatient" />
  </wsdl:message>
  <wsdl:message name="IService1_UpdatePatient_OutputMessage">
    <wsdl:part name="parameters" element="tns:UpdatePatientResponse" />
  </wsdl:message>
  <wsdl:message name="IService1_RemovePatient_InputMessage">
    <wsdl:part name="parameters" element="tns:RemovePatient" />
  </wsdl:message>
  <wsdl:message name="IService1_RemovePatient_OutputMessage">
    <wsdl:part name="parameters" element="tns:RemovePatientResponse" />
  </wsdl:message>
  <wsdl:message name="IService1_AddPatient_InputMessage">
    <wsdl:part name="parameters" element="tns:AddPatient" />
  </wsdl:message>
  <wsdl:message name="IService1_AddPatient_OutputMessage">
    <wsdl:part name="parameters" element="tns:AddPatientResponse" />
  </wsdl:message>
  <wsdl:message name="IService1_GetListeAgendas_InputMessage">
    <wsdl:part name="parameters" element="tns:GetListeAgendas" />
  </wsdl:message>
  <wsdl:message name="IService1_GetListeAgendas_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetListeAgendasResponse" />
  </wsdl:message>
  <wsdl:message name="IService1_GetListeGroupesSanguins_InputMessage">
    <wsdl:part name="parameters" element="tns:GetListeGroupesSanguins" />
  </wsdl:message>
  <wsdl:message name="IService1_GetListeGroupesSanguins_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetListeGroupesSanguinsResponse" />
  </wsdl:message>
  <wsdl:portType name="IService1">
    <wsdl:operation name="GetData">
      <wsdl:input wsaw:Action="http://tempuri.org/IService1/GetData" message="tns:IService1_GetData_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IService1/GetDataResponse" message="tns:IService1_GetData_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetDataUsingDataContract">
      <wsdl:input wsaw:Action="http://tempuri.org/IService1/GetDataUsingDataContract" message="tns:IService1_GetDataUsingDataContract_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IService1/GetDataUsingDataContractResponse" message="tns:IService1_GetDataUsingDataContract_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="UpdatePatient">
      <wsdl:input wsaw:Action="http://tempuri.org/IService1/UpdatePatient" message="tns:IService1_UpdatePatient_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IService1/UpdatePatientResponse" message="tns:IService1_UpdatePatient_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="RemovePatient">
      <wsdl:input wsaw:Action="http://tempuri.org/IService1/RemovePatient" message="tns:IService1_RemovePatient_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IService1/RemovePatientResponse" message="tns:IService1_RemovePatient_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="AddPatient">
      <wsdl:input wsaw:Action="http://tempuri.org/IService1/AddPatient" message="tns:IService1_AddPatient_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IService1/AddPatientResponse" message="tns:IService1_AddPatient_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetListeAgendas">
      <wsdl:input wsaw:Action="http://tempuri.org/IService1/GetListeAgendas" message="tns:IService1_GetListeAgendas_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IService1/GetListeAgendasResponse" message="tns:IService1_GetListeAgendas_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetListeGroupesSanguins">
      <wsdl:input wsaw:Action="http://tempuri.org/IService1/GetListeGroupesSanguins" message="tns:IService1_GetListeGroupesSanguins_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IService1/GetListeGroupesSanguinsResponse" message="tns:IService1_GetListeGroupesSanguins_OutputMessage" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="BasicHttpBinding_IService1" type="tns:IService1">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="GetData">
      <soap:operation soapAction="http://tempuri.org/IService1/GetData" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetDataUsingDataContract">
      <soap:operation soapAction="http://tempuri.org/IService1/GetDataUsingDataContract" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpdatePatient">
      <soap:operation soapAction="http://tempuri.org/IService1/UpdatePatient" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="RemovePatient">
      <soap:operation soapAction="http://tempuri.org/IService1/RemovePatient" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AddPatient">
      <soap:operation soapAction="http://tempuri.org/IService1/AddPatient" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetListeAgendas">
      <soap:operation soapAction="http://tempuri.org/IService1/GetListeAgendas" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetListeGroupesSanguins">
      <soap:operation soapAction="http://tempuri.org/IService1/GetListeGroupesSanguins" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="Service1">
    <wsdl:port name="BasicHttpBinding_IService1" binding="tns:BasicHttpBinding_IService1">
      <soap:address location="http://localhost:60827/Service1.svc" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>