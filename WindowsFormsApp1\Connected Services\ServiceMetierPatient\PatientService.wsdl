<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:wsam="http://www.w3.org/2007/05/addressing/metadata" xmlns:wsx="http://schemas.xmlsoap.org/ws/2004/09/mex" xmlns:wsap="http://schemas.xmlsoap.org/ws/2004/08/addressing/policy" xmlns:msc="http://schemas.microsoft.com/ws/2005/12/wsdl/contract" xmlns:wsp="http://schemas.xmlsoap.org/ws/2004/09/policy" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:tns="http://tempuri.org/" xmlns:wsa10="http://www.w3.org/2005/08/addressing" xmlns:wsaw="http://www.w3.org/2006/05/addressing/wsdl" xmlns:wsa="http://schemas.xmlsoap.org/ws/2004/08/addressing" name="PatientService" targetNamespace="http://tempuri.org/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types>
    <xsd:schema targetNamespace="http://tempuri.org/Imports">
      <xsd:import schemaLocation="http://localhost:60827/Wcf/PatientService.svc?xsd=xsd0" namespace="http://tempuri.org/" />
      <xsd:import schemaLocation="http://localhost:60827/Wcf/PatientService.svc?xsd=xsd1" namespace="http://schemas.microsoft.com/2003/10/Serialization/" />
      <xsd:import schemaLocation="http://localhost:60827/Wcf/PatientService.svc?xsd=xsd2" namespace="http://schemas.datacontract.org/2004/07/MetierRvMedical.Model" />
    </xsd:schema>
  </wsdl:types>
  <wsdl:message name="IPatientService_GetListePatients_InputMessage">
    <wsdl:part name="parameters" element="tns:GetListePatients" />
  </wsdl:message>
  <wsdl:message name="IPatientService_GetListePatients_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetListePatientsResponse" />
  </wsdl:message>
  <wsdl:message name="IPatientService_AddPatient_InputMessage">
    <wsdl:part name="parameters" element="tns:AddPatient" />
  </wsdl:message>
  <wsdl:message name="IPatientService_AddPatient_OutputMessage">
    <wsdl:part name="parameters" element="tns:AddPatientResponse" />
  </wsdl:message>
  <wsdl:message name="IPatientService_UpdatePatient_InputMessage">
    <wsdl:part name="parameters" element="tns:UpdatePatient" />
  </wsdl:message>
  <wsdl:message name="IPatientService_UpdatePatient_OutputMessage">
    <wsdl:part name="parameters" element="tns:UpdatePatientResponse" />
  </wsdl:message>
  <wsdl:message name="IPatientService_RemovePatient_InputMessage">
    <wsdl:part name="parameters" element="tns:RemovePatient" />
  </wsdl:message>
  <wsdl:message name="IPatientService_RemovePatient_OutputMessage">
    <wsdl:part name="parameters" element="tns:RemovePatientResponse" />
  </wsdl:message>
  <wsdl:message name="IPatientService_GetListeGroupesSanguins_InputMessage">
    <wsdl:part name="parameters" element="tns:GetListeGroupesSanguins" />
  </wsdl:message>
  <wsdl:message name="IPatientService_GetListeGroupesSanguins_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetListeGroupesSanguinsResponse" />
  </wsdl:message>
  <wsdl:message name="IPatientService_ResearchPatient_InputMessage">
    <wsdl:part name="parameters" element="tns:ResearchPatient" />
  </wsdl:message>
  <wsdl:message name="IPatientService_ResearchPatient_OutputMessage">
    <wsdl:part name="parameters" element="tns:ResearchPatientResponse" />
  </wsdl:message>
  <wsdl:portType name="IPatientService">
    <wsdl:operation name="GetListePatients">
      <wsdl:input wsaw:Action="http://tempuri.org/IPatientService/GetListePatients" message="tns:IPatientService_GetListePatients_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IPatientService/GetListePatientsResponse" message="tns:IPatientService_GetListePatients_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="AddPatient">
      <wsdl:input wsaw:Action="http://tempuri.org/IPatientService/AddPatient" message="tns:IPatientService_AddPatient_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IPatientService/AddPatientResponse" message="tns:IPatientService_AddPatient_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="UpdatePatient">
      <wsdl:input wsaw:Action="http://tempuri.org/IPatientService/UpdatePatient" message="tns:IPatientService_UpdatePatient_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IPatientService/UpdatePatientResponse" message="tns:IPatientService_UpdatePatient_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="RemovePatient">
      <wsdl:input wsaw:Action="http://tempuri.org/IPatientService/RemovePatient" message="tns:IPatientService_RemovePatient_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IPatientService/RemovePatientResponse" message="tns:IPatientService_RemovePatient_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetListeGroupesSanguins">
      <wsdl:input wsaw:Action="http://tempuri.org/IPatientService/GetListeGroupesSanguins" message="tns:IPatientService_GetListeGroupesSanguins_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IPatientService/GetListeGroupesSanguinsResponse" message="tns:IPatientService_GetListeGroupesSanguins_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="ResearchPatient">
      <wsdl:input wsaw:Action="http://tempuri.org/IPatientService/ResearchPatient" message="tns:IPatientService_ResearchPatient_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IPatientService/ResearchPatientResponse" message="tns:IPatientService_ResearchPatient_OutputMessage" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="BasicHttpBinding_IPatientService" type="tns:IPatientService">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="GetListePatients">
      <soap:operation soapAction="http://tempuri.org/IPatientService/GetListePatients" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="AddPatient">
      <soap:operation soapAction="http://tempuri.org/IPatientService/AddPatient" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="UpdatePatient">
      <soap:operation soapAction="http://tempuri.org/IPatientService/UpdatePatient" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="RemovePatient">
      <soap:operation soapAction="http://tempuri.org/IPatientService/RemovePatient" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetListeGroupesSanguins">
      <soap:operation soapAction="http://tempuri.org/IPatientService/GetListeGroupesSanguins" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ResearchPatient">
      <soap:operation soapAction="http://tempuri.org/IPatientService/ResearchPatient" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="PatientService">
    <wsdl:port name="BasicHttpBinding_IPatientService" binding="tns:BasicHttpBinding_IPatientService">
      <soap:address location="http://localhost:60827/Wcf/PatientService.svc" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>