﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>System.Security.Principal.Windows</name>
  </assembly>
  <members>
    <member name="T:Microsoft.Win32.SafeHandles.SafeAccessTokenHandle">
      <summary>[SECURITY CRITICAL] Предоставляет безопасный дескриптор для потока Windows или маркера доступа процесса.Дополнительные сведения см. в разделе Маркеры доступа.</summary>
    </member>
    <member name="M:Microsoft.Win32.SafeHandles.SafeAccessTokenHandle.#ctor(System.IntPtr)">
      <summary>[SECURITY CRITICAL] Инициализирует новый экземпляр класса <see cref="T:Microsoft.Win32.SafeHandles.SafeAccessTokenHandle" />.</summary>
      <param name="handle">Объект <see cref="T:System.IntPtr" />, представляющий ранее существующий дескриптор для использования.Используя <see cref="F:System.IntPtr.Zero" />, возвращает недопустимый дескриптор.</param>
    </member>
    <member name="P:Microsoft.Win32.SafeHandles.SafeAccessTokenHandle.InvalidHandle">
      <summary>[SECURITY CRITICAL] Возвращает недопустимый дескриптор путем создания экземпляра объекта <see cref="T:Microsoft.Win32.SafeHandles.SafeAccessTokenHandle" /> с <see cref="F:System.IntPtr.Zero" />.</summary>
      <returns>Возвращает объект <see cref="T:Microsoft.Win32.SafeHandles.SafeAccessTokenHandle" />.</returns>
    </member>
    <member name="P:Microsoft.Win32.SafeHandles.SafeAccessTokenHandle.IsInvalid">
      <summary>[SECURITY CRITICAL] Получает значение, указывающее, является ли дескриптор недействительным.</summary>
      <returns>Значение true, если дескриптор недействителен, в противном случае — значение false.</returns>
    </member>
    <member name="T:System.Security.Principal.IdentityNotMappedException">
      <summary>Представляет исключение для участника, удостоверение которого невозможно сопоставить известному удостоверению.</summary>
    </member>
    <member name="M:System.Security.Principal.IdentityNotMappedException.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.Principal.IdentityNotMappedException" />.</summary>
    </member>
    <member name="M:System.Security.Principal.IdentityNotMappedException.#ctor(System.String)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.Principal.IdentityNotMappedException" />, используя указанное сообщение об ошибке.</summary>
      <param name="message">Сообщение об ошибке с объяснением причин исключения.</param>
    </member>
    <member name="M:System.Security.Principal.IdentityNotMappedException.#ctor(System.String,System.Exception)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.Principal.IdentityNotMappedException" />, используя указанные сообщение об ошибке и внутреннее исключение.</summary>
      <param name="message">Сообщение об ошибке с объяснением причин исключения.</param>
      <param name="inner">Исключение, которое вызвало текущее исключение.Если значение параметра <paramref name="inner" /> отличается от null, текущее исключение выдается в блоке catch, обрабатывающем внутреннее исключение.</param>
    </member>
    <member name="P:System.Security.Principal.IdentityNotMappedException.UnmappedIdentities">
      <summary>Представляет коллекцию несопоставленных удостоверений для исключения <see cref="T:System.Security.Principal.IdentityNotMappedException" />.</summary>
      <returns>Коллекция несопоставленных удостоверений.</returns>
    </member>
    <member name="T:System.Security.Principal.IdentityReference">
      <summary>Представляет удостоверение и является базовым классом для классов <see cref="T:System.Security.Principal.NTAccount" /> и <see cref="T:System.Security.Principal.SecurityIdentifier" />.Этот класс не предоставляет открытый конструктор и поэтому не может быть унаследованным.</summary>
    </member>
    <member name="M:System.Security.Principal.IdentityReference.Equals(System.Object)">
      <summary>Возвращает значение, показывающее, является ли указанный тип допустимым типом преобразования для класса <see cref="T:System.Security.Principal.IdentityReference" />.</summary>
      <returns>true, если <paramref name="o" /> является объектом тех же базового типа и значения, что и данный экземпляр <see cref="T:System.Security.Principal.IdentityReference" />; в противном случае — false.</returns>
      <param name="o">Объект, сравниваемый с данным экземпляром <see cref="T:System.Security.Principal.IdentityReference" />, или пустая ссылка.</param>
    </member>
    <member name="M:System.Security.Principal.IdentityReference.GetHashCode">
      <summary>Служит хэш-функцией для <see cref="T:System.Security.Principal.IdentityReference" />.Метод <see cref="M:System.Security.Principal.IdentityReference.GetHashCode" /> подходит для использования в алгоритмах хэширования и структурах данных, таких как хэш-таблицы.</summary>
      <returns>Хэш-код для данного объекта <see cref="T:System.Security.Principal.IdentityReference" />.</returns>
    </member>
    <member name="M:System.Security.Principal.IdentityReference.IsValidTargetType(System.Type)">
      <summary>Возвращает значение, показывающее, является ли указанный тип допустимым типом преобразования для класса <see cref="T:System.Security.Principal.IdentityReference" />.</summary>
      <returns>Значение true, если <paramref name="targetType" /> является допустимым типом преобразования для класса <see cref="T:System.Security.Principal.IdentityReference" />; в противном случае — значение false.</returns>
      <param name="targetType">Тип, допустимость использования которого в качестве типа преобразования из класса <see cref="T:System.Security.Principal.IdentityReference" /> требуется проверить.Допустимы следующие типы целевого объекта:<see cref="T:System.Security.Principal.NTAccount" /><see cref="T:System.Security.Principal.SecurityIdentifier" /></param>
    </member>
    <member name="M:System.Security.Principal.IdentityReference.op_Equality(System.Security.Principal.IdentityReference,System.Security.Principal.IdentityReference)">
      <summary>Сравнивает два объекта <see cref="T:System.Security.Principal.IdentityReference" /> на равенство.Объекты считаются равными, если их каноническое представление имени совпадает с представлением, возвращаемым свойством <see cref="P:System.Security.Principal.IdentityReference.Value" />, или значения обоих объектов равны null.</summary>
      <returns>true, если значения <paramref name="left" /> и <paramref name="right" /> равны; в противном случае — false.</returns>
      <param name="left">Левый операнд <see cref="T:System.Security.Principal.IdentityReference" />, используемый для сравнения в отношении равенства.Этот параметр может иметь значение null.</param>
      <param name="right">Правый операнд <see cref="T:System.Security.Principal.IdentityReference" />, используемый для сравнения в отношении равенства.Этот параметр может иметь значение null.</param>
    </member>
    <member name="M:System.Security.Principal.IdentityReference.op_Inequality(System.Security.Principal.IdentityReference,System.Security.Principal.IdentityReference)">
      <summary>Сравнивает два объекта <see cref="T:System.Security.Principal.IdentityReference" /> на неравенство.Объекты считаются неравными, если их канонические представления имени отличаются от представления, возвращаемого свойством <see cref="P:System.Security.Principal.IdentityReference.Value" />, или один из объектов имеет значение null, а другой объект имеет другое значение.</summary>
      <returns>Значение true, если значения параметров <paramref name="left" /> и <paramref name="right" /> не равны; в противном случае — значение false.</returns>
      <param name="left">Левый операнд <see cref="T:System.Security.Principal.IdentityReference" />, используемый для сравнения в отношении неравенства.Этот параметр может иметь значение null.</param>
      <param name="right">Правый операнд <see cref="T:System.Security.Principal.IdentityReference" />, используемый для сравнения на неравенство.Этот параметр может иметь значение null.</param>
    </member>
    <member name="M:System.Security.Principal.IdentityReference.ToString">
      <summary>Возвращает строковое представление удостоверения, представленного объектом <see cref="T:System.Security.Principal.IdentityReference" />.</summary>
      <returns>Удостоверение в строковом формате.</returns>
    </member>
    <member name="M:System.Security.Principal.IdentityReference.Translate(System.Type)">
      <summary>Преобразует имя учетной записи, представленное объектом <see cref="T:System.Security.Principal.IdentityReference" />, в другой тип, производный от <see cref="T:System.Security.Principal.IdentityReference" />.</summary>
      <returns>Преобразованное удостоверение.</returns>
      <param name="targetType">Тип целевого объекта для преобразования из объекта <see cref="T:System.Security.Principal.IdentityReference" />. </param>
    </member>
    <member name="P:System.Security.Principal.IdentityReference.Value">
      <summary>Получает строковое значение удостоверения, представленного объектом <see cref="T:System.Security.Principal.IdentityReference" />.</summary>
      <returns>Строковое значение удостоверения, представленного объектом <see cref="T:System.Security.Principal.IdentityReference" />.</returns>
    </member>
    <member name="T:System.Security.Principal.IdentityReferenceCollection">
      <summary>Представляет коллекцию объектов <see cref="T:System.Security.Principal.IdentityReference" /> и обеспечивает средства преобразования наборов объектов, производных от <see cref="T:System.Security.Principal.IdentityReference" />, в типы, производные от <see cref="T:System.Security.Principal.IdentityReference" />. </summary>
    </member>
    <member name="M:System.Security.Principal.IdentityReferenceCollection.#ctor">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.Principal.IdentityReferenceCollection" /> нулевыми элементами в коллекции.</summary>
    </member>
    <member name="M:System.Security.Principal.IdentityReferenceCollection.#ctor(System.Int32)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.Principal.IdentityReferenceCollection" />, используя заданный исходный размер.</summary>
      <param name="capacity">Исходное число элементов в коллекции.Значение параметра <paramref name="capacity" /> служит только в качестве подсказки; необязательно создается максимальное число элементов.</param>
    </member>
    <member name="M:System.Security.Principal.IdentityReferenceCollection.Add(System.Security.Principal.IdentityReference)">
      <summary>Добавляет объект <see cref="T:System.Security.Principal.IdentityReference" /> в коллекцию <see cref="T:System.Security.Principal.IdentityReferenceCollection" />.</summary>
      <param name="identity">Объект <see cref="T:System.Security.Principal.IdentityReference" /> для добавления в коллекцию.</param>
    </member>
    <member name="M:System.Security.Principal.IdentityReferenceCollection.Clear">
      <summary>Удаляет все объекты <see cref="T:System.Security.Principal.IdentityReference" /> из коллекции <see cref="T:System.Security.Principal.IdentityReferenceCollection" />.</summary>
    </member>
    <member name="M:System.Security.Principal.IdentityReferenceCollection.Contains(System.Security.Principal.IdentityReference)">
      <summary>Указывает, содержит ли коллекция <see cref="T:System.Security.Principal.IdentityReferenceCollection" /> заданный объект <see cref="T:System.Security.Principal.IdentityReference" />.</summary>
      <returns>Значение true, если коллекция содержит заданный объект.</returns>
      <param name="identity">Объект <see cref="T:System.Security.Principal.IdentityReference" /> для проверки.</param>
    </member>
    <member name="M:System.Security.Principal.IdentityReferenceCollection.CopyTo(System.Security.Principal.IdentityReference[],System.Int32)">
      <summary>Копирует коллекцию <see cref="T:System.Security.Principal.IdentityReferenceCollection" /> в массив <see cref="T:System.Security.Principal.IdentityReferenceCollection" />, начиная с указанного индекса.</summary>
      <param name="array">Объект массива <see cref="T:System.Security.Principal.IdentityReferenceCollection" />, в который должна быть скопирована коллекция <see cref="T:System.Security.Principal.IdentityReferenceCollection" />.</param>
      <param name="offset">Отсчитываемый от нуля индекс в массиве <paramref name="array" />, который обозначает позицию для копирования коллекции <see cref="T:System.Security.Principal.IdentityReferenceCollection" />.</param>
    </member>
    <member name="P:System.Security.Principal.IdentityReferenceCollection.Count">
      <summary>Возвращает число элементов в коллекции <see cref="T:System.Security.Principal.IdentityReferenceCollection" />.</summary>
      <returns>Число объектов <see cref="T:System.Security.Principal.IdentityReference" /> в коллекции <see cref="T:System.Security.Principal.IdentityReferenceCollection" />.</returns>
    </member>
    <member name="M:System.Security.Principal.IdentityReferenceCollection.GetEnumerator">
      <summary>Возвращает перечислитель, который может использоваться для выполнения итерации по коллекции <see cref="T:System.Security.Principal.IdentityReferenceCollection" />.</summary>
      <returns>Перечислитель для коллекции <see cref="T:System.Security.Principal.IdentityReferenceCollection" />.</returns>
    </member>
    <member name="P:System.Security.Principal.IdentityReferenceCollection.Item(System.Int32)">
      <summary>Устанавливает или возвращает узел по заданному индексу коллекции <see cref="T:System.Security.Principal.IdentityReferenceCollection" />.</summary>
      <returns>Объект <see cref="T:System.Security.Principal.IdentityReference" />, содержащийся в коллекции по указанному индексу.Если значение параметра <paramref name="index" /> больше или равно числу узлов в коллекции, возвращается значение null.</returns>
      <param name="index">Отсчитываемый от нуля индекс в коллекции <see cref="T:System.Security.Principal.IdentityReferenceCollection" />.</param>
    </member>
    <member name="M:System.Security.Principal.IdentityReferenceCollection.Remove(System.Security.Principal.IdentityReference)">
      <summary>Удаляет указанный объект <see cref="T:System.Security.Principal.IdentityReference" /> из коллекции.</summary>
      <returns>Значение true, если заданный объект был удален из коллекции.</returns>
      <param name="identity">Удаляемый объект <see cref="T:System.Security.Principal.IdentityReference" />.</param>
    </member>
    <member name="P:System.Security.Principal.IdentityReferenceCollection.System#Collections#Generic#ICollection{T}#IsReadOnly"></member>
    <member name="M:System.Security.Principal.IdentityReferenceCollection.System#Collections#IEnumerable#GetEnumerator">
      <summary>Возвращает перечислитель, который может использоваться для выполнения итерации по коллекции <see cref="T:System.Security.Principal.IdentityReferenceCollection" />.</summary>
      <returns>Перечислитель для коллекции <see cref="T:System.Security.Principal.IdentityReferenceCollection" />.</returns>
    </member>
    <member name="M:System.Security.Principal.IdentityReferenceCollection.Translate(System.Type)">
      <summary>Преобразует объекты коллекции в указанный тип.Этот метод вызывается так же, как метод <see cref="M:System.Security.Principal.IdentityReferenceCollection.Translate(System.Type,System.Boolean)" /> со значением false второго параметра. Это означает, что для элементов, преобразование которых завершается неудачно, исключения выдаваться не будут.</summary>
      <returns>Коллекция <see cref="T:System.Security.Principal.IdentityReferenceCollection" />, представляющая преобразованное содержимое исходной коллекции.</returns>
      <param name="targetType">Тип, в который преобразуются элементы коллекции.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.Principal.IdentityReferenceCollection.Translate(System.Type,System.Boolean)">
      <summary>Преобразует объекты коллекции в указанный тип и использует заданную отказоустойчивость для обработки или игнорирования ошибок, связанных с типом, не имеющим сопоставление преобразования.</summary>
      <returns>Коллекция <see cref="T:System.Security.Principal.IdentityReferenceCollection" />, представляющая преобразованное содержимое исходной коллекции.</returns>
      <param name="targetType">Тип, в который преобразуются элементы коллекции.</param>
      <param name="forceSuccess">Логическое значение, определяющее способ обработки ошибок преобразования.Если параметр <paramref name="forceSuccess" /> имеет значение true, ошибки преобразования из-за необнаружения сопоставления во время преобразования приводят к сбою преобразования и вызову исключений.Если параметр <paramref name="forceSuccess" /> имеет значение false, типы, которые не удалось преобразовать из-за необнаружения сопоставления во время преобразования, копируются в возвращаемую коллекцию без преобразования.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
    <member name="T:System.Security.Principal.NTAccount">
      <summary>Представляет учетную запись пользователя или группы.</summary>
    </member>
    <member name="M:System.Security.Principal.NTAccount.#ctor(System.String)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.Principal.NTAccount" />, используя указанное имя.</summary>
      <param name="name">Имя, используемое для создания объекта <see cref="T:System.Security.Principal.NTAccount" />.Этот параметр не может иметь значение null или являться пустой строкой.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="name" /> имеет значение null.</exception>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="name" /> содержит пустую строку.– или –Параметр <paramref name="name" /> имеет слишком большую длину.</exception>
    </member>
    <member name="M:System.Security.Principal.NTAccount.#ctor(System.String,System.String)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.Principal.NTAccount" />, используя указанные имя домена и имя учетной записи. </summary>
      <param name="domainName">Имя домена.Этот параметр может иметь значение null или являться пустой строкой.Имена доменов, имеющие значения NULL, обрабатываются аналогично пустой строке.</param>
      <param name="accountName">Имя учетной записи.Этот параметр не может иметь значение null или являться пустой строкой.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="accountName" /> имеет значение null.</exception>
      <exception cref="T:System.ArgumentException">Параметр <paramref name="accountName" /> содержит пустую строку.– или –Параметр <paramref name="accountName" /> имеет слишком большую длину.– или –Параметр <paramref name="domainName" /> имеет слишком большую длину.</exception>
    </member>
    <member name="M:System.Security.Principal.NTAccount.Equals(System.Object)">
      <summary>Возвращает значение, показывающее, равен ли данный объект <see cref="T:System.Security.Principal.NTAccount" /> указанному объекту.</summary>
      <returns>true, если <paramref name="o" /> является объектом того же базового типа и значения, что и данный объект <see cref="T:System.Security.Principal.NTAccount" />; в противном случае — false.</returns>
      <param name="o">Объект, сравниваемый с данным объектом <see cref="T:System.Security.Principal.NTAccount" />, или значение null.</param>
    </member>
    <member name="M:System.Security.Principal.NTAccount.GetHashCode">
      <summary>Служит хэш-функцией для текущего объекта <see cref="T:System.Security.Principal.NTAccount" />.Метод <see cref="M:System.Security.Principal.NTAccount.GetHashCode" /> подходит для алгоритмов хэширования и структур данных, таких как хэш-таблицы.</summary>
      <returns>Хэш-значение для текущего объекта <see cref="T:System.Security.Principal.NTAccount" />.</returns>
    </member>
    <member name="M:System.Security.Principal.NTAccount.IsValidTargetType(System.Type)">
      <summary>Возвращает значение, показывающее, является ли указанный тип допустимым типом преобразования для класса <see cref="T:System.Security.Principal.NTAccount" />.</summary>
      <returns>true, если <paramref name="targetType" /> является допустимым типом преобразования для класса <see cref="T:System.Security.Principal.NTAccount" />; в противном случае — false.</returns>
      <param name="targetType">Тип, допустимость использования которого в качестве типа преобразования из класса <see cref="T:System.Security.Principal.NTAccount" /> требуется проверить.Допустимы следующие типы целевого объекта:- <see cref="T:System.Security.Principal.NTAccount" />- <see cref="T:System.Security.Principal.SecurityIdentifier" /></param>
    </member>
    <member name="M:System.Security.Principal.NTAccount.op_Equality(System.Security.Principal.NTAccount,System.Security.Principal.NTAccount)">
      <summary>Сравнивает два объекта <see cref="T:System.Security.Principal.NTAccount" /> на равенство.Объекты считаются равными, если их каноническое представление имени совпадает с представлением, возвращаемым свойством <see cref="P:System.Security.Principal.NTAccount.Value" />, или значения обоих объектов равны null.</summary>
      <returns>true, если <paramref name="left" /> и <paramref name="right" /> равны; в ином случае — false.</returns>
      <param name="left">Левый операнд, используемый для сравнения в отношении равенства.Этот параметр может иметь значение null.</param>
      <param name="right">Правый операнд, используемый для сравнения в отношении равенства.Этот параметр может иметь значение null.</param>
    </member>
    <member name="M:System.Security.Principal.NTAccount.op_Inequality(System.Security.Principal.NTAccount,System.Security.Principal.NTAccount)">
      <summary>Сравнивает два объекта <see cref="T:System.Security.Principal.NTAccount" /> на неравенство.Объекты считаются неравными, если их канонические представления имени отличаются от представления, возвращаемого свойством <see cref="P:System.Security.Principal.NTAccount.Value" />, или один из объектов имеет значение null, а другой объект имеет другое значение.</summary>
      <returns>true, если <paramref name="left" /> и <paramref name="right" /> не равны; в ином случае — false.</returns>
      <param name="left">Левый операнд, используемый для сравнения в отношении неравенства.Этот параметр может иметь значение null.</param>
      <param name="right">Правый операнд, используемый для сравнения в отношении неравенства.Этот параметр может иметь значение null.</param>
    </member>
    <member name="M:System.Security.Principal.NTAccount.ToString">
      <summary>Возвращает имя учетной записи в формате Домен\Учетная запись для учетной записи, представленной объектом <see cref="T:System.Security.Principal.NTAccount" />.</summary>
      <returns>Имя учетной записи в формате Домен\Учетная запись.</returns>
    </member>
    <member name="M:System.Security.Principal.NTAccount.Translate(System.Type)">
      <summary>Преобразует имя учетной записи, представленное объектом <see cref="T:System.Security.Principal.NTAccount" />, в другой тип, производный от <see cref="T:System.Security.Principal.IdentityReference" />.</summary>
      <returns>Преобразованное удостоверение.</returns>
      <param name="targetType">Тип целевого объекта для преобразования из объекта <see cref="T:System.Security.Principal.NTAccount" />.Типом целевого объекта должен быть тип, допустимый для использования в качестве аргумента метода <see cref="M:System.Security.Principal.NTAccount.IsValidTargetType(System.Type)" />.</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="targetType " />— null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="targetType " /> не является типом <see cref="T:System.Security.Principal.IdentityReference" />.</exception>
      <exception cref="T:System.Security.Principal.IdentityNotMappedException">Некоторые или ссылки на свойства нельзя преобразовать.</exception>
      <exception cref="T:System.SystemException">Исходное имя учетной записи слишком длинное.– или –Возвращен код ошибки Win32.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
    <member name="P:System.Security.Principal.NTAccount.Value">
      <summary>Возвращает строковое представление (в верхнем регистре) данного объекта <see cref="T:System.Security.Principal.NTAccount" />.</summary>
      <returns>Строковое представление (в верхнем регистре) данного объекта <see cref="T:System.Security.Principal.NTAccount" />.</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="T:System.Security.Principal.SecurityIdentifier">
      <summary>Представляет идентификатор безопасности (SID) и предоставляет операции маршалинга и сравнения для SID.</summary>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.#ctor(System.Byte[],System.Int32)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.Principal.SecurityIdentifier" />, используя указанное двоичное представление идентификатора безопасности (ИД безопасности).</summary>
      <param name="binaryForm">Массив байтов, представляющий ИД безопасности.</param>
      <param name="offset">Смещение в байтах, которое должно использоваться в качестве начального индекса в параметре <paramref name="binaryForm" />. </param>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.#ctor(System.IntPtr)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.Principal.SecurityIdentifier" />, используя целое число, представляющее двоичную форму идентификатора безопасности (ИД безопасности).</summary>
      <param name="binaryForm">Целое число, представляющее двоичную форму ИД безопасности.</param>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.#ctor(System.Security.Principal.WellKnownSidType,System.Security.Principal.SecurityIdentifier)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.Principal.SecurityIdentifier" />, используя указанные хорошо известный тип идентификатора безопасности (ИД безопасности) и ИД безопасности домена.</summary>
      <param name="sidType">Одно из значений перечисления.Это значение не должно быть равно <see cref="F:System.Security.Principal.WellKnownSidType.LogonIdsSid" />.</param>
      <param name="domainSid">ИД безопасности домена.Это значение требуется для указанных ниже значений <see cref="T:System.Security.Principal.WellKnownSidType" />.Для любых других значений <see cref="T:System.Security.Principal.WellKnownSidType" /> данный параметр игнорируется.- <see cref="F:System.Security.Principal.WellKnownSidType.AccountAdministratorSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountGuestSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountKrbtgtSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountDomainAdminsSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountDomainUsersSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountDomainGuestsSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountComputersSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountControllersSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountCertAdminsSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountSchemaAdminsSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountEnterpriseAdminsSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountPolicyAdminsSid" />- <see cref="F:System.Security.Principal.WellKnownSidType.AccountRasAndIasServersSid" /></param>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.#ctor(System.String)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.Principal.SecurityIdentifier" />, используя указанный идентификатор безопасности (ИД безопасности) в формате SDDL (Security Descriptor Definition Language).</summary>
      <param name="sddlForm">Строка SDDL для идентификатора безопасности, используемого для создания объекта <see cref="T:System.Security.Principal.SecurityIdentifier" />.</param>
    </member>
    <member name="P:System.Security.Principal.SecurityIdentifier.AccountDomainSid">
      <summary>Возвращает часть идентификатора безопасности (ИД безопасности) домена учетных записей из ИД безопасности, представленного объектом <see cref="T:System.Security.Principal.SecurityIdentifier" />, если этот ИД безопасности представляет ИД безопасности учетной записи Windows.Если данный ИД безопасности не представляет ИД безопасности учетной записи Windows, это свойство возвращает <see cref="T:System.ArgumentNullException" />.</summary>
      <returns>Часть ИД безопасности домена учетных записей из ИД безопасности, представленного объектом <see cref="T:System.Security.Principal.SecurityIdentifier" />, если этот ИД безопасности представляет ИД безопасности учетной записи Windows; в противном случае возвращается <see cref="T:System.ArgumentNullException" />.</returns>
    </member>
    <member name="P:System.Security.Principal.SecurityIdentifier.BinaryLength">
      <summary>Возвращает длину (в байтах) идентификатора безопасности (ИД безопасности), представленного объектом <see cref="T:System.Security.Principal.SecurityIdentifier" />.</summary>
      <returns>Длина (в байтах) ИД безопасности, представленного объектом <see cref="T:System.Security.Principal.SecurityIdentifier" />.</returns>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.CompareTo(System.Security.Principal.SecurityIdentifier)">
      <summary>Сравнивает текущий объект <see cref="T:System.Security.Principal.SecurityIdentifier" /> с заданным объектом <see cref="T:System.Security.Principal.SecurityIdentifier" />.</summary>
      <returns>Знаковое число, представляющее относительные значения этого экземпляра и параметра <paramref name="sid" />.Возвращаемое значение Описание Меньше нуля Данный экземпляр меньше <paramref name="sid" />. Zero Этот экземпляр и параметр <paramref name="sid" /> равны. Больше нуля. Этот экземпляр больше параметра <paramref name="sid" />. </returns>
      <param name="sid">Объект, который требуется сравнить с текущим объектом.</param>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.Equals(System.Object)">
      <summary>Возвращает значение, показывающее, равен ли данный объект <see cref="T:System.Security.Principal.SecurityIdentifier" /> указанному объекту.</summary>
      <returns>true, если <paramref name="o" /> является объектом того же базового типа и значения, что и данный объект <see cref="T:System.Security.Principal.SecurityIdentifier" />; в противном случае — false.</returns>
      <param name="o">Объект, сравниваемый с данным объектом <see cref="T:System.Security.Principal.SecurityIdentifier" />, или значение null.</param>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.Equals(System.Security.Principal.SecurityIdentifier)">
      <summary>Показывает, равен ли заданный объект <see cref="T:System.Security.Principal.SecurityIdentifier" /> текущему объекту <see cref="T:System.Security.Principal.SecurityIdentifier" />.</summary>
      <returns>true, если значение <paramref name="sid" /> равно значению текущего объекта <see cref="T:System.Security.Principal.SecurityIdentifier" />.</returns>
      <param name="sid">Объект, который требуется сравнить с текущим объектом.</param>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.GetBinaryForm(System.Byte[],System.Int32)">
      <summary>Копирует двоичное представление указанного идентификатора безопасности (ИД безопасности), представленного классом <see cref="T:System.Security.Principal.SecurityIdentifier" />, в массив байтов.</summary>
      <param name="binaryForm">Массив байтов для копируемого ИД безопасности.</param>
      <param name="offset">Смещение в байтах, которое должно использоваться в качестве начального индекса в параметре <paramref name="binaryForm" />. </param>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.GetHashCode">
      <summary>Служит хэш-функцией для текущего объекта <see cref="T:System.Security.Principal.SecurityIdentifier" />.Метод <see cref="M:System.Security.Principal.SecurityIdentifier.GetHashCode" /> подходит для алгоритмов хэширования и структур данных, таких как хэш-таблицы.</summary>
      <returns>Хэш-значение для текущего объекта <see cref="T:System.Security.Principal.SecurityIdentifier" />.</returns>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.IsAccountSid">
      <summary>Возвращает значение, показывающее, является ли идентификатор безопасности (ИД безопасности), представленный данным объектом <see cref="T:System.Security.Principal.SecurityIdentifier" />, допустимым ИД безопасности учетной записи Windows.</summary>
      <returns>true, если ИД безопасности, представленный данным объектом <see cref="T:System.Security.Principal.SecurityIdentifier" />, является допустимым ИД безопасности учетной записи Windows; в противном случае — false.</returns>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.IsEqualDomainSid(System.Security.Principal.SecurityIdentifier)">
      <summary>Возвращает значение, показывающее, относится ли идентификатор безопасности (ИД безопасности), представленный данным объектом <see cref="T:System.Security.Principal.SecurityIdentifier" />, к тому же домену, что и заданный ИД безопасности.</summary>
      <returns>true, если ИД безопасности, представленный данным объектом <see cref="T:System.Security.Principal.SecurityIdentifier" />, относится к тому же домену, что и ИД безопасности <paramref name="sid" />; в противном случае — false.</returns>
      <param name="sid">ИД безопасности, который требуется сравнить с данным объектом <see cref="T:System.Security.Principal.SecurityIdentifier" />.</param>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.IsValidTargetType(System.Type)">
      <summary>Возвращает значение, показывающее, является ли указанный тип допустимым типом преобразования для класса <see cref="T:System.Security.Principal.SecurityIdentifier" />.</summary>
      <returns>true, если <paramref name="targetType" /> является допустимым типом преобразования для класса <see cref="T:System.Security.Principal.SecurityIdentifier" />; в противном случае — false.</returns>
      <param name="targetType">Тип, допустимость использования которого в качестве типа преобразования из класса <see cref="T:System.Security.Principal.SecurityIdentifier" /> требуется проверить.Допустимы следующие типы целевого объекта:- <see cref="T:System.Security.Principal.NTAccount" />- <see cref="T:System.Security.Principal.SecurityIdentifier" /></param>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.IsWellKnown(System.Security.Principal.WellKnownSidType)">
      <summary>Возвращает значение, показывающее, соответствует ли объект <see cref="T:System.Security.Principal.SecurityIdentifier" /> заданному хорошо известному типу идентификатора безопасности (ИД безопасности). </summary>
      <returns>true, если <paramref name="type" /> — тип ИД безопасности для объекта <see cref="T:System.Security.Principal.SecurityIdentifier" />; в противном случае — false.</returns>
      <param name="type">Значение, которое требуется сравнить с объектом <see cref="T:System.Security.Principal.SecurityIdentifier" />.</param>
    </member>
    <member name="F:System.Security.Principal.SecurityIdentifier.MaxBinaryLength">
      <summary>Возвращает максимальный размер (в байтах) двоичного представления идентификатора безопасности.</summary>
    </member>
    <member name="F:System.Security.Principal.SecurityIdentifier.MinBinaryLength">
      <summary>Возвращает минимальный размер (в байтах) двоичного представления идентификатора безопасности.</summary>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.op_Equality(System.Security.Principal.SecurityIdentifier,System.Security.Principal.SecurityIdentifier)">
      <summary>Сравнивает два объекта <see cref="T:System.Security.Principal.SecurityIdentifier" /> на равенство.Объекты считаются равными, если их каноническое представление совпадает с представлением, возвращаемым свойством <see cref="P:System.Security.Principal.SecurityIdentifier.Value" />, или оба объекта имеют значение null.</summary>
      <returns>true, если значения <paramref name="left" /> и <paramref name="right" /> равны; в противном случае — false.</returns>
      <param name="left">Левый операнд, используемый для сравнения в отношении равенства.Этот параметр может иметь значение null.</param>
      <param name="right">Правый операнд, используемый для сравнения в отношении равенства.Этот параметр может иметь значение null.</param>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.op_Inequality(System.Security.Principal.SecurityIdentifier,System.Security.Principal.SecurityIdentifier)">
      <summary>Сравнивает два объекта <see cref="T:System.Security.Principal.SecurityIdentifier" /> на неравенство.Объекты считаются неравными, если их канонические представления имени отличаются от представления, возвращаемого свойством <see cref="P:System.Security.Principal.SecurityIdentifier.Value" />, или один из объектов имеет значение null, а другой объект имеет другое значение.</summary>
      <returns>Значение true, если значения параметров <paramref name="left" /> и <paramref name="right" /> не равны; в противном случае — значение false.</returns>
      <param name="left">Левый операнд, используемый для сравнения в отношении неравенства.Этот параметр может иметь значение null.</param>
      <param name="right">Правый операнд, используемый для сравнения в отношении неравенства.Этот параметр может иметь значение null.</param>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.ToString">
      <summary>Возвращает идентификатор безопасности (ИД безопасности) в формате SDDL (Security Descriptor Definition Language) для учетной записи, представленной объектом <see cref="T:System.Security.Principal.SecurityIdentifier" />.Пример формата SDDL — S-1-5-9.</summary>
      <returns>ИД безопасности в формате SDDL для учетной записи, представленной объектом <see cref="T:System.Security.Principal.SecurityIdentifier" />.</returns>
    </member>
    <member name="M:System.Security.Principal.SecurityIdentifier.Translate(System.Type)">
      <summary>Преобразует имя учетной записи, представленной объектом <see cref="T:System.Security.Principal.SecurityIdentifier" />, в другой тип, производный от <see cref="T:System.Security.Principal.IdentityReference" />.</summary>
      <returns>Преобразованное удостоверение.</returns>
      <param name="targetType">Тип целевого объекта для преобразования из объекта <see cref="T:System.Security.Principal.SecurityIdentifier" />.Типом целевого объекта должен быть тип, допустимый для использования в качестве аргумента метода <see cref="M:System.Security.Principal.SecurityIdentifier.IsValidTargetType(System.Type)" />.</param>
      <exception cref="T:System.ArgumentNullException">Значение параметра <paramref name="targetType " />— null.</exception>
      <exception cref="T:System.ArgumentException">
        <paramref name="targetType " /> не является типом <see cref="T:System.Security.Principal.IdentityReference" />.</exception>
      <exception cref="T:System.Security.Principal.IdentityNotMappedException">Некоторые или ссылки на свойства нельзя преобразовать.</exception>
      <exception cref="T:System.SystemException">Возвращен код ошибки Win32.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
    <member name="P:System.Security.Principal.SecurityIdentifier.Value">
      <summary>Возвращает строку символов в верхнем регистре в формате SDDL (Security Descriptor Definition Language) для идентификатора безопасности (ИД безопасности), представленного данным объектом <see cref="T:System.Security.Principal.SecurityIdentifier" />.</summary>
      <returns>Строка символов в верхнем регистре в формате SDDL для ИД безопасности, представленного объектом <see cref="T:System.Security.Principal.SecurityIdentifier" />.</returns>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="T:System.Security.Principal.TokenAccessLevels">
      <summary>Определяет привилегии учетной записи пользователя, связанной с маркером доступа. </summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.AdjustDefault">
      <summary>Пользователь может изменить для маркера владельца по умолчанию, основную группу или список управления доступом на уровне пользователей (DACL).</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.AdjustGroups">
      <summary>Пользователь может изменить атрибуты групп в маркере.</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.AdjustPrivileges">
      <summary>Пользователь может включить или отключить привилегии в маркере.</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.AdjustSessionId">
      <summary>Пользователь может изменить идентификатор сеанса маркера.</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.AllAccess">
      <summary>Пользователь имеет полный доступ к маркеру.</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.AssignPrimary">
      <summary>Пользователь может присоединить основной маркер к процессу.</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.Duplicate">
      <summary>Пользователь может дублировать маркер.</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.Impersonate">
      <summary>Пользователь может олицетворять клиента.</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.MaximumAllowed">
      <summary>Максимальное значение, которое может быть назначено для перечисления <see cref="T:System.Security.Principal.TokenAccessLevels" />.</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.Query">
      <summary>Пользователь может запросить маркер.</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.QuerySource">
      <summary>Пользователь может запросить источник маркера.</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.Read">
      <summary>Пользователь имеет обычные права чтения и привилегию <see cref="F:System.Security.Principal.TokenAccessLevels.Query" /> для маркера.</summary>
    </member>
    <member name="F:System.Security.Principal.TokenAccessLevels.Write">
      <summary>Пользователь имеет обычные права записи и привилегии <see cref="F:System.Security.Principal.TokenAccessLevels.AdjustPrivileges,F:System.Security.Principal.TokenAccessLevels.AdjustGroups" /> и <see cref="F:System.Security.Principal.TokenAccessLevels.AdjustDefault" /> для маркера.</summary>
    </member>
    <member name="T:System.Security.Principal.WellKnownSidType">
      <summary>Определяет набор часто используемых идентификаторов безопасности (SID).</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountAdministratorSid">
      <summary>Идентификатор безопасности, соответствующий группе учетных записей администраторов.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountCertAdminsSid">
      <summary>Идентификатор безопасности, соответствующий группе администраторов сертификатов.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountComputersSid">
      <summary>Идентификатор безопасности, соответствующий группе учетных записей компьютеров.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountControllersSid">
      <summary>Идентификатор безопасности, соответствующий группе учетных записей контроллеров.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountDomainAdminsSid">
      <summary>Идентификатор безопасности, соответствующий группе учетных записей администраторов домена.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountDomainGuestsSid">
      <summary>Идентификатор безопасности, соответствующий группе учетных записей гостей домена.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountDomainUsersSid">
      <summary>Идентификатор безопасности, соответствующий группе учетных записей пользователей домена.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountEnterpriseAdminsSid">
      <summary>Идентификатор безопасности, соответствующий группе администраторов предприятия.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountGuestSid">
      <summary>Идентификатор безопасности, соответствующий группе учетных записей гостей.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountKrbtgtSid">
      <summary>Идентификатор безопасности, соответствующий группе учетных записей целевых объектов Kerberos.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountPolicyAdminsSid">
      <summary>Идентификатор безопасности, соответствующий группе администраторов политик.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountRasAndIasServersSid">
      <summary>Идентификатор безопасности, соответствующий учетной записи серверов RAS и IAS.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AccountSchemaAdminsSid">
      <summary>Идентификатор безопасности, соответствующий группе администраторов схемы.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AnonymousSid">
      <summary>Идентификатор безопасности для анонимной учетной записи.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.AuthenticatedUserSid">
      <summary>Идентификатор безопасности для прошедшего проверку пользователя.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BatchSid">
      <summary>Идентификатор безопасности для процесса пакетной обработки.Этот идентификатор добавляется в процесс маркера, когда он входит в систему в качестве пакетного задания.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinAccountOperatorsSid">
      <summary>Идентификатор безопасности, соответствующий учетной записи операторов учета.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinAdministratorsSid">
      <summary>Идентификатор безопасности, соответствующий учетной записи администратора.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinAuthorizationAccessSid">
      <summary>Идентификатор безопасности, соответствующий группе авторизации доступа Windows.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinBackupOperatorsSid">
      <summary>Идентификатор безопасности, соответствующий группе операторов архива.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinDomainSid">
      <summary>Идентификатор безопасности, соответствующий учетной записи домена.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinGuestsSid">
      <summary>Идентификатор безопасности, соответствующий учетной записи "Гость".</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinIncomingForestTrustBuildersSid">
      <summary>Идентификатор безопасности, позволяющий пользователю создавать входящие доверия лесов.Этот идентификатор добавляется в маркер пользователей, являющихся членами встроенной группы "Создатели входящего доверия леса" в корневом домене леса.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinNetworkConfigurationOperatorsSid">
      <summary>Идентификатор безопасности, соответствующий группе операторов сети.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinPerformanceLoggingUsersSid">
      <summary>Указывает идентификатор безопасности, соответствующий группе пользователей, которые имеют доступ к мониторингу компьютера.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinPerformanceMonitoringUsersSid">
      <summary>Указывает идентификатор безопасности, соответствующий группе пользователей, которые имеют доступ к планированию записи в журнал счетчиков производительности на данном компьютере.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinPowerUsersSid">
      <summary>Идентификатор безопасности, соответствующий группе опытных пользователей.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinPreWindows2000CompatibleAccessSid">
      <summary>Идентификатор безопасности, соответствующий учетным записям, совместимым с пред-Windows 2000.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinPrintOperatorsSid">
      <summary>Идентификатор безопасности, соответствующий группе операторов печати.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinRemoteDesktopUsersSid">
      <summary>Идентификатор безопасности, соответствующий пользователям удаленного рабочего стола.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinReplicatorSid">
      <summary>Идентификатор безопасности, соответствующий учетной записи "Репликатор".</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinSystemOperatorsSid">
      <summary>Идентификатор безопасности, соответствующий группе системных операторов.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.BuiltinUsersSid">
      <summary>Идентификатор безопасности, соответствующий встроенным учетным записям пользователей.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.CreatorGroupServerSid">
      <summary>Идентификатор безопасности группы-создателя "сервер".</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.CreatorGroupSid">
      <summary>Идентификатор безопасности, соответствующий группе-создателю объекта.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.CreatorOwnerServerSid">
      <summary>Идентификатор безопасности владельца-создателя "сервер".</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.CreatorOwnerSid">
      <summary>Идентификатор безопасности, соответствующий владельцу или создателю объекта.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.DialupSid">
      <summary>Идентификатор безопасности для учетной записи удаленного подключения.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.DigestAuthenticationSid">
      <summary>Идентификатор безопасности, присутствующий после проверки подлинности клиента пакетом дайджест-проверки подлинности Microsoft.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.EnterpriseControllersSid">
      <summary>Идентификатор безопасности для контроллера предприятия.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.InteractiveSid">
      <summary>Идентификатор безопасности для интерактивной учетной записи.Этот идентификатор добавляется в процесс маркера, когда он входит в систему интерактивно.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.LocalServiceSid">
      <summary>Идентификатор безопасности, соответствующий локальной службе.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.LocalSid">
      <summary>Локальный идентификатор безопасности.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.LocalSystemSid">
      <summary>Идентификатор безопасности, соответствующий локальной системе.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.LogonIdsSid">
      <summary>Идентификатор безопасности, соответствующий идентификаторам входа.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.MaxDefined">
      <summary>Максимальный определенный идентификатор безопасности в перечислении <see cref="T:System.Security.Principal.WellKnownSidType" />.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.NetworkServiceSid">
      <summary>Идентификатор безопасности, соответствующий сетевой службе.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.NetworkSid">
      <summary>Идентификатор безопасности для сетевой учетной записи.Этот идентификатор добавляется в процесс маркера, когда он входит в систему по сети.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.NTAuthoritySid">
      <summary>Идентификатор безопасности для центра Windows NT.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.NtlmAuthenticationSid">
      <summary>Идентификатор безопасности, присутствующий после проверки подлинности клиента пакетом проверки подлинности Microsoft NTLM.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.NullSid">
      <summary>Пустой идентификатор безопасности.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.OtherOrganizationSid">
      <summary>Идентификатор безопасности, присутствующий после прохождения пользователем проверки подлинности через лес с включенной функцией выборочной проверки подлинности.При наличии идентификатора безопасности идентификатор <see cref="F:System.Security.Principal.WellKnownSidType.ThisOrganizationSid" /> присутствовать не может.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.ProxySid">
      <summary>Идентификатор безопасности прокси-сервера.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.RemoteLogonIdSid">
      <summary>Идентификатор безопасности, соответствующий удаленным входам в систему.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.RestrictedCodeSid">
      <summary>Идентификатор безопасности для запрещенного кода.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.SChannelAuthenticationSid">
      <summary>Идентификатор безопасности, присутствующий после проверки подлинности клиента пакетом проверки подлинности по безопасному каналу (SSL/TLS).</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.SelfSid">
      <summary>Идентификатор безопасности для самообслуживания.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.ServiceSid">
      <summary>Идентификатор безопасности для службы.Этот идентификатор добавляется в процесс маркера, когда он входит в систему в качестве службы.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.TerminalServerSid">
      <summary>Идентификатор безопасности, соответствующий учетной записи сервера терминалов.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.ThisOrganizationSid">
      <summary>Идентификатор безопасности, присутствующий после прохождения пользователем проверки подлинности изнутри леса или через уровень доверия, не имеющий включенной функции выборочной проверки подлинности.При наличии этого идентификатора идентификатор <see cref="F:System.Security.Principal.WellKnownSidType.OtherOrganizationSid" /> присутствовать не может.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.WinBuiltinTerminalServerLicenseServersSid">
      <summary>Идентификатор безопасности, присутствующий на сервере, который может выдавать лицензии для сервера терминалов.</summary>
    </member>
    <member name="F:System.Security.Principal.WellKnownSidType.WorldSid">
      <summary>Идентификатор безопасности, соответствующий любому объекту.</summary>
    </member>
    <member name="T:System.Security.Principal.WindowsBuiltInRole">
      <summary>Задает основные роли, используемые в методе <see cref="M:System.Security.Principal.WindowsPrincipal.IsInRole(System.String)" />.</summary>
    </member>
    <member name="F:System.Security.Principal.WindowsBuiltInRole.AccountOperator">
      <summary>Операторы учетных записей осуществляют управление учетными записями пользователей на компьютере или в домене.</summary>
    </member>
    <member name="F:System.Security.Principal.WindowsBuiltInRole.Administrator">
      <summary>Администраторы обладают полным и неограниченным доступом к компьютеру или домену.</summary>
    </member>
    <member name="F:System.Security.Principal.WindowsBuiltInRole.BackupOperator">
      <summary>Операторы архива могут переопределять ограничения по безопасности для конкретных целей (резервное копирование или восстановление файлов).</summary>
    </member>
    <member name="F:System.Security.Principal.WindowsBuiltInRole.Guest">
      <summary>Для пользователей с гостевой учетной записью существует больше ограничений, чем для обычных пользователей.</summary>
    </member>
    <member name="F:System.Security.Principal.WindowsBuiltInRole.PowerUser">
      <summary>Опытным пользователям предоставлено большинство административных прав с некоторыми ограничениями.Поэтому наряду с сертифицированными приложениями опытные пользователи могут запускать приложения прежних версий.</summary>
    </member>
    <member name="F:System.Security.Principal.WindowsBuiltInRole.PrintOperator">
      <summary>Операторы печати могут управлять принтером.</summary>
    </member>
    <member name="F:System.Security.Principal.WindowsBuiltInRole.Replicator">
      <summary>Репликаторы поддерживают репликацию файлов в домене.</summary>
    </member>
    <member name="F:System.Security.Principal.WindowsBuiltInRole.SystemOperator">
      <summary>Системные операторы управляют определенным компьютером.</summary>
    </member>
    <member name="F:System.Security.Principal.WindowsBuiltInRole.User">
      <summary>Обычным пользователям не разрешено вносить в систему случайные или преднамеренные изменения.Поэтому обычные пользователи могут запускать только сертифицированные приложения. Запуск большинства приложений прежних версий им запрещен.</summary>
    </member>
    <member name="T:System.Security.Principal.WindowsIdentity">
      <summary>Представляет пользователя Windows.</summary>
    </member>
    <member name="M:System.Security.Principal.WindowsIdentity.#ctor(System.IntPtr)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.Principal.WindowsIdentity" /> для пользователя, представленного заданным токеном учетной записи Windows.</summary>
      <param name="userToken">Токен учетной записи для пользователя, от лица которого выполняется код. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="userToken" /> is 0.-or-<paramref name="userToken" /> is duplicated and invalid for impersonation.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the correct permissions. -or-A Win32 error occurred.</exception>
    </member>
    <member name="M:System.Security.Principal.WindowsIdentity.#ctor(System.IntPtr,System.String)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.Principal.WindowsIdentity" /> для пользователя, представленного заданным токеном учетной записи Windows и заданным типом проверки подлинности.</summary>
      <param name="userToken">Токен учетной записи для пользователя, от лица которого выполняется код. </param>
      <param name="type">(Использовать только для справки.) Тип проверки подлинности, применяемой для идентификации пользователя.Дополнительные сведения см. в разделе "Замечания".</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="userToken" /> is 0.-or-<paramref name="userToken" /> is duplicated and invalid for impersonation.</exception>
      <exception cref="T:System.Security.SecurityException">The caller does not have the correct permissions. -or-A Win32 error occurred.</exception>
    </member>
    <member name="P:System.Security.Principal.WindowsIdentity.AccessToken">
      <summary>[SECURITY CRITICAL] Возвращает объект <see cref="T:Microsoft.Win32.SafeHandles.SafeAccessTokenHandle" /> для данного экземпляра <see cref="T:System.Security.Principal.WindowsIdentity" />. </summary>
      <returns>Возвращает значение типа <see cref="T:Microsoft.Win32.SafeHandles.SafeAccessTokenHandle" />.</returns>
    </member>
    <member name="M:System.Security.Principal.WindowsIdentity.Dispose">
      <summary>Освобождает все ресурсы, занятые модулем <see cref="T:System.Security.Principal.WindowsIdentity" />. </summary>
    </member>
    <member name="M:System.Security.Principal.WindowsIdentity.Dispose(System.Boolean)">
      <summary>Освобождает неуправляемые ресурсы, используемые объектом <see cref="T:System.Security.Principal.WindowsIdentity" />, а при необходимости освобождает также управляемые ресурсы. </summary>
      <param name="disposing">Значение true позволяет освободить как управляемые, так и неуправляемые ресурсы; значение false освобождает только неуправляемые ресурсы. </param>
    </member>
    <member name="M:System.Security.Principal.WindowsIdentity.GetAnonymous">
      <summary>Возвращает объект <see cref="T:System.Security.Principal.WindowsIdentity" />, который можно использовать в качестве контрольного значения в коде, чтобы представлять анонимного пользователя.Значение свойства не представляет встроенный анонимный идентификатор, используемый операционной системой Windows.</summary>
      <returns>Объект, представляющий анонимного пользователя.</returns>
    </member>
    <member name="M:System.Security.Principal.WindowsIdentity.GetCurrent">
      <summary>Возвращает объект <see cref="T:System.Security.Principal.WindowsIdentity" />, представляющий текущего пользователя Windows.</summary>
      <returns>Объект, представляющий текущего пользователя.</returns>
      <exception cref="T:System.Security.SecurityException">The caller does not have the correct permissions. </exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlPrincipal" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.Principal.WindowsIdentity.GetCurrent(System.Boolean)">
      <summary>Возвращает объект <see cref="T:System.Security.Principal.WindowsIdentity" />, представляющий идентификатор Windows для потока или процесса в зависимости от значения параметра <paramref name="ifImpersonating" />.</summary>
      <returns>Объект, представляющий пользователя Windows.</returns>
      <param name="ifImpersonating">Значение true для возврата объекта <see cref="T:System.Security.Principal.WindowsIdentity" />, только если олицетворение потока выполняется в данный момент; значение false для возврата объекта <see cref="T:System.Security.Principal.WindowsIdentity" /> потока, если олицетворение потока выполняется, или объекта <see cref="T:System.Security.Principal.WindowsIdentity" /> процесса, если олицетворение потока в настоящий момент не выполняется.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlPrincipal" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.Principal.WindowsIdentity.GetCurrent(System.Security.Principal.TokenAccessLevels)">
      <summary>Возвращает объект <see cref="T:System.Security.Principal.WindowsIdentity" />, представляющий текущего пользователя Windows, используя указанный требуемый уровень доступа к токену.</summary>
      <returns>Объект, представляющий текущего пользователя.</returns>
      <param name="desiredAccess">Побитовое сочетание значений перечисления. </param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="ControlPrincipal" />
      </PermissionSet>
    </member>
    <member name="P:System.Security.Principal.WindowsIdentity.Groups">
      <summary>Возвращает группы, к которым относится текущий пользователь Windows.</summary>
      <returns>Объект, представляющий группы, к которым принадлежит текущий пользователь Windows.</returns>
    </member>
    <member name="P:System.Security.Principal.WindowsIdentity.ImpersonationLevel">
      <summary>Возвращает уровень олицетворения для пользователя.</summary>
      <returns>Одно из значений перечисления, указывающее уровень олицетворения. </returns>
    </member>
    <member name="P:System.Security.Principal.WindowsIdentity.IsAnonymous">
      <summary>Возвращает значение, показывающее, определена ли в системе учетная запись пользователя как анонимная.</summary>
      <returns>Значение true, если учетная запись пользователя является анонимной; в противном случае — значение false.</returns>
    </member>
    <member name="P:System.Security.Principal.WindowsIdentity.IsGuest">
      <summary>Возвращает значение, показывающее, определена ли в системе учетная запись пользователя как учетная запись <see cref="F:System.Security.Principal.WindowsAccountType.Guest" />.</summary>
      <returns>Значение true, если учетная запись пользователя является учетной записью <see cref="F:System.Security.Principal.WindowsAccountType.Guest" />; в противном случае — значение false.</returns>
    </member>
    <member name="P:System.Security.Principal.WindowsIdentity.IsSystem">
      <summary>Возвращает значение, показывающее, определена ли в системе учетная запись пользователя как учетная запись <see cref="F:System.Security.Principal.WindowsAccountType.System" />.</summary>
      <returns>Значение true, если учетная запись пользователя является учетной записью <see cref="F:System.Security.Principal.WindowsAccountType.System" />; в противном случае — значение false.</returns>
    </member>
    <member name="P:System.Security.Principal.WindowsIdentity.Owner">
      <summary>Возвращает идентификатор безопасности (ИД безопасности) для владельца токена.</summary>
      <returns>Объект для владельца токена.</returns>
    </member>
    <member name="M:System.Security.Principal.WindowsIdentity.RunImpersonated(Microsoft.Win32.SafeHandles.SafeAccessTokenHandle,System.Action)">
      <summary>Выполняет указанное действие с олицетворенным удостоверением Windows.Вместо олицетворенного вызова метода и выполнения функции в контексте <see cref="T:System.Security.Principal.WindowsImpersonationContext" /> можно использовать метод <see cref="M:System.Security.Principal.WindowsIdentity.RunImpersonated(Microsoft.Win32.SafeHandles.SafeAccessTokenHandle,System.Action)" /> и предоставить функцию непосредственно в качестве параметра.</summary>
      <param name="safeAccessTokenHandle">Дескриптор SafeAccessTokenHandle олицетворенного удостоверения Windows.</param>
      <param name="action">System.Action для запуска. </param>
    </member>
    <member name="M:System.Security.Principal.WindowsIdentity.RunImpersonated``1(Microsoft.Win32.SafeHandles.SafeAccessTokenHandle,System.Func{``0})">
      <summary>Выполняет указанную функцию с олицетворенным удостоверением Windows.Вместо олицетворенного вызова метода и выполнения функции в контексте <see cref="T:System.Security.Principal.WindowsImpersonationContext" /> можно использовать метод <see cref="M:System.Security.Principal.WindowsIdentity.RunImpersonated(Microsoft.Win32.SafeHandles.SafeAccessTokenHandle,System.Action)" /> и предоставить функцию непосредственно в качестве параметра.</summary>
      <returns>Возвращает результат функции.</returns>
      <param name="safeAccessTokenHandle">Дескриптор SafeAccessTokenHandle олицетворенного удостоверения Windows.</param>
      <param name="func">System.Func для запуска.</param>
      <typeparam name="T">Тип объекта, который используется и возвращается функцией.</typeparam>
    </member>
    <member name="P:System.Security.Principal.WindowsIdentity.User">
      <summary>Возвращает идентификатор безопасности (ИД безопасности) для пользователя.</summary>
      <returns>Объект для пользователя.</returns>
    </member>
    <member name="T:System.Security.Principal.WindowsPrincipal">
      <summary>Включает код для проверки членства пользователя Windows в группе Windows.</summary>
    </member>
    <member name="M:System.Security.Principal.WindowsPrincipal.#ctor(System.Security.Principal.WindowsIdentity)">
      <summary>Инициализирует новый экземпляр класса <see cref="T:System.Security.Principal.WindowsPrincipal" /> указанным объектом <see cref="T:System.Security.Principal.WindowsIdentity" />.</summary>
      <param name="ntIdentity">Объект, из которого создается новый экземпляр <see cref="T:System.Security.Principal.WindowsPrincipal" />. </param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="ntIdentity" /> имеет значение null. </exception>
    </member>
    <member name="M:System.Security.Principal.WindowsPrincipal.IsInRole(System.Int32)">
      <summary>Определяет, относится ли текущий участник к группе пользователей Windows с заданным относительным идентификатором (RID).</summary>
      <returns>Значение true, если текущий субъект является членом заданной группы пользователей Windows (в конкретной роли); в противном случае — значение false.</returns>
      <param name="rid">RID группы пользователей Windows, в которой требуется проверить состояние членства участника. </param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.Principal.WindowsPrincipal.IsInRole(System.Security.Principal.SecurityIdentifier)">
      <summary>Определяет, относится ли текущий субъект к группе пользователей Windows с заданным идентификатором безопасности (SID).</summary>
      <returns>Значение true, если текущий участник является членом заданной группы пользователей Windows; в противном случае — значение false.</returns>
      <param name="sid">Объект <see cref="T:System.Security.Principal.SecurityIdentifier" />, уникально определяющий группу пользователей Windows.</param>
      <exception cref="T:System.ArgumentNullException">Параметр <paramref name="sid" /> имеет значение null.</exception>
      <exception cref="T:System.Security.SecurityException">Система Windows возвратила код ошибки Win32.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
    <member name="M:System.Security.Principal.WindowsPrincipal.IsInRole(System.Security.Principal.WindowsBuiltInRole)">
      <summary>Определяет, относится ли текущий субъект к группе пользователей Windows с заданным <see cref="T:System.Security.Principal.WindowsBuiltInRole" />.</summary>
      <returns>Значение true, если текущий участник является членом заданной группы пользователей Windows; в противном случае — значение false.</returns>
      <param name="role">Одно из значений <see cref="T:System.Security.Principal.WindowsBuiltInRole" />. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="role" /> не является допустимым значением <see cref="T:System.Security.Principal.WindowsBuiltInRole" />.</exception>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.ReflectionPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="MemberAccess" />
      </PermissionSet>
    </member>
  </members>
</doc>