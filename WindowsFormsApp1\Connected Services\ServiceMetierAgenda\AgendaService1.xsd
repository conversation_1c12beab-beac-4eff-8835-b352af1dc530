<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:tns="http://schemas.datacontract.org/2004/07/MetierRvMedical.Model" elementFormDefault="qualified" targetNamespace="http://schemas.datacontract.org/2004/07/MetierRvMedical.Model" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:complexType name="ArrayOfAgenda">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="Agenda" nillable="true" type="tns:Agenda" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfAgenda" nillable="true" type="tns:ArrayOfAgenda" />
  <xs:complexType name="Agenda">
    <xs:sequence>
      <xs:element minOccurs="0" name="Creneau" type="xs:int" />
      <xs:element minOccurs="0" name="Creneaux" nillable="true" type="tns:ArrayOfCreneau" />
      <xs:element minOccurs="0" name="DatePlanifie" nillable="true" type="xs:dateTime" />
      <xs:element minOccurs="0" name="HeureDebut" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="HeureFin" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="IdAgenda" type="xs:int" />
      <xs:element minOccurs="0" name="IdMedecin" nillable="true" type="xs:int" />
      <xs:element minOccurs="0" name="Lieu" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Medecin" nillable="true" type="tns:Medecin" />
      <xs:element minOccurs="0" name="RendezVous" nillable="true" type="tns:ArrayOfRendezVous" />
      <xs:element minOccurs="0" name="Titre" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="statut" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="Agenda" nillable="true" type="tns:Agenda" />
  <xs:complexType name="ArrayOfCreneau">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="Creneau" nillable="true" type="tns:Creneau" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfCreneau" nillable="true" type="tns:ArrayOfCreneau" />
  <xs:complexType name="Creneau">
    <xs:sequence>
      <xs:element minOccurs="0" name="Agenda" nillable="true" type="tns:Agenda" />
      <xs:element minOccurs="0" name="Date" type="xs:dateTime" />
      <xs:element minOccurs="0" name="Disponible" type="xs:boolean" />
      <xs:element minOccurs="0" name="HeureDebut" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="HeureFin" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="IdAgenda" type="xs:int" />
      <xs:element minOccurs="0" name="IdCreneau" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="Creneau" nillable="true" type="tns:Creneau" />
  <xs:complexType name="Medecin">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:Utilisateur">
        <xs:sequence>
          <xs:element minOccurs="0" name="Agendas" nillable="true" type="tns:ArrayOfAgenda" />
          <xs:element minOccurs="0" name="IdSpecialite" nillable="true" type="xs:int" />
          <xs:element minOccurs="0" name="NumeroOrdre" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="Specialite" nillable="true" type="tns:Specialite" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="Medecin" nillable="true" type="tns:Medecin" />
  <xs:complexType name="Utilisateur">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:Personne">
        <xs:sequence>
          <xs:element minOccurs="0" name="IdRole" nillable="true" type="xs:int" />
          <xs:element minOccurs="0" name="MotDePasse" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="Role" nillable="true" type="tns:Role" />
          <xs:element minOccurs="0" name="identifiant" nillable="true" type="xs:string" />
          <xs:element minOccurs="0" name="statut" nillable="true" type="xs:boolean" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="Utilisateur" nillable="true" type="tns:Utilisateur" />
  <xs:complexType name="Personne">
    <xs:sequence>
      <xs:element minOccurs="0" name="Adresse" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Email" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="IdU" type="xs:int" />
      <xs:element minOccurs="0" name="NomPrenom" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="TEL" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="Personne" nillable="true" type="tns:Personne" />
  <xs:complexType name="Role">
    <xs:sequence>
      <xs:element minOccurs="0" name="Code" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Description" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Id" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="Role" nillable="true" type="tns:Role" />
  <xs:complexType name="Specialite">
    <xs:sequence>
      <xs:element minOccurs="0" name="CodeSpecialite" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Id" type="xs:int" />
      <xs:element minOccurs="0" name="NomSpecialite" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="Specialite" nillable="true" type="tns:Specialite" />
  <xs:complexType name="ArrayOfRendezVous">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="RendezVous" nillable="true" type="tns:RendezVous" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfRendezVous" nillable="true" type="tns:ArrayOfRendezVous" />
  <xs:complexType name="RendezVous">
    <xs:sequence>
      <xs:element minOccurs="0" name="Creneau" nillable="true" type="tns:Creneau" />
      <xs:element minOccurs="0" name="DateRv" type="xs:dateTime" />
      <xs:element minOccurs="0" name="IdCreneau" nillable="true" type="xs:int" />
      <xs:element minOccurs="0" name="IdMedecin" nillable="true" type="xs:int" />
      <xs:element minOccurs="0" name="IdPatient" nillable="true" type="xs:int" />
      <xs:element minOccurs="0" name="IdRv" type="xs:int" />
      <xs:element minOccurs="0" name="IdSoin" nillable="true" type="xs:int" />
      <xs:element minOccurs="0" name="Medecin" nillable="true" type="tns:Medecin" />
      <xs:element minOccurs="0" name="Patient" nillable="true" type="tns:Patient" />
      <xs:element minOccurs="0" name="Soin" nillable="true" type="tns:Soin" />
      <xs:element minOccurs="0" name="Statut" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="RendezVous" nillable="true" type="tns:RendezVous" />
  <xs:complexType name="Patient">
    <xs:complexContent mixed="false">
      <xs:extension base="tns:Personne">
        <xs:sequence>
          <xs:element minOccurs="0" name="DateNaissance" nillable="true" type="xs:dateTime" />
          <xs:element minOccurs="0" name="GroupeSanguin" nillable="true" type="tns:GroupeSanguin" />
          <xs:element minOccurs="0" name="IdGroupeSanguin" type="xs:int" />
          <xs:element minOccurs="0" name="Poids" nillable="true" type="xs:float" />
          <xs:element minOccurs="0" name="Taille" nillable="true" type="xs:float" />
        </xs:sequence>
      </xs:extension>
    </xs:complexContent>
  </xs:complexType>
  <xs:element name="Patient" nillable="true" type="tns:Patient" />
  <xs:complexType name="GroupeSanguin">
    <xs:sequence>
      <xs:element minOccurs="0" name="CodeGroupeSanguin" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="IdGroupeSanguin" type="xs:int" />
      <xs:element minOccurs="0" name="NomGroupeSanguin" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="GroupeSanguin" nillable="true" type="tns:GroupeSanguin" />
  <xs:complexType name="Soin">
    <xs:sequence>
      <xs:element minOccurs="0" name="Category" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Duration" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="IdSoin" type="xs:int" />
      <xs:element minOccurs="0" name="NameSoin" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Price" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="Soin" nillable="true" type="tns:Soin" />
</xs:schema>