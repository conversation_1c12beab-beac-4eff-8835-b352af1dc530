<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:wsam="http://www.w3.org/2007/05/addressing/metadata" xmlns:wsx="http://schemas.xmlsoap.org/ws/2004/09/mex" xmlns:wsap="http://schemas.xmlsoap.org/ws/2004/08/addressing/policy" xmlns:msc="http://schemas.microsoft.com/ws/2005/12/wsdl/contract" xmlns:wsp="http://schemas.xmlsoap.org/ws/2004/09/policy" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:tns="http://tempuri.org/" xmlns:wsa10="http://www.w3.org/2005/08/addressing" xmlns:wsaw="http://www.w3.org/2006/05/addressing/wsdl" xmlns:wsa="http://schemas.xmlsoap.org/ws/2004/08/addressing" name="GeneralService" targetNamespace="http://tempuri.org/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types>
    <xsd:schema targetNamespace="http://tempuri.org/Imports">
      <xsd:import schemaLocation="http://localhost:60827/Wcf/GeneralService.svc?xsd=xsd0" namespace="http://tempuri.org/" />
      <xsd:import schemaLocation="http://localhost:60827/Wcf/GeneralService.svc?xsd=xsd1" namespace="http://schemas.microsoft.com/2003/10/Serialization/" />
      <xsd:import schemaLocation="http://localhost:60827/Wcf/GeneralService.svc?xsd=xsd2" namespace="http://schemas.microsoft.com/2003/10/Serialization/Arrays" />
      <xsd:import schemaLocation="http://localhost:60827/Wcf/GeneralService.svc?xsd=xsd3" namespace="http://schemas.datacontract.org/2004/07/MetierRvMedical.Model" />
    </xsd:schema>
  </wsdl:types>
  <wsdl:message name="IGeneralService_GetPhoneNumbersForAutoComplete_InputMessage">
    <wsdl:part name="parameters" element="tns:GetPhoneNumbersForAutoComplete" />
  </wsdl:message>
  <wsdl:message name="IGeneralService_GetPhoneNumbersForAutoComplete_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetPhoneNumbersForAutoCompleteResponse" />
  </wsdl:message>
  <wsdl:message name="IGeneralService_GetListSoins_InputMessage">
    <wsdl:part name="parameters" element="tns:GetListSoins" />
  </wsdl:message>
  <wsdl:message name="IGeneralService_GetListSoins_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetListSoinsResponse" />
  </wsdl:message>
  <wsdl:message name="IGeneralService_GetListeGroupesSanguins_InputMessage">
    <wsdl:part name="parameters" element="tns:GetListeGroupesSanguins" />
  </wsdl:message>
  <wsdl:message name="IGeneralService_GetListeGroupesSanguins_OutputMessage">
    <wsdl:part name="parameters" element="tns:GetListeGroupesSanguinsResponse" />
  </wsdl:message>
  <wsdl:portType name="IGeneralService">
    <wsdl:operation name="GetPhoneNumbersForAutoComplete">
      <wsdl:input wsaw:Action="http://tempuri.org/IGeneralService/GetPhoneNumbersForAutoComplete" message="tns:IGeneralService_GetPhoneNumbersForAutoComplete_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IGeneralService/GetPhoneNumbersForAutoCompleteResponse" message="tns:IGeneralService_GetPhoneNumbersForAutoComplete_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetListSoins">
      <wsdl:input wsaw:Action="http://tempuri.org/IGeneralService/GetListSoins" message="tns:IGeneralService_GetListSoins_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IGeneralService/GetListSoinsResponse" message="tns:IGeneralService_GetListSoins_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="GetListeGroupesSanguins">
      <wsdl:input wsaw:Action="http://tempuri.org/IGeneralService/GetListeGroupesSanguins" message="tns:IGeneralService_GetListeGroupesSanguins_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IGeneralService/GetListeGroupesSanguinsResponse" message="tns:IGeneralService_GetListeGroupesSanguins_OutputMessage" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="BasicHttpBinding_IGeneralService" type="tns:IGeneralService">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="GetPhoneNumbersForAutoComplete">
      <soap:operation soapAction="http://tempuri.org/IGeneralService/GetPhoneNumbersForAutoComplete" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetListSoins">
      <soap:operation soapAction="http://tempuri.org/IGeneralService/GetListSoins" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="GetListeGroupesSanguins">
      <soap:operation soapAction="http://tempuri.org/IGeneralService/GetListeGroupesSanguins" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="GeneralService">
    <wsdl:port name="BasicHttpBinding_IGeneralService" binding="tns:BasicHttpBinding_IGeneralService">
      <soap:address location="http://localhost:60827/Wcf/GeneralService.svc" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>