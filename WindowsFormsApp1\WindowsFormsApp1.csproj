<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="packages\EntityFramework.6.5.1\build\EntityFramework.props" Condition="Exists('packages\EntityFramework.6.5.1\build\EntityFramework.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{A7CFEF9B-0DFE-409E-A848-57BB45CB838E}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>WindowsFormsApp1</RootNamespace>
    <AssemblyName>WindowsFormsApp1</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="BouncyCastle.Crypto, Version=1.9.0.0, Culture=neutral, PublicKeyToken=0e99375e54769942, processorArchitecture=MSIL">
      <HintPath>packages\Portable.BouncyCastle.1.9.0\lib\net40\BouncyCastle.Crypto.dll</HintPath>
    </Reference>
    <Reference Include="BouncyCastle.Cryptography, Version=2.0.0.0, Culture=neutral, PublicKeyToken=072edcf4a5328938, processorArchitecture=MSIL">
      <HintPath>packages\BouncyCastle.Cryptography.2.6.1\lib\net461\BouncyCastle.Cryptography.dll</HintPath>
    </Reference>
    <Reference Include="CrystalDecisions.CrystalReports.Engine, Version=13.0.4000.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=MSIL" />
    <Reference Include="CrystalDecisions.ReportSource, Version=13.0.4000.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=MSIL" />
    <Reference Include="CrystalDecisions.Shared, Version=13.0.4000.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=MSIL" />
    <Reference Include="CrystalDecisions.Windows.Forms, Version=13.0.4000.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=MSIL" />
    <Reference Include="EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>packages\EntityFramework.6.5.1\lib\net45\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>packages\EntityFramework.6.5.1\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="Google.Protobuf, Version=3.31.0.0, Culture=neutral, PublicKeyToken=a7d26565bac4d604, processorArchitecture=MSIL">
      <HintPath>packages\Google.Protobuf.3.31.0\lib\net45\Google.Protobuf.dll</HintPath>
    </Reference>
    <Reference Include="K4os.Compression.LZ4, Version=1.3.8.0, Culture=neutral, PublicKeyToken=2186fa9121ef231d, processorArchitecture=MSIL">
      <HintPath>packages\K4os.Compression.LZ4.1.3.8\lib\net462\K4os.Compression.LZ4.dll</HintPath>
    </Reference>
    <Reference Include="K4os.Compression.LZ4.Streams, Version=1.3.8.0, Culture=neutral, PublicKeyToken=2186fa9121ef231d, processorArchitecture=MSIL">
      <HintPath>packages\K4os.Compression.LZ4.Streams.1.3.8\lib\net462\K4os.Compression.LZ4.Streams.dll</HintPath>
    </Reference>
    <Reference Include="K4os.Hash.xxHash, Version=1.0.8.0, Culture=neutral, PublicKeyToken=32cd54395057cec3, processorArchitecture=MSIL">
      <HintPath>packages\K4os.Hash.xxHash.1.0.8\lib\net462\K4os.Hash.xxHash.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.Bcl.AsyncInterfaces.9.0.5\lib\net462\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Configuration, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.Extensions.Configuration.9.0.5\lib\net462\Microsoft.Extensions.Configuration.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Configuration.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.Extensions.Configuration.Abstractions.9.0.5\lib\net462\Microsoft.Extensions.Configuration.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Configuration.Binder, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.Extensions.Configuration.Binder.9.0.5\lib\net462\Microsoft.Extensions.Configuration.Binder.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.DependencyInjection, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.Extensions.DependencyInjection.9.0.5\lib\net462\Microsoft.Extensions.DependencyInjection.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.DependencyInjection.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.Extensions.DependencyInjection.Abstractions.9.0.5\lib\net462\Microsoft.Extensions.DependencyInjection.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Logging, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.Extensions.Logging.9.0.5\lib\net462\Microsoft.Extensions.Logging.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Logging.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.Extensions.Logging.Abstractions.9.0.5\lib\net462\Microsoft.Extensions.Logging.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Options, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.Extensions.Options.9.0.5\lib\net462\Microsoft.Extensions.Options.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Primitives, Version=*******, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>packages\Microsoft.Extensions.Primitives.9.0.5\lib\net462\Microsoft.Extensions.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.VisualBasic" />
    <Reference Include="MySql.Data, Version=9.1.0.0, Culture=neutral, PublicKeyToken=c5687fc88969c44d, processorArchitecture=MSIL">
      <HintPath>packages\MySql.Data.9.1.0\lib\net48\MySql.Data.dll</HintPath>
    </Reference>
    <Reference Include="MySql.Data.EntityFramework, Version=9.1.0.0, Culture=neutral, PublicKeyToken=c5687fc88969c44d, processorArchitecture=MSIL">
      <HintPath>packages\MySql.Data.EntityFramework.9.1.0\lib\net48\MySql.Data.EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="Renci.SshNet, Version=2025.0.0.1, Culture=neutral, PublicKeyToken=1cee9f8bde3db106, processorArchitecture=MSIL">
      <HintPath>packages\SSH.NET.2025.0.0\lib\net462\Renci.SshNet.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Buffers, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\System.Buffers.4.6.1\lib\net462\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections.Immutable, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Collections.Immutable.9.0.5\lib\net462\System.Collections.Immutable.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel" />
    <Reference Include="System.ComponentModel.Annotations, Version=4.2.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.ComponentModel.Annotations.5.0.0\lib\net461\System.ComponentModel.Annotations.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Composition" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Configuration.ConfigurationManager, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\System.Configuration.ConfigurationManager.9.0.5\lib\net462\System.Configuration.ConfigurationManager.dll</HintPath>
    </Reference>
    <Reference Include="System.Configuration.Install" />
    <Reference Include="System.Core" />
    <Reference Include="System.Diagnostics.DiagnosticSource, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\System.Diagnostics.DiagnosticSource.9.0.5\lib\net462\System.Diagnostics.DiagnosticSource.dll</HintPath>
    </Reference>
    <Reference Include="System.Drawing.Design" />
    <Reference Include="System.Formats.Asn1, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\System.Formats.Asn1.9.0.5\lib\net462\System.Formats.Asn1.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Pipelines, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\System.IO.Pipelines.9.0.5\lib\net462\System.IO.Pipelines.dll</HintPath>
    </Reference>
    <Reference Include="System.Management" />
    <Reference Include="System.Memory, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\System.Memory.4.6.3\lib\net462\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Numerics.Vectors.4.6.1\lib\net462\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Runtime.4.3.1\lib\net462\System.Runtime.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=6.0.3.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>packages\System.Runtime.CompilerServices.Unsafe.6.1.2\lib\net462\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Security" />
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.Threading.Tasks.Extensions, Version=4.2.4.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>packages\System.Threading.Tasks.Extensions.4.6.3\lib\net462\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Transactions" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="ZstdSharp, Version=0.8.5.0, Culture=neutral, PublicKeyToken=8d151af33a4ad5cf, processorArchitecture=MSIL">
      <HintPath>packages\ZstdSharp.Port.0.8.5\lib\net462\ZstdSharp.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Connected Services\ServiceMetierAgenda\Reference.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Compile>
    <Compile Include="Connected Services\ServiceMetierGeneral\Reference.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Compile>
    <Compile Include="Connected Services\ServiceMetierPatient\Reference.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Compile>
    <Compile Include="Connected Services\ServiceMetier\Reference.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Reference.svcmap</DependentUpon>
    </Compile>
    <Compile Include="Error\erreur1.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>erreur.txt</DependentUpon>
    </Compile>
    <Compile Include="Form1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form1.Designer.cs">
      <DependentUpon>Form1.cs</DependentUpon>
    </Compile>
    <Compile Include="frmConnexion.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmConnexion.Designer.cs">
      <DependentUpon>frmConnexion.cs</DependentUpon>
    </Compile>
    <Compile Include="frmMDI.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="frmMDI.Designer.cs">
      <DependentUpon>frmMDI.cs</DependentUpon>
    </Compile>
    <Compile Include="Helper\CryptString.cs" />
    <Compile Include="Helper\GMailer.cs" />
    <Compile Include="Helper\Utils.cs" />
    <Compile Include="Migrations\Configuration.cs" />
    <Compile Include="Model\Admin.cs" />
    <Compile Include="Model\Agenda.cs" />
    <Compile Include="Model\BdRvMedicalContext.cs" />
    <Compile Include="Model\ClassReportTicketRv.cs" />
    <Compile Include="Model\Creneau.cs" />
    <Compile Include="Model\GroupeSanguin.cs" />
    <Compile Include="Model\Medecin.cs" />
    <Compile Include="Model\MoyenPayment.cs" />
    <Compile Include="Model\Paiement.cs" />
    <Compile Include="Model\Patient.cs" />
    <Compile Include="Model\Personne.cs" />
    <Compile Include="Model\RendezVous.cs" />
    <Compile Include="Model\Role.cs" />
    <Compile Include="Model\Secretaire.cs" />
    <Compile Include="Model\SelectListView.cs" />
    <Compile Include="Model\Soin.cs" />
    <Compile Include="Model\Specialite.cs" />
    <Compile Include="Model\Td_Erreur.cs" />
    <Compile Include="Model\Utilisateur.cs" />
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Report\rptTicketRv.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>rptTicketRv.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="View\frmAgenda.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="View\frmAgenda.Designer.cs">
      <DependentUpon>frmAgenda.cs</DependentUpon>
    </Compile>
    <Compile Include="View\frmMedecins.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="View\frmMedecins.Designer.cs">
      <DependentUpon>frmMedecins.cs</DependentUpon>
    </Compile>
    <Compile Include="View\frmPatients.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="View\frmPatients.Designer.cs">
      <DependentUpon>frmPatients.cs</DependentUpon>
    </Compile>
    <Compile Include="View\frmPrintTicket.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="View\frmPrintTicket.Designer.cs">
      <DependentUpon>frmPrintTicket.cs</DependentUpon>
    </Compile>
    <Compile Include="View\frmRendezVous2.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="View\frmRendezVous2.Designer.cs">
      <DependentUpon>frmRendezVous2.cs</DependentUpon>
    </Compile>
    <Compile Include="View\frmRendezVous.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="View\frmRendezVous.Designer.cs">
      <DependentUpon>frmRendezVous.cs</DependentUpon>
    </Compile>
    <Compile Include="View\frmTest.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="View\frmTest.Designer.cs">
      <DependentUpon>frmTest.cs</DependentUpon>
    </Compile>
    <EmbeddedResource Include="Form1.resx">
      <DependentUpon>Form1.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmConnexion.resx">
      <DependentUpon>frmConnexion.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="frmMDI.resx">
      <DependentUpon>frmMDI.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <EmbeddedResource Include="Report\rptTicketRv.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>rptTicketRv.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="View\frmAgenda.resx">
      <DependentUpon>frmAgenda.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="View\frmMedecins.resx">
      <DependentUpon>frmMedecins.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="View\frmPatients.resx">
      <DependentUpon>frmPatients.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="View\frmPrintTicket.resx">
      <DependentUpon>frmPrintTicket.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="View\frmRendezVous2.resx">
      <DependentUpon>frmRendezVous2.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="View\frmRendezVous.resx">
      <DependentUpon>frmRendezVous.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="View\frmTest.resx">
      <DependentUpon>frmTest.cs</DependentUpon>
    </EmbeddedResource>
    <None Include="Connected Services\ServiceMetierAgenda\AgendaService.wsdl" />
    <None Include="Connected Services\ServiceMetierAgenda\AgendaService.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Connected Services\ServiceMetierAgenda\AgendaService1.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Connected Services\ServiceMetierAgenda\AgendaService2.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Connected Services\ServiceMetierAgenda\AgendaService3.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Connected Services\ServiceMetierAgenda\WindowsFormsApp1.ServiceMetierAgenda.Agenda.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Connected Services\ServiceMetierGeneral\GeneralService.wsdl" />
    <None Include="Connected Services\ServiceMetierGeneral\GeneralService.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Connected Services\ServiceMetierGeneral\GeneralService1.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Connected Services\ServiceMetierGeneral\GeneralService2.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Connected Services\ServiceMetierGeneral\GeneralService3.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Connected Services\ServiceMetierGeneral\WindowsFormsApp1.ServiceMetierGeneral.GroupeSanguin.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Connected Services\ServiceMetierGeneral\WindowsFormsApp1.ServiceMetierGeneral.Soin.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Connected Services\ServiceMetierPatient\PatientService.wsdl" />
    <None Include="Connected Services\ServiceMetierPatient\PatientService.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Connected Services\ServiceMetierPatient\PatientService1.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Connected Services\ServiceMetierPatient\PatientService2.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Connected Services\ServiceMetierPatient\WindowsFormsApp1.ServiceMetierPatient.GroupeSanguin.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Connected Services\ServiceMetierPatient\WindowsFormsApp1.ServiceMetierPatient.Patient.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Connected Services\ServiceMetier\Service1.wsdl" />
    <None Include="Connected Services\ServiceMetier\Service1.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Connected Services\ServiceMetier\Service11.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Connected Services\ServiceMetier\Service12.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Connected Services\ServiceMetier\Service13.xsd">
      <SubType>Designer</SubType>
    </None>
    <None Include="Connected Services\ServiceMetier\WindowsFormsApp1.ServiceMetier.Agenda.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Connected Services\ServiceMetier\WindowsFormsApp1.ServiceMetier.CompositeType.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="Connected Services\ServiceMetier\WindowsFormsApp1.ServiceMetier.GroupeSanguin.datasource">
      <DependentUpon>Reference.svcmap</DependentUpon>
    </None>
    <None Include="packages.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\istockphoto-**********-612x612.jpg" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\WhatsApp-Image-2024-11-06-at-4.54.45-PM.jpeg" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\abstract-health-medical-science-healthcare-icon-digital-technology-science-concept-modern-innovation-treatment-medicine-on-hi-tech-future-blue-background-for-wallpaper-template-web-design-vec.jpg" />
  </ItemGroup>
  <ItemGroup>
    <Service Include="{508349B6-6B84-4DF5-91F0-309BEEBAD82D}" />
    <Service Include="{C0C07587-41A7-46C8-8FBD-3F9C8EBE2DDC}" />
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Connected Services\" />
  </ItemGroup>
  <ItemGroup>
    <WCFMetadataStorage Include="Connected Services\ServiceMetierAgenda\" />
    <WCFMetadataStorage Include="Connected Services\ServiceMetierGeneral\" />
    <WCFMetadataStorage Include="Connected Services\ServiceMetierPatient\" />
    <WCFMetadataStorage Include="Connected Services\ServiceMetier\" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Connected Services\ServiceMetier\Service1.disco" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Connected Services\ServiceMetier\configuration91.svcinfo" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Connected Services\ServiceMetier\configuration.svcinfo" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Connected Services\ServiceMetier\Reference.svcmap">
      <Generator>WCF Proxy Generator</Generator>
      <LastGenOutput>Reference.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <ItemGroup>
    <None Include="Connected Services\ServiceMetierPatient\PatientService.disco" />
    <None Include="Connected Services\ServiceMetierPatient\configuration91.svcinfo" />
    <None Include="Connected Services\ServiceMetierPatient\configuration.svcinfo" />
    <None Include="Connected Services\ServiceMetierPatient\Reference.svcmap">
      <Generator>WCF Proxy Generator</Generator>
      <LastGenOutput>Reference.cs</LastGenOutput>
    </None>
    <None Include="Connected Services\ServiceMetierGeneral\GeneralService.disco" />
    <None Include="Connected Services\ServiceMetierGeneral\configuration91.svcinfo" />
    <None Include="Connected Services\ServiceMetierGeneral\configuration.svcinfo" />
    <None Include="Connected Services\ServiceMetierGeneral\Reference.svcmap">
      <Generator>WCF Proxy Generator</Generator>
      <LastGenOutput>Reference.cs</LastGenOutput>
    </None>
    <None Include="Connected Services\ServiceMetierAgenda\AgendaService.disco" />
    <None Include="Connected Services\ServiceMetierAgenda\configuration91.svcinfo" />
    <None Include="Connected Services\ServiceMetierAgenda\configuration.svcinfo" />
    <None Include="Connected Services\ServiceMetierAgenda\Reference.svcmap">
      <Generator>WCF Proxy Generator</Generator>
      <LastGenOutput>Reference.cs</LastGenOutput>
    </None>
    <Content Include="Error\erreur.txt">
      <Generator>TextTemplatingFileGenerator</Generator>
      <LastGenOutput>erreur1.cs</LastGenOutput>
    </Content>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>Ce projet fait référence à des packages NuGet qui sont manquants sur cet ordinateur. Utilisez l'option de restauration des packages NuGet pour les télécharger. Pour plus d'informations, consultez http://go.microsoft.com/fwlink/?LinkID=322105. Le fichier manquant est : {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('packages\EntityFramework.6.5.1\build\EntityFramework.props')" Text="$([System.String]::Format('$(ErrorText)', 'packages\EntityFramework.6.5.1\build\EntityFramework.props'))" />
    <Error Condition="!Exists('packages\EntityFramework.6.5.1\build\EntityFramework.targets')" Text="$([System.String]::Format('$(ErrorText)', 'packages\EntityFramework.6.5.1\build\EntityFramework.targets'))" />
    <Error Condition="!Exists('packages\System.ValueTuple.4.6.1\build\net471\System.ValueTuple.targets')" Text="$([System.String]::Format('$(ErrorText)', 'packages\System.ValueTuple.4.6.1\build\net471\System.ValueTuple.targets'))" />
  </Target>
  <Import Project="packages\EntityFramework.6.5.1\build\EntityFramework.targets" Condition="Exists('packages\EntityFramework.6.5.1\build\EntityFramework.targets')" />
  <Import Project="packages\System.ValueTuple.4.6.1\build\net471\System.ValueTuple.targets" Condition="Exists('packages\System.ValueTuple.4.6.1\build\net471\System.ValueTuple.targets')" />
</Project>