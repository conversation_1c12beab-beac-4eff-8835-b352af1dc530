﻿<?xml version="1.0" encoding="utf-8" ?>
<configuration>
	<configSections>
		<section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
	</configSections>

	<connectionStrings>
		<add name="bdRvMedicalContext" providerName="MySql.Data.MySqlClient" connectionString="server=localhost;port=3306;database=metierbdrvmedical;uid=root;password=******" />
	</connectionStrings>

	<entityFramework>
		<defaultConnectionFactory type="MySql.Data.MySqlClient.MySqlClientFactory, MySql.Data" />
		<providers>
			<provider invariantName="MySql.Data.MySqlClient" type="MySql.Data.MySqlClient.MySqlProviderServices, MySql.Data.EntityFramework, Version=*******, Culture=neutral, PublicKeyToken=c5687fc88969c44d" />
		</providers>
	</entityFramework>

	<runtime>
		<assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
			<dependentAssembly>
				<assemblyIdentity name="MySql.Data" publicKeyToken="c5687fc88969c44d" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******" />
			</dependentAssembly>
			<!-- autres bindingRedirect si nécessaire -->
		</assemblyBinding>
	</runtime>
</configuration>
