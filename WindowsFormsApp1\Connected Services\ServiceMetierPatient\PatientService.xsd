<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:tns="http://tempuri.org/" elementFormDefault="qualified" targetNamespace="http://tempuri.org/" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:import schemaLocation="http://localhost:60827/Wcf/PatientService.svc?xsd=xsd2" namespace="http://schemas.datacontract.org/2004/07/MetierRvMedical.Model" />
  <xs:element name="GetListePatients">
    <xs:complexType>
      <xs:sequence />
    </xs:complexType>
  </xs:element>
  <xs:element name="GetListePatientsResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q1="http://schemas.datacontract.org/2004/07/MetierRvMedical.Model" minOccurs="0" name="GetListePatientsResult" nillable="true" type="q1:ArrayOfPatient" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="AddPatient">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q2="http://schemas.datacontract.org/2004/07/MetierRvMedical.Model" minOccurs="0" name="patient" nillable="true" type="q2:Patient" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="AddPatientResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="AddPatientResult" type="xs:boolean" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="UpdatePatient">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q3="http://schemas.datacontract.org/2004/07/MetierRvMedical.Model" minOccurs="0" name="patient" nillable="true" type="q3:Patient" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="UpdatePatientResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="UpdatePatientResult" type="xs:boolean" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="RemovePatient">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q4="http://schemas.datacontract.org/2004/07/MetierRvMedical.Model" minOccurs="0" name="patient" nillable="true" type="q4:Patient" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="RemovePatientResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="RemovePatientResult" type="xs:boolean" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetListeGroupesSanguins">
    <xs:complexType>
      <xs:sequence />
    </xs:complexType>
  </xs:element>
  <xs:element name="GetListeGroupesSanguinsResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q5="http://schemas.datacontract.org/2004/07/MetierRvMedical.Model" minOccurs="0" name="GetListeGroupesSanguinsResult" nillable="true" type="q5:ArrayOfGroupeSanguin" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="ResearchPatient">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="phoneNumberInput" nillable="true" type="xs:string" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="ResearchPatientResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q6="http://schemas.datacontract.org/2004/07/MetierRvMedical.Model" minOccurs="0" name="ResearchPatientResult" nillable="true" type="q6:Patient" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
</xs:schema>