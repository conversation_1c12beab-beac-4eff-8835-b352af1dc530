
                     ___
                    / __|
         ___  ___  | |__
        | __|| __| |  _ \
        | _| | _|  | |_| |
        |___||_|    \___/

TOPIC
    about_EntityFramework6

SHORT DESCRIPTION
    Provides information about Entity Framework commands.

LONG DESCRIPTION
    This topic describes the Entity Framework commands. Entity Framework is
    Microsoft's recommended data access technology for new applications.

    The following Entity Framework cmdlets are used with Entity Framework
    Migrations.

        Cmdlet              Description
        -----------------   ---------------------------------------------------
        Enable-Migrations   Enables Code First Migrations in a project.

        Add-Migration       Scaffolds a migration script for any pending model
                            changes.

        Update-Database     Applies any pending migrations to the database.

        Get-Migrations      Displays the migrations that have been applied to
                            the target database.

    The following Entity Framework cmdlets are used by NuGet packages that
    install Entity Framework providers. These commands are not usually used as
    part of normal application development.

        Cmdlet                          Description
        ------------------------------  ---------------------------------------
        Add-EFProvider                  Adds or updates an Entity Framework
                                        provider entry in the project config
                                        file.

        Add-EFDefaultConnectionFactory  Adds or updates an Entity Framework
                                        default connection factory in the
                                        project config file.

SEE ALSO
    Enable-Migrations
    Add-Migration
    Update-Database
    Get-Migrations
