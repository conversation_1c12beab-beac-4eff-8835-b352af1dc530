﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>Microsoft.VisualBasic</name>
  </assembly>
  <members>
    <member name="T:Microsoft.VisualBasic.CallType">
      <summary>G<PERSON><PERSON> den Typ der Prozedur an, die beim Aufrufen der CallByName-Funktion aufgerufen wird.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:Microsoft.VisualBasic.CallType.Get">
      <summary>Ein Eigenschaftswert wird abgerufen.  Dieser Member entspricht der Konstante vbGet in Visual Basic.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.CallType.Let">
      <summary>Ein Objekteigenschaftswert wird festgelegt.Dieser Member entspricht der Konstante vbLet in Visual Basic.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.CallType.Method">
      <summary>Eine Methode wird aufgerufen.  Dieser Member entspricht der Konstante vbMethod in Visual Basic.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.CallType.Set">
      <summary>Ein Eigenschaftswert wird festgelegt.  Dieser Member entspricht der Konstante vbSet in Visual Basic.</summary>
    </member>
    <member name="T:Microsoft.VisualBasic.Constants">
      <summary>Das Constants-Modul enthält verschiedene Konstanten.Diese Konstanten können an einer beliebigen Stelle im Code verwendet werden.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbBack">
      <summary>Stellt ein Rücktastenzeichen für Druck- und Anzeigefunktionen dar.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbCr">
      <summary>Stellt ein Wagenrücklaufzeichen für Druck- und Anzeigefunktionen dar.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbCrLf">
      <summary>Stellt ein Wagenrücklaufzeichen mit einem Zeilenvorschubzeichen für Druck- und Anzeigefunktionen dar.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbFormFeed">
      <summary>Stellt ein Seitenvorschubzeichen für Druckfunktionen dar.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbLf">
      <summary>Stellt ein Zeilenvorschubzeichen für Druck- und Anzeigefunktionen dar.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbNewLine">
      <summary>Stellt ein Zeilenumbruchzeichen für Druck- und Anzeigefunktionen dar.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbNullChar">
      <summary>Stellt ein NULL-Zeichen für Druck- und Anzeigefunktionen dar.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbNullString">
      <summary>Stellt eine Zeichenfolge der Länge 0 (null) für Druck- und Anzeigefunktionen sowie zum Aufrufen externer Prozeduren dar.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbTab">
      <summary>Stellt ein Tabulatorzeichen für Druck- und Anzeigefunktionen dar.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbVerticalTab">
      <summary>Stellt ein Wagenrücklaufzeichen für Druckfunktionen dar.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:Microsoft.VisualBasic.HideModuleNameAttribute">
      <summary>Wenn das HideModuleNameAttribute-Attribut auf ein Modul angewendet wird, sind für den Zugriff auf die Member des Moduls nur die Zugriffsberechtigungen für das Modul erforderlich.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:Microsoft.VisualBasic.HideModuleNameAttribute.#ctor">
      <summary>Initialisiert eine neue Instanz des <see cref="T:Microsoft.VisualBasic.HideModuleNameAttribute" />-Attributs. </summary>
    </member>
    <member name="T:Microsoft.VisualBasic.Strings">
      <summary>Das Strings-Modul enthält Prozeduren, mit denen Zeichenfolgenoperationen ausgeführt werden. </summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:Microsoft.VisualBasic.Strings.AscW(System.Char)">
      <summary>Gibt einen Integer-Wert zurück, der den Zeichencode darstellt, der einem Zeichen entspricht.</summary>
      <returns>Gibt einen Integer-Wert zurück, der den Zeichencode darstellt, der einem Zeichen entspricht.</returns>
      <param name="String">Erforderlich.Alle gültigen Char-Ausdrücke oder String-Ausdrücke.Wenn <paramref name="String" /> ein String-Ausdruck ist, wird nur das erste Zeichen der Zeichenfolge als Eingabe verwendet.Wenn <paramref name="String" />Nothing ist oder keine Zeichen enthält, tritt ein <see cref="T:System.ArgumentException" />-Fehler auf.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:Microsoft.VisualBasic.Strings.AscW(System.String)">
      <summary>Gibt einen Integer-Wert zurück, der den Zeichencode darstellt, der einem Zeichen entspricht.</summary>
      <returns>Gibt einen Integer-Wert zurück, der den Zeichencode darstellt, der einem Zeichen entspricht.</returns>
      <param name="String">Erforderlich.Alle gültigen Char-Ausdrücke oder String-Ausdrücke.Wenn <paramref name="String" /> ein String-Ausdruck ist, wird nur das erste Zeichen der Zeichenfolge als Eingabe verwendet.Wenn <paramref name="String" />Nothing ist oder keine Zeichen enthält, tritt ein <see cref="T:System.ArgumentException" />-Fehler auf.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:Microsoft.VisualBasic.Strings.ChrW(System.Int32)">
      <summary>Gibt das dem angegebenen Zeichencode zugeordnete Zeichen zurück.</summary>
      <returns>Gibt das dem angegebenen Zeichencode zugeordnete Zeichen zurück.</returns>
      <param name="CharCode">Erforderlich.Ein Integer-Ausdruck, der den <paramref name="code point" /> oder Zeichencode für das Zeichen darstellt.</param>
      <exception cref="T:System.ArgumentException">
        <paramref name="CharCode" /> &lt; -32768 oder &gt; 65535 für ChrW.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:Microsoft.VisualBasic.CompilerServices.Conversions">
      <summary>Stellt Methoden bereit, die verschiedene Typkonvertierungen ausführen.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ChangeType(System.Object,System.Type)">
      <summary>Konvertiert ein Objekt in den angegebenen Typ.</summary>
      <returns>Ein Objekt vom angegebenen Zieltyp.</returns>
      <param name="Expression">Das zu konvertierende Objekt.</param>
      <param name="TargetType">Der Typ, in den das Objekt konvertiert werden soll.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToBoolean(System.Object)">
      <summary>Konvertiert ein Objekt in einen <see cref="T:System.Boolean" />-Wert.</summary>
      <returns>Ein Boolean-Wert.Gibt False zurück, wenn das Objekt NULL ist, andernfalls True.</returns>
      <param name="Value">Das zu konvertierende Objekt.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToBoolean(System.String)">
      <summary>Konvertiert eine Zeichenfolge in einen <see cref="T:System.Boolean" />-Wert.</summary>
      <returns>Ein Boolean-Wert.Gibt False zurück, wenn die Zeichenfolge NULL ist, andernfalls True.</returns>
      <param name="Value">Die zu konvertierende Zeichenfolge.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToByte(System.Object)">
      <summary>Konvertiert ein Objekt in einen <see cref="T:System.Byte" />-Wert.</summary>
      <returns>Der Byte-Wert des Objekts.</returns>
      <param name="Value">Das zu konvertierende Objekt.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToByte(System.String)">
      <summary>Konvertiert eine Zeichenfolge in einen <see cref="T:System.Byte" />-Wert.</summary>
      <returns>Der Byte-Wert der Zeichenfolge.</returns>
      <param name="Value">Die zu konvertierende Zeichenfolge.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToChar(System.Object)">
      <summary>Konvertiert ein Objekt in einen <see cref="T:System.Char" />-Wert.</summary>
      <returns>Der Char-Wert des Objekts.</returns>
      <param name="Value">Das zu konvertierende Objekt.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToChar(System.String)">
      <summary>Konvertiert eine Zeichenfolge in einen <see cref="T:System.Char" />-Wert.</summary>
      <returns>Der Char-Wert der Zeichenfolge.</returns>
      <param name="Value">Die zu konvertierende Zeichenfolge.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToCharArrayRankOne(System.Object)">
      <summary>Konvertiert ein Objekt in ein eindimensionales <see cref="T:System.Char" />-Array.</summary>
      <returns>Ein eindimensionales Char-Array.</returns>
      <param name="Value">Das zu konvertierende Objekt.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToCharArrayRankOne(System.String)">
      <summary>Konvertiert eine Zeichenfolge in ein eindimensionales <see cref="T:System.Char" />-Array.</summary>
      <returns>Ein eindimensionales Char-Array.</returns>
      <param name="Value">Die zu konvertierende Zeichenfolge.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToDate(System.Object)">
      <summary>Konvertiert ein Objekt in einen <see cref="T:System.DateTime" />-Wert.</summary>
      <returns>Der DateTime-Wert des Objekts.</returns>
      <param name="Value">Das zu konvertierende Objekt.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToDate(System.String)">
      <summary>Konvertiert eine Zeichenfolge in einen <see cref="T:System.DateTime" />-Wert.</summary>
      <returns>Der DateTime-Wert der Zeichenfolge.</returns>
      <param name="Value">Die zu konvertierende Zeichenfolge.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToDecimal(System.Boolean)">
      <summary>Konvertiert einen <see cref="T:System.Boolean" />-Wert in einen <see cref="T:System.Decimal" />-Wert.</summary>
      <returns>Der Decimal-Wert des booleschen Werts.</returns>
      <param name="Value">Ein zu konvertierender boolescher Wert.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToDecimal(System.Object)">
      <summary>Konvertiert ein Objekt in einen <see cref="T:System.Decimal" />-Wert.</summary>
      <returns>Der Decimal-Wert des Objekts.</returns>
      <param name="Value">Das zu konvertierende Objekt.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToDecimal(System.String)">
      <summary>Konvertiert eine Zeichenfolge in einen <see cref="T:System.Decimal" />-Wert.</summary>
      <returns>Der Decimal-Wert der Zeichenfolge.</returns>
      <param name="Value">Die zu konvertierende Zeichenfolge.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToDouble(System.Object)">
      <summary>Konvertiert ein Objekt in einen <see cref="T:System.Double" />-Wert.</summary>
      <returns>Der Double-Wert des Objekts.</returns>
      <param name="Value">Das zu konvertierende Objekt.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToDouble(System.String)">
      <summary>Konvertiert eine Zeichenfolge in einen <see cref="T:System.Double" />-Wert.</summary>
      <returns>Der Double-Wert der Zeichenfolge.</returns>
      <param name="Value">Die zu konvertierende Zeichenfolge.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToGenericParameter``1(System.Object)">
      <summary>Konvertiert ein Objekt in einen generischen Typ <paramref name="T" />.</summary>
      <returns>Eine Struktur oder ein Objekt des generischen Typs <paramref name="T" />.</returns>
      <param name="Value">Das zu konvertierende Objekt.</param>
      <typeparam name="T">Der Typ, in den <paramref name="Value" /> konvertiert werden soll.</typeparam>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToInteger(System.Object)">
      <summary>Konvertiert ein Objekt in einen ganzzahligen Wert.</summary>
      <returns>Der int-Wert des Objekts.</returns>
      <param name="Value">Das zu konvertierende Objekt.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToInteger(System.String)">
      <summary>Konvertiert eine Zeichenfolge in einen ganzzahligen Wert.</summary>
      <returns>Der int-Wert der Zeichenfolge.</returns>
      <param name="Value">Die zu konvertierende Zeichenfolge.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToLong(System.Object)">
      <summary>Konvertiert ein Objekt in einen Long-Wert.</summary>
      <returns>Der Long-Wert des Objekts.</returns>
      <param name="Value">Das zu konvertierende Objekt.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToLong(System.String)">
      <summary>Konvertiert eine Zeichenfolge in einen Long-Wert.</summary>
      <returns>Der Long-Wert der Zeichenfolge.</returns>
      <param name="Value">Die zu konvertierende Zeichenfolge.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToSByte(System.Object)">
      <summary>Konvertiert ein Objekt in einen <see cref="T:System.SByte" />-Wert.</summary>
      <returns>Der SByte-Wert des Objekts.</returns>
      <param name="Value">Das zu konvertierende Objekt.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToSByte(System.String)">
      <summary>Konvertiert eine Zeichenfolge in einen <see cref="T:System.SByte" />-Wert.</summary>
      <returns>Der SByte-Wert der Zeichenfolge.</returns>
      <param name="Value">Die zu konvertierende Zeichenfolge.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToShort(System.Object)">
      <summary>Konvertiert ein Objekt in einen Short-Wert.</summary>
      <returns>Der Short-Wert des Objekts.</returns>
      <param name="Value">Das zu konvertierende Objekt.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToShort(System.String)">
      <summary>Konvertiert eine Zeichenfolge in einen Short-Wert.</summary>
      <returns>Der Short-Wert der Zeichenfolge.</returns>
      <param name="Value">Die zu konvertierende Zeichenfolge.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToSingle(System.Object)">
      <summary>Konvertiert ein Objekt in einen <see cref="T:System.Single" />-Wert.</summary>
      <returns>Der Single-Wert des Objekts.</returns>
      <param name="Value">Das zu konvertierende Objekt.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToSingle(System.String)">
      <summary>Konvertiert einen <see cref="T:System.String" /> in einen <see cref="T:System.Single" />-Wert.</summary>
      <returns>Der Single-Wert der Zeichenfolge.</returns>
      <param name="Value">Die zu konvertierende Zeichenfolge.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToString(System.Boolean)">
      <summary>Konvertiert einen <see cref="T:System.Boolean" />-Wert in einen <see cref="T:System.String" />.</summary>
      <returns>Die String-Entsprechung des Boolean-Werts.</returns>
      <param name="Value">Der zu konvertierende Boolean-Wert.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToString(System.Byte)">
      <summary>Konvertiert einen <see cref="T:System.Byte" />-Wert in einen <see cref="T:System.String" />.</summary>
      <returns>Die String-Entsprechung des Byte-Werts.</returns>
      <param name="Value">Der zu konvertierende Byte-Wert.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToString(System.Char)">
      <summary>Konvertiert einen <see cref="T:System.Char" />-Wert in einen <see cref="T:System.String" />.</summary>
      <returns>Die String-Entsprechung des Char-Werts.</returns>
      <param name="Value">Der zu konvertierende Char-Wert.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToString(System.DateTime)">
      <summary>Konvertiert einen <see cref="T:System.DateTime" />-Wert in einen <see cref="T:System.String" />-Wert.</summary>
      <returns>Die String-Entsprechung des DateTime-Werts.</returns>
      <param name="Value">Der zu konvertierende DateTime-Wert.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToString(System.Decimal)">
      <summary>Konvertiert einen <see cref="T:System.Decimal" />-Wert in einen <see cref="T:System.String" />-Wert.</summary>
      <returns>Die String-Entsprechung des Decimal-Werts.</returns>
      <param name="Value">Der zu konvertierende Decimal-Wert.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToString(System.Double)">
      <summary>Konvertiert einen <see cref="T:System.Double" />-Wert in einen <see cref="T:System.String" />-Wert.</summary>
      <returns>Die String-Entsprechung des Double-Werts.</returns>
      <param name="Value">Der zu konvertierende Double-Wert.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToString(System.Int16)">
      <summary>Konvertiert einen Short-Wert in einen <see cref="T:System.String" />-Wert.</summary>
      <returns>Die String-Entsprechung des Short-Werts.</returns>
      <param name="Value">Der zu konvertierende Short-Wert.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToString(System.Int32)">
      <summary>Konvertiert einen ganzzahligen Wert in einen <see cref="T:System.String" />-Wert.</summary>
      <returns>Die String-Entsprechung des int-Werts.</returns>
      <param name="Value">Der zu konvertierende int-Wert.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToString(System.Int64)">
      <summary>Konvertiert einen Long-Wert in einen <see cref="T:System.String" />-Wert.</summary>
      <returns>Die String-Entsprechung des Long-Werts.</returns>
      <param name="Value">Der zu konvertierende Long-Wert.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToString(System.Object)">
      <summary>Konvertiert ein Objekt in einen <see cref="T:System.String" />-Wert.</summary>
      <returns>Die String-Entsprechung des Objekts.</returns>
      <param name="Value">Das zu konvertierende Objekt.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToString(System.Single)">
      <summary>Konvertiert einen <see cref="T:System.Single" />-Wert (Gleitkommazahl mit einfacher Genauigkeit) in einen <see cref="T:System.String" />-Wert.</summary>
      <returns>Die String-Entsprechung des Single-Werts.</returns>
      <param name="Value">Der zu konvertierende Single-Wert.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToString(System.UInt32)">
      <summary>Konvertiert einen uint-Wert in einen <see cref="T:System.String" />-Wert.</summary>
      <returns>Die String-Entsprechung des Uint-Werts.</returns>
      <param name="Value">Der zu konvertierende Uint-Wert.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToString(System.UInt64)">
      <summary>Konvertiert einen Ulong-Wert in einen <see cref="T:System.String" />-Wert.</summary>
      <returns>Die String-Entsprechung des Ulong-Werts.</returns>
      <param name="Value">Der zu konvertierende Ulong-Wert.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToUInteger(System.Object)">
      <summary>Konvertiert ein Objekt in einen Uint-Wert.</summary>
      <returns>Der Uint-Wert des Objekts.</returns>
      <param name="Value">Das zu konvertierende Objekt.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToUInteger(System.String)">
      <summary>Konvertiert eine Zeichenfolge in einen Uint-Wert.</summary>
      <returns>Der Uint-Wert der Zeichenfolge.</returns>
      <param name="Value">Die zu konvertierende Zeichenfolge.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToULong(System.Object)">
      <summary>Konvertiert ein Objekt in einen Ulong-Wert.</summary>
      <returns>Der Ulong-Wert des Objekts.</returns>
      <param name="Value">Das zu konvertierende Objekt.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToULong(System.String)">
      <summary>Konvertiert eine Zeichenfolge in einen Ulong-Wert.</summary>
      <returns>Der Ulong-Wert der Zeichenfolge.</returns>
      <param name="Value">Die zu konvertierende Zeichenfolge.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToUShort(System.Object)">
      <summary>Konvertiert ein Objekt in einen Ushort-Wert.</summary>
      <returns>Der Ushort-Wert des Objekts.</returns>
      <param name="Value">Das zu konvertierende Objekt.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToUShort(System.String)">
      <summary>Konvertiert eine Zeichenfolge in einen Ushort-Wert.</summary>
      <returns>Der Ushort-Wert der Zeichenfolge.</returns>
      <param name="Value">Die zu konvertierende Zeichenfolge.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="T:Microsoft.VisualBasic.CompilerServices.DesignerGeneratedAttribute">
      <summary>Bei Anwendung auf eine Klasse ruft der Compiler implizit eine Methode für die Komponenteninitialisierung im synthetischen Standardkonstruktor auf.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.DesignerGeneratedAttribute.#ctor">
      <summary>Initialisiert eine neue Instanz von <see cref="T:Microsoft.VisualBasic.CompilerServices.DesignerGeneratedAttribute" />.</summary>
    </member>
    <member name="T:Microsoft.VisualBasic.CompilerServices.IncompleteInitialization">
      <summary>Der Visual Basic-Compiler verwendet diese Klasse während der statischen lokalen Initialisierung. Sie ist nicht für den direkten Aufruf aus Code vorgesehen.Eine Ausnahme dieses Typs wird ausgelöst, wenn eine statische lokale Variable nicht initialisiert werden kann.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.IncompleteInitialization.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:Microsoft.VisualBasic.CompilerServices.IncompleteInitialization" />-Klasse.</summary>
    </member>
    <member name="T:Microsoft.VisualBasic.CompilerServices.NewLateBinding">
      <summary>Diese Klasse stellt Hilfsmethoden bereit, die der Visual Basic-Compiler für Aufrufe mit später Bindung verwendet. Sie ist nicht für den direkten Aufruf aus Code vorgesehen.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.NewLateBinding.LateCall(System.Object,System.Type,System.String,System.Object[],System.String[],System.Type[],System.Boolean[],System.Boolean)">
      <summary>Führt eine spät gebundene Methode oder einen Funktionsaufruf aus.Diese Hilfsmethode ist nicht für den direkten Aufruf aus dem Code vorgesehen.</summary>
      <returns>Eine Instanz des Aufrufobjekts.</returns>
      <param name="Instance">Eine Instanz des Aufrufobjekts, die die Eigenschaft oder die Methode verfügbar macht.</param>
      <param name="Type">Der Typ des Aufrufobjekts.</param>
      <param name="MemberName">Der Name der Eigenschaft oder der Methode für das Aufrufobjekt.</param>
      <param name="Arguments">Ein Array mit den Argumenten, die an die aufgerufene Eigenschaft oder Methode übergeben werden sollen.</param>
      <param name="ArgumentNames">Ein Array von Argumentnamen.</param>
      <param name="TypeArguments">Ein Array von Argumenttypen. Es wird nur für generische Aufrufe zum Übergeben von Argumenttypen verwendet.</param>
      <param name="CopyBack">Ein Array von Boolean-Werten, die bei der späten Bindung verwendet werden, um die Aufrufsite darüber zu informieren, welche Argumente mit ByRef-Parametern übereinstimmen.Jeder True-Wert gibt an, dass die Argumente übereingestimmt haben und herauskopiert werden sollen, nachdem der Aufruf von LateCall abgeschlossen ist.</param>
      <param name="IgnoreReturn">Ein Boolean-Wert, der angibt, ob der Rückgabewert ignoriert werden kann.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.NewLateBinding.LateGet(System.Object,System.Type,System.String,System.Object[],System.String[],System.Type[],System.Boolean[])">
      <summary>Führt einen Get-Vorgang einer spät gebundenen Eigenschaft oder einen Feldzugriffsaufruf aus.Diese Hilfsmethode ist nicht für den direkten Aufruf aus dem Code vorgesehen.</summary>
      <returns>Eine Instanz des Aufrufobjekts.</returns>
      <param name="Instance">Eine Instanz des Aufrufobjekts, die die Eigenschaft oder die Methode verfügbar macht.</param>
      <param name="Type">Der Typ des Aufrufobjekts.</param>
      <param name="MemberName">Der Name der Eigenschaft oder der Methode für das Aufrufobjekt.</param>
      <param name="Arguments">Ein Array mit den Argumenten, die an die aufgerufene Eigenschaft oder Methode übergeben werden sollen.</param>
      <param name="ArgumentNames">Ein Array von Argumentnamen.</param>
      <param name="TypeArguments">Ein Array von Argumenttypen. Es wird nur für generische Aufrufe zum Übergeben von Argumenttypen verwendet.</param>
      <param name="CopyBack">Ein Array von Boolean-Werten, die bei der späten Bindung verwendet werden, um die Aufrufsite darüber zu informieren, welche Argumente mit ByRef-Parametern übereinstimmen.Jeder True-Wert gibt an, dass die Argumente übereingestimmt haben und herauskopiert werden sollen, nachdem der Aufruf von LateCall abgeschlossen ist.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.NewLateBinding.LateIndexGet(System.Object,System.Object[],System.String[])">
      <summary>Führt einen Get-Vorgang einer spät gebundenen Eigenschaft oder einen Feldzugriffsaufruf aus.Diese Hilfsmethode ist nicht für den direkten Aufruf aus dem Code vorgesehen.</summary>
      <returns>Eine Instanz des Aufrufobjekts.</returns>
      <param name="Instance">Eine Instanz des Aufrufobjekts, die die Eigenschaft oder die Methode verfügbar macht.</param>
      <param name="Arguments">Ein Array mit den Argumenten, die an die aufgerufene Eigenschaft oder Methode übergeben werden sollen.</param>
      <param name="ArgumentNames">Ein Array von Argumentnamen.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.NewLateBinding.LateIndexSet(System.Object,System.Object[],System.String[])">
      <summary>Führt einen Set-Vorgang einer spät gebundenen Eigenschaft oder einen Feldschreibaufruf aus.Diese Hilfsmethode ist nicht für den direkten Aufruf aus dem Code vorgesehen.</summary>
      <param name="Instance">Eine Instanz des Aufrufobjekts, die die Eigenschaft oder die Methode verfügbar macht.</param>
      <param name="Arguments">Ein Array mit den Argumenten, die an die aufgerufene Eigenschaft oder Methode übergeben werden sollen.</param>
      <param name="ArgumentNames">Ein Array von Argumentnamen.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.NewLateBinding.LateIndexSetComplex(System.Object,System.Object[],System.String[],System.Boolean,System.Boolean)">
      <summary>Führt einen Set-Vorgang einer spät gebundenen Eigenschaft oder einen Feldschreibaufruf aus.Diese Hilfsmethode ist nicht für den direkten Aufruf aus dem Code vorgesehen.</summary>
      <param name="Instance">Eine Instanz des Aufrufobjekts, die die Eigenschaft oder die Methode verfügbar macht.</param>
      <param name="Arguments">Ein Array mit den Argumenten, die an die aufgerufene Eigenschaft oder Methode übergeben werden sollen.</param>
      <param name="ArgumentNames">Ein Array von Argumentnamen.</param>
      <param name="OptimisticSet">Ein Boolean-Wert, mit dem ermittelt wird, ob der Set-Vorgang funktioniert.Legen Sie True fest, wenn Sie glauben, dass in der Eigenschaft oder im Feld ein Zwischenwert festgelegt wurde, andernfalls False.</param>
      <param name="RValueBase">Ein Boolean-Wert, der angibt, wenn der Basisverweis des späten Verweises ein RValue ist.Legen Sie True fest, wenn der Basisverweis des späten Verweises ein RValue ist. Dadurch können Sie eine Laufzeitausnahme für späte Zuweisungen zu Feldern der Werttypen von RValues generieren.Andernfalls legen Sie False fest.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.NewLateBinding.LateSet(System.Object,System.Type,System.String,System.Object[],System.String[],System.Type[])">
      <summary>Führt einen Set-Vorgang einer spät gebundenen Eigenschaft oder einen Feldschreibaufruf aus.Diese Hilfsmethode ist nicht für den direkten Aufruf aus dem Code vorgesehen.</summary>
      <param name="Instance">Eine Instanz des Aufrufobjekts, die die Eigenschaft oder die Methode verfügbar macht.</param>
      <param name="Type">Der Typ des Aufrufobjekts.</param>
      <param name="MemberName">Der Name der Eigenschaft oder der Methode für das Aufrufobjekt.</param>
      <param name="Arguments">Ein Array mit den Argumenten, die an die aufgerufene Eigenschaft oder Methode übergeben werden sollen.</param>
      <param name="ArgumentNames">Ein Array von Argumentnamen.</param>
      <param name="TypeArguments">Ein Array von Argumenttypen. Es wird nur für generische Aufrufe zum Übergeben von Argumenttypen verwendet.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.NewLateBinding.LateSet(System.Object,System.Type,System.String,System.Object[],System.String[],System.Type[],System.Boolean,System.Boolean,Microsoft.VisualBasic.CallType)">
      <summary>Führt einen Set-Vorgang einer spät gebundenen Eigenschaft oder einen Feldschreibaufruf aus.Diese Hilfsmethode ist nicht für den direkten Aufruf aus dem Code vorgesehen.</summary>
      <param name="Instance">Eine Instanz des Aufrufobjekts, die die Eigenschaft oder die Methode verfügbar macht.</param>
      <param name="Type">Der Typ des Aufrufobjekts.</param>
      <param name="MemberName">Der Name der Eigenschaft oder der Methode für das Aufrufobjekt.</param>
      <param name="Arguments">Ein Array mit den Argumenten, die an die aufgerufene Eigenschaft oder Methode übergeben werden sollen.</param>
      <param name="ArgumentNames">Ein Array von Argumentnamen.</param>
      <param name="TypeArguments">Ein Array von Argumenttypen. Es wird nur für generische Aufrufe zum Übergeben von Argumenttypen verwendet.</param>
      <param name="OptimisticSet">Ein Boolean-Wert, mit dem ermittelt wird, ob der Set-Vorgang funktioniert.Legen Sie True fest, wenn Sie glauben, dass in der Eigenschaft oder im Feld ein Zwischenwert festgelegt wurde, andernfalls False.</param>
      <param name="RValueBase">Ein Boolean-Wert, der angibt, wenn der Basisverweis des späten Verweises ein RValue ist.Legen Sie True fest, wenn der Basisverweis des späten Verweises ein RValue ist. Dadurch können Sie eine Laufzeitausnahme für späte Zuweisungen zu Feldern der Werttypen von RValues generieren.Andernfalls legen Sie False fest.</param>
      <param name="CallType">Ein Enumerationsmember vom Typ <see cref="T:Microsoft.VisualBasic.CallType" />, der den Typ der aufgerufenen Prozedur darstellt.Der Wert von CallType kann Method, Get oder Set sein.Es wird nur Set verwendet.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.NewLateBinding.LateSetComplex(System.Object,System.Type,System.String,System.Object[],System.String[],System.Type[],System.Boolean,System.Boolean)">
      <summary>Führt einen Set-Vorgang einer spät gebundenen Eigenschaft oder einen Feldschreibaufruf aus.Diese Hilfsmethode ist nicht für den direkten Aufruf aus dem Code vorgesehen.</summary>
      <param name="Instance">Eine Instanz des Aufrufobjekts, die die Eigenschaft oder die Methode verfügbar macht.</param>
      <param name="Type">Der Typ des Aufrufobjekts.</param>
      <param name="MemberName">Der Name der Eigenschaft oder der Methode für das Aufrufobjekt.</param>
      <param name="Arguments">Ein Array mit den Argumenten, die an die aufgerufene Eigenschaft oder Methode übergeben werden sollen.</param>
      <param name="ArgumentNames">Ein Array von Argumentnamen.</param>
      <param name="TypeArguments">Ein Array von Argumenttypen. Es wird nur für generische Aufrufe zum Übergeben von Argumenttypen verwendet.</param>
      <param name="OptimisticSet">Ein Boolean-Wert, mit dem ermittelt wird, ob der Set-Vorgang funktioniert.Legen Sie True fest, wenn Sie glauben, dass in der Eigenschaft oder im Feld ein Zwischenwert festgelegt wurde, andernfalls False.</param>
      <param name="RValueBase">Ein Boolean-Wert, der angibt, wenn der Basisverweis des späten Verweises ein RValue ist.Legen Sie True fest, wenn der Basisverweis des späten Verweises ein RValue ist. Dadurch können Sie eine Laufzeitausnahme für späte Zuweisungen zu Feldern der Werttypen von RValues generieren.Andernfalls legen Sie False fest.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="T:Microsoft.VisualBasic.CompilerServices.ObjectFlowControl">
      <summary>Der Visual Basic-Compiler verwendet diese Klasse für die Objektablaufsteuerung. Sie ist nicht für den direkten Aufruf aus Code vorgesehen.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.ObjectFlowControl.CheckForSyncLockOnValueType(System.Object)">
      <summary>Prüft auf eine Synchronisierungssperre für den angegebenen Typ.</summary>
      <param name="Expression">Der Datentyp, der auf eine Synchronisierungssperre geprüft wird.</param>
    </member>
    <member name="T:Microsoft.VisualBasic.CompilerServices.ObjectFlowControl.ForLoopControl">
      <summary>Stellt für den Visual Basic-Compiler Dienste zum Kompilieren von For...Next-Schleifen bereit.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.ObjectFlowControl.ForLoopControl.ForLoopInitObj(System.Object,System.Object,System.Object,System.Object,System.Object@,System.Object@)">
      <summary>Initialisiert eine For...Next-Schleife.</summary>
      <returns>False, wenn der Schleifendurchlauf beendet wurde, andernfalls True.</returns>
      <param name="Counter">Die Zählervariable für die Schleife.</param>
      <param name="Start">Der Anfangswert des Schleifenzählers.</param>
      <param name="Limit">Der Wert der To-Option.</param>
      <param name="StepValue">Der Wert der Step-Option.</param>
      <param name="LoopForResult">Ein Objekt, das überprüfte Werte für Schleifenwerte enthält.</param>
      <param name="CounterResult">Der Zählerwert für die nächste Schleifeniteration.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.ObjectFlowControl.ForLoopControl.ForNextCheckDec(System.Decimal,System.Decimal,System.Decimal)">
      <summary>Führt eine Überprüfung auf gültige Werte für den Schleifenzähler sowie den Step-Wert und den To-Wert aus.</summary>
      <returns>True, wenn <paramref name="StepValue" /> größer als 0 (null) und <paramref name="count" /> kleiner als oder gleich <paramref name="limit" /> ist oder wenn <paramref name="StepValue" /> kleiner als oder gleich 0 (null) und <paramref name="count" /> größer als oder gleich <paramref name="limit" /> ist, andernfalls False.</returns>
      <param name="count">Erforderlich.Ein Decimal-Wert, der den Anfangswert darstellt, der für die Zählervariable der Schleife übergeben wird.</param>
      <param name="limit">Erforderlich.Ein Decimal-Wert, der den Wert darstellt, der mit dem To-Schlüsselwort übergeben wird.</param>
      <param name="StepValue">Erforderlich.Ein Decimal-Wert, der den Wert darstellt, der mit dem Step-Schlüsselwort übergeben wird.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.ObjectFlowControl.ForLoopControl.ForNextCheckObj(System.Object,System.Object,System.Object@)">
      <summary>Inkrementiert eine For...Next-Schleife.</summary>
      <returns>False, wenn der Schleifendurchlauf beendet wurde, andernfalls True.</returns>
      <param name="Counter">Die Zählervariable für die Schleife.</param>
      <param name="LoopObj">Ein Objekt, das überprüfte Werte für Schleifenwerte enthält.</param>
      <param name="CounterResult">Der Zählerwert für die nächste Schleifeniteration.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.ObjectFlowControl.ForLoopControl.ForNextCheckR4(System.Single,System.Single,System.Single)">
      <summary>Führt eine Überprüfung auf gültige Werte für den Schleifenzähler sowie den Step-Wert und den To-Wert aus.</summary>
      <returns>True, wenn <paramref name="StepValue" /> größer als 0 (null) und <paramref name="count" /> kleiner als oder gleich <paramref name="limit" /> ist oder wenn <paramref name="StepValue" /> kleiner als oder gleich 0 (null) und <paramref name="count" /> größer als oder gleich <paramref name="limit" /> ist, andernfalls False.</returns>
      <param name="count">Erforderlich.Ein Single-Wert, der den Anfangswert darstellt, der für die Zählervariable der Schleife übergeben wird.</param>
      <param name="limit">Erforderlich.Ein Single-Wert, der den Wert darstellt, der mit dem To-Schlüsselwort übergeben wird.</param>
      <param name="StepValue">Erforderlich.Ein Single-Wert, der den Wert darstellt, der mit dem Step-Schlüsselwort übergeben wird.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.ObjectFlowControl.ForLoopControl.ForNextCheckR8(System.Double,System.Double,System.Double)">
      <summary>Führt eine Überprüfung auf gültige Werte für den Schleifenzähler sowie den Step-Wert und den To-Wert aus.</summary>
      <returns>True, wenn <paramref name="StepValue" /> größer als 0 (null) und <paramref name="count" /> kleiner als oder gleich <paramref name="limit" /> ist oder wenn <paramref name="StepValue" /> kleiner als oder gleich 0 (null) und <paramref name="count" /> größer als oder gleich <paramref name="limit" /> ist, andernfalls False.</returns>
      <param name="count">Erforderlich.Ein Double-Wert, der den Anfangswert darstellt, der für die Zählervariable der Schleife übergeben wird.</param>
      <param name="limit">Erforderlich.Ein Double-Wert, der den Wert darstellt, der mit dem To-Schlüsselwort übergeben wird.</param>
      <param name="StepValue">Erforderlich.Ein Double-Wert, der den Wert darstellt, der mit dem Step-Schlüsselwort übergeben wird.</param>
    </member>
    <member name="T:Microsoft.VisualBasic.CompilerServices.Operators">
      <summary>Stellt mathematische Operatoren mit später Bindung bereit, z. B. <see cref="M:Microsoft.VisualBasic.CompilerServices.Operators.AddObject(System.Object,System.Object)" /> und <see cref="M:Microsoft.VisualBasic.CompilerServices.Operators.CompareObject(System.Object,System.Object,System.Boolean)" />, die vom Visual Basic-Compiler intern verwendet werden. </summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.AddObject(System.Object,System.Object)">
      <summary>Stellt den Visual Basic-Additionsoperator (+) dar.</summary>
      <returns>Die Summe von <paramref name="Left" /> und <paramref name="Right" />.</returns>
      <param name="Left">Erforderlich.Ein beliebiger numerischer Ausdruck.</param>
      <param name="Right">Erforderlich.Ein beliebiger numerischer Ausdruck.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.AndObject(System.Object,System.Object)">
      <summary>Stellt den And-Operator in Visual Basic dar.</summary>
      <returns>Für Boolean-Operationen: True, wenn sowohl <paramref name="Left" /> als auch <paramref name="Right" />True ergeben, andernfalls False.Für bitweise Operationen: 1, wenn sowohl <paramref name="Left" /> als auch <paramref name="Right" /> 1 ergeben, andernfalls 0.</returns>
      <param name="Left">Erforderlich.Ein beliebiger Boolean-Ausdruck oder numerischer Ausdruck.</param>
      <param name="Right">Erforderlich.Ein beliebiger Boolean-Ausdruck oder numerischer Ausdruck.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.CompareObjectEqual(System.Object,System.Object,System.Boolean)">
      <summary>Stellt den Visual Basic-Gleichheitsoperator (=) dar.</summary>
      <returns>True, wenn <paramref name="Left" /> und <paramref name="Right" /> gleich sind, andernfalls False.</returns>
      <param name="Left">Erforderlich.Ein beliebiger Ausdruck.</param>
      <param name="Right">Erforderlich.Ein beliebiger Ausdruck.</param>
      <param name="TextCompare">Erforderlich.True, um einen Zeichenfolgenvergleich ohne Berücksichtigung der Groß- und Kleinschreibung auszuführen, andernfalls False.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.CompareObjectGreater(System.Object,System.Object,System.Boolean)">
      <summary>Stellt den Größer-als-Operator (&gt;) in Visual Basic dar.</summary>
      <returns>True, wenn <paramref name="Left" /> größer als <paramref name="Right" /> ist, andernfalls False.</returns>
      <param name="Left">Erforderlich.Ein beliebiger Ausdruck.</param>
      <param name="Right">Erforderlich.Ein beliebiger Ausdruck.</param>
      <param name="TextCompare">Erforderlich.True, um einen Zeichenfolgenvergleich ohne Berücksichtigung der Groß- und Kleinschreibung auszuführen, andernfalls False.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.CompareObjectGreaterEqual(System.Object,System.Object,System.Boolean)">
      <summary>Stellt den Größer-oder-gleich-Operator (&gt;=) in Visual Basic dar.</summary>
      <returns>True, wenn <paramref name="Left" /> größer als oder gleich <paramref name="Right" /> ist, andernfalls False.</returns>
      <param name="Left">Erforderlich.Ein beliebiger Ausdruck.</param>
      <param name="Right">Erforderlich.Ein beliebiger Ausdruck.</param>
      <param name="TextCompare">Erforderlich.True, um einen Zeichenfolgenvergleich ohne Berücksichtigung der Groß- und Kleinschreibung auszuführen, andernfalls False.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.CompareObjectLess(System.Object,System.Object,System.Boolean)">
      <summary>Stellt den Kleiner-als-Operator (&lt;) in Visual Basic dar.</summary>
      <returns>True, wenn <paramref name="Left" /> kleiner als <paramref name="Right" /> ist, andernfalls False.</returns>
      <param name="Left">Erforderlich.Ein beliebiger Ausdruck.</param>
      <param name="Right">Erforderlich.Ein beliebiger Ausdruck.</param>
      <param name="TextCompare">Erforderlich.True, um einen Zeichenfolgenvergleich ohne Berücksichtigung der Groß- und Kleinschreibung auszuführen, andernfalls False.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.CompareObjectLessEqual(System.Object,System.Object,System.Boolean)">
      <summary>Stellt den Kleiner-oder-gleich-Operator (&lt;=) in Visual Basic dar.</summary>
      <returns>True, wenn <paramref name="Left" /> kleiner als oder gleich <paramref name="Right" /> ist, andernfalls False.</returns>
      <param name="Left">Erforderlich.Ein beliebiger Ausdruck.</param>
      <param name="Right">Erforderlich.Ein beliebiger Ausdruck.</param>
      <param name="TextCompare">Erforderlich.True, um einen Zeichenfolgenvergleich ohne Berücksichtigung der Groß- und Kleinschreibung auszuführen, andernfalls False.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.CompareObjectNotEqual(System.Object,System.Object,System.Boolean)">
      <summary>Stellt den Visual Basic-Ungleichheitsoperator (&lt;&gt;) dar.</summary>
      <returns>True, wenn <paramref name="Left" /> und <paramref name="Right" /> ungleich sind, andernfalls False.</returns>
      <param name="Left">Erforderlich.Ein beliebiger Ausdruck.</param>
      <param name="Right">Erforderlich.Ein beliebiger Ausdruck.</param>
      <param name="TextCompare">Erforderlich.True, um einen Zeichenfolgenvergleich ohne Berücksichtigung der Groß- und Kleinschreibung auszuführen, andernfalls False.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.CompareString(System.String,System.String,System.Boolean)">
      <summary>Führt einen binären Vergleich oder einen Textzeichenfolgenvergleich für zwei angegebene Zeichenfolgen aus.</summary>
      <returns>Wert Bedingung -1 <paramref name="Left" /> ist kleiner als <paramref name="Right" />. 0<paramref name="Left" /> ist gleich <paramref name="Right" />. 1 <paramref name="Left" /> ist größer als <paramref name="Right" />. </returns>
      <param name="Left">Erforderlich.Ein beliebiger String-Ausdruck.</param>
      <param name="Right">Erforderlich.Ein beliebiger String-Ausdruck.</param>
      <param name="TextCompare">Erforderlich.True, um einen Zeichenfolgenvergleich ohne Berücksichtigung der Groß- und Kleinschreibung auszuführen, andernfalls False.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.ConcatenateObject(System.Object,System.Object)">
      <summary>Stellt den Visual Basic-Verkettungsoperator (&amp;) dar.</summary>
      <returns>Eine Zeichenfolge, die die Verkettung von <paramref name="Left" /> und <paramref name="Right" /> darstellt.</returns>
      <param name="Left">Erforderlich.Ein beliebiger Ausdruck.</param>
      <param name="Right">Erforderlich.Ein beliebiger Ausdruck.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.ConditionalCompareObjectEqual(System.Object,System.Object,System.Boolean)">
      <summary>Stellt den überladenen Visual Basic-Gleichheitsoperator (=) dar.</summary>
      <returns>Das Ergebnis des überladenen Gleichheitsoperators.False, wenn eine Operatorüberladung nicht unterstützt wird.</returns>
      <param name="Left">Erforderlich.Ein beliebiger Ausdruck.</param>
      <param name="Right">Erforderlich.Ein beliebiger Ausdruck.</param>
      <param name="TextCompare">Erforderlich.True, um einen Zeichenfolgenvergleich ohne Berücksichtigung der Groß- und Kleinschreibung auszuführen, andernfalls False.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.ConditionalCompareObjectGreater(System.Object,System.Object,System.Boolean)">
      <summary>Stellt den überladenen Größer-als-Operator (&gt;) in Visual Basic dar.</summary>
      <returns>Das Ergebnis des überladenen Größer-als-Operators.False, wenn eine Operatorüberladung nicht unterstützt wird.</returns>
      <param name="Left">Erforderlich.Ein beliebiger Ausdruck.</param>
      <param name="Right">Erforderlich.Ein beliebiger Ausdruck.</param>
      <param name="TextCompare">Erforderlich.True, um einen Zeichenfolgenvergleich ohne Berücksichtigung der Groß- und Kleinschreibung auszuführen, andernfalls False.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.ConditionalCompareObjectGreaterEqual(System.Object,System.Object,System.Boolean)">
      <summary>Stellt den überladenen Größer-oder-gleich-Operator (&gt;=) in Visual Basic dar.</summary>
      <returns>Das Ergebnis des überladenen Größer-als-Operators oder des überladenen Gleichheitsoperators.False, wenn eine Operatorüberladung nicht unterstützt wird.</returns>
      <param name="Left">Erforderlich.Ein beliebiger Ausdruck.</param>
      <param name="Right">Erforderlich.Ein beliebiger Ausdruck.</param>
      <param name="TextCompare">Erforderlich.True, um einen Zeichenfolgenvergleich ohne Berücksichtigung der Groß- und Kleinschreibung auszuführen, andernfalls False.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.ConditionalCompareObjectLess(System.Object,System.Object,System.Boolean)">
      <summary>Stellt den überladenen Kleiner-als-Operator (&lt;) in Visual Basic dar.</summary>
      <returns>Das Ergebnis des überladenen Kleiner-als-Operators.False, wenn eine Operatorüberladung nicht unterstützt wird.</returns>
      <param name="Left">Erforderlich.Ein beliebiger Ausdruck.</param>
      <param name="Right">Erforderlich.Ein beliebiger Ausdruck.</param>
      <param name="TextCompare">Erforderlich.True, um einen Zeichenfolgenvergleich ohne Berücksichtigung der Groß- und Kleinschreibung auszuführen, andernfalls False.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.ConditionalCompareObjectLessEqual(System.Object,System.Object,System.Boolean)">
      <summary>Stellt den überladenen Kleiner-oder-gleich-Operator (&lt;=) in Visual Basic dar.</summary>
      <returns>Das Ergebnis des überladenen Kleiner-als-Operators oder des überladenen Gleichheitsoperators.False, wenn eine Operatorüberladung nicht unterstützt wird.</returns>
      <param name="Left">Erforderlich.Ein beliebiger Ausdruck.</param>
      <param name="Right">Erforderlich.Ein beliebiger Ausdruck.</param>
      <param name="TextCompare">Erforderlich.True, um einen Zeichenfolgenvergleich ohne Berücksichtigung der Groß- und Kleinschreibung auszuführen, andernfalls False.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.ConditionalCompareObjectNotEqual(System.Object,System.Object,System.Boolean)">
      <summary>Stellt den überladenen Visual Basic-Ungleichheitsoperator (&lt;&gt;) dar.</summary>
      <returns>Das Ergebnis des überladenen Ungleichheitsoperators.False, wenn eine Operatorüberladung nicht unterstützt wird.</returns>
      <param name="Left">Erforderlich.Ein beliebiger Ausdruck.</param>
      <param name="Right">Erforderlich.Ein beliebiger Ausdruck.</param>
      <param name="TextCompare">Erforderlich.True, um einen Zeichenfolgenvergleich ohne Berücksichtigung der Groß- und Kleinschreibung auszuführen, andernfalls False.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.DivideObject(System.Object,System.Object)">
      <summary>Stellt den Visual Basic-Divisionsoperator (/) dar.</summary>
      <returns>Der volle Quotient, der sich aus der Division von <paramref name="Left" /> durch <paramref name="Right" /> ergibt, einschließlich des Rests.</returns>
      <param name="Left">Erforderlich.Ein beliebiger numerischer Ausdruck.</param>
      <param name="Right">Erforderlich.Ein beliebiger numerischer Ausdruck.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.ExponentObject(System.Object,System.Object)">
      <summary>Stellt den Visual Basic-Exponentialoperator (^) dar.</summary>
      <returns>Das Ergebnis der Potenzierung von <paramref name="Left" /> mit <paramref name="Right" />.</returns>
      <param name="Left">Erforderlich.Ein beliebiger numerischer Ausdruck.</param>
      <param name="Right">Erforderlich.Ein beliebiger numerischer Ausdruck.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.IntDivideObject(System.Object,System.Object)">
      <summary>Stellt den Ganzzahl-Divisionsoperator (\) in Visual Basic dar.</summary>
      <returns>Der ganzzahlige Quotient, der sich aus der Division von <paramref name="Left" /> durch <paramref name="Right" /> ergibt. Es wird nur der ganzzahlige Teil beibehalten, und ein eventueller Rest wird verworfen.</returns>
      <param name="Left">Erforderlich.Ein beliebiger numerischer Ausdruck.</param>
      <param name="Right">Erforderlich.Ein beliebiger numerischer Ausdruck.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.LeftShiftObject(System.Object,System.Object)">
      <summary>Stellt den arithmetischen Linksschiebeoperator (&lt;&lt;) in Visual Basic dar.</summary>
      <returns>Ein ganzzahliger numerischer Wert.Das Ergebnis der Verschiebung des Bitmusters.Der Datentyp entspricht dem von <paramref name="Operand" />.</returns>
      <param name="Operand">Erforderlich.Ein ganzzahliger numerischer Ausdruck.Das zu verschiebende Bitmuster.Der Datentyp muss ein ganzzahliger Typ (SByte, Byte, Short, UShort, Integer, UInteger, Long oder ULong) sein.</param>
      <param name="Amount">Erforderlich.Ein numerischer Ausdruck.Die Anzahl der Bits, um die das Bitmuster verschoben werden soll.Der Datentyp muss Integer sein oder zu Integer erweitert werden.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.ModObject(System.Object,System.Object)">
      <summary>Stellt den Mod-Operator in Visual Basic dar.</summary>
      <returns>Der Rest aus der Division von <paramref name="Left" /> durch <paramref name="Right" />. </returns>
      <param name="Left">Erforderlich.Ein beliebiger numerischer Ausdruck.</param>
      <param name="Right">Erforderlich.Ein beliebiger numerischer Ausdruck.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.MultiplyObject(System.Object,System.Object)">
      <summary>Stellt den Visual Basic-Multiplikationsoperator (*) dar.</summary>
      <returns>Das Produkt von <paramref name="Left" /> und <paramref name="Right" />.</returns>
      <param name="Left">Erforderlich.Ein beliebiger numerischer Ausdruck.</param>
      <param name="Right">Erforderlich.Ein beliebiger numerischer Ausdruck.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.NegateObject(System.Object)">
      <summary>Stellt den unären Subtraktionsoperator (–) in Visual Basic dar.</summary>
      <returns>Der negative Wert von <paramref name="Operand" />.</returns>
      <param name="Operand">Erforderlich.Ein beliebiger numerischer Ausdruck.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.NotObject(System.Object)">
      <summary>Stellt den Not-Operator in Visual Basic dar.</summary>
      <returns>Für Boolean-Operationen: False, wenn <paramref name="Operand" /> gleich True ist, andernfalls True.Für bitweise Operationen: 1, wenn <paramref name="Operand" /> gleich 0 ist, andernfalls 0.</returns>
      <param name="Operand">Erforderlich.Ein beliebiger Boolean-Ausdruck oder numerischer Ausdruck.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.OrObject(System.Object,System.Object)">
      <summary>Stellt den Or-Operator in Visual Basic dar.</summary>
      <returns>Für Boolean-Operationen: False, wenn sowohl <paramref name="Left" /> als auch <paramref name="Right" />False ergeben, andernfalls True.Für bitweise Operationen: 0, wenn sowohl <paramref name="Left" /> als auch <paramref name="Right" /> 0 ergeben, andernfalls 1.</returns>
      <param name="Left">Erforderlich.Ein beliebiger Boolean-Ausdruck oder numerischer Ausdruck.</param>
      <param name="Right">Erforderlich.Ein beliebiger Boolean-Ausdruck oder numerischer Ausdruck.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.PlusObject(System.Object)">
      <summary>Stellt den unären Additionsoperator (+) in Visual Basic dar.</summary>
      <returns>Der Wert von <paramref name="Operand" />. (Das Vorzeichen von <paramref name="Operand" /> wird nicht geändert.)</returns>
      <param name="Operand">Erforderlich.Ein beliebiger numerischer Ausdruck.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.RightShiftObject(System.Object,System.Object)">
      <summary>Stellt den arithmetischen Rechtsschiebeoperator (&gt;&gt;) in Visual Basic dar.</summary>
      <returns>Ein ganzzahliger numerischer Wert.Das Ergebnis der Verschiebung des Bitmusters.Der Datentyp entspricht dem von <paramref name="Operand" />.</returns>
      <param name="Operand">Erforderlich.Ein ganzzahliger numerischer Ausdruck.Das zu verschiebende Bitmuster.Der Datentyp muss ein ganzzahliger Typ (SByte, Byte, Short, UShort, Integer, UInteger, Long oder ULong) sein.</param>
      <param name="Amount">Erforderlich.Ein numerischer Ausdruck.Die Anzahl der Bits, um die das Bitmuster verschoben werden soll.Der Datentyp muss Integer sein oder zu Integer erweitert werden.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.SubtractObject(System.Object,System.Object)">
      <summary>Stellt den Visual Basic-Subtraktionsoperator (–) dar.</summary>
      <returns>Die Differenz zwischen <paramref name="Left" /> und <paramref name="Right" />.</returns>
      <param name="Left">Erforderlich.Ein beliebiger numerischer Ausdruck.</param>
      <param name="Right">Erforderlich.Ein beliebiger numerischer Ausdruck.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.XorObject(System.Object,System.Object)">
      <summary>Stellt den Xor-Operator in Visual Basic dar.</summary>
      <returns>Ein Boolean-Wert oder ein numerischer Wert.Bei einem Boolean-Vergleich ist der Rückgabewert der logische Ausschluss (exklusive logische Disjunktion) aus zwei Boolean-Werten.Bei bitweisen (numerischen) Vorgängen ist der Rückgabewert ein numerischer Wert, der den bitweisen Ausschluss (exklusive bitweise Disjunktion) aus zwei numerischen Bitmustern darstellt.Weitere Informationen finden Sie unter Xor-Operator (Visual Basic).</returns>
      <param name="Left">Erforderlich.Ein beliebiger Boolean-Ausdruck oder numerischer Ausdruck.</param>
      <param name="Right">Erforderlich.Ein beliebiger Boolean-Ausdruck oder numerischer Ausdruck.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="T:Microsoft.VisualBasic.CompilerServices.OptionCompareAttribute">
      <summary>Gibt an, dass die aktuelle Option Compare-Einstellung als Standardwert für ein Argument übergeben werden soll. </summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.OptionCompareAttribute.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:Microsoft.VisualBasic.CompilerServices.OptionCompareAttribute" />-Klasse.</summary>
    </member>
    <member name="T:Microsoft.VisualBasic.CompilerServices.OptionTextAttribute">
      <summary>Der Visual Basic-Compiler gibt diese Hilfsklasse aus, um (für das Visual Basic-Debuggen) anzugeben, welche Vergleichsoption (Binär oder Text) verwendet werden soll.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.OptionTextAttribute.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:Microsoft.VisualBasic.CompilerServices.OptionTextAttribute" />-Klasse.Dies ist eine Hilfsmethode.</summary>
    </member>
    <member name="T:Microsoft.VisualBasic.CompilerServices.ProjectData">
      <summary>Stellt Hilfsmethoden für das Err-Objekt in Visual Basic bereit. </summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.ProjectData.ClearProjectError">
      <summary>Führt die Vorgänge für die Clear-Methode des Err-Objekts aus.Dies ist eine Hilfsmethode.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.ProjectData.SetProjectError(System.Exception)">
      <summary>Der Visual Basic-Compiler verwendet diese Hilfsmethode, um Ausnahmen im Err-Objekt aufzuzeichnen.</summary>
      <param name="ex">Das abzufangende <see cref="T:System.Exception" />-Objekt.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.ProjectData.SetProjectError(System.Exception,System.Int32)">
      <summary>Der Visual Basic-Compiler verwendet diese Hilfsmethode, um Ausnahmen im Err-Objekt aufzuzeichnen.</summary>
      <param name="ex">Das abzufangende <see cref="T:System.Exception" />-Objekt.</param>
      <param name="lErl">Die Zeilennummer der Ausnahme.</param>
    </member>
    <member name="T:Microsoft.VisualBasic.CompilerServices.StandardModuleAttribute">
      <summary>Diese Klasse stellt Attribute bereit, die auf das Standardmodulkonstrukt angewendet werden, wenn es in die Intermediate Language (IL) ausgegeben wird.Sie ist nicht für den direkten Aufruf aus Code vorgesehen.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.StandardModuleAttribute.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:Microsoft.VisualBasic.CompilerServices.StandardModuleAttribute" />-Klasse.</summary>
    </member>
    <member name="T:Microsoft.VisualBasic.CompilerServices.StaticLocalInitFlag">
      <summary>Der Visual Basic-Compiler verwendet diese Klasse intern zum Initialisieren statischer lokaler Member. Sie ist nicht für den direkten Aufruf aus Code vorgesehen.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.StaticLocalInitFlag.#ctor">
      <summary>Initialisiert eine neue Instanz der <see cref="T:Microsoft.VisualBasic.CompilerServices.StaticLocalInitFlag" />-Klasse.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.CompilerServices.StaticLocalInitFlag.State">
      <summary>Gibt den Zustand des Initialisierungsflags (initialisiert oder nicht) des statischen lokalen Members zurück.</summary>
    </member>
    <member name="T:Microsoft.VisualBasic.CompilerServices.Utils">
      <summary>Enthält Dienstprogramme, die vom Visual Basic-Compiler verwendet werden.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Utils.CopyArray(System.Array,System.Array)">
      <summary>Wird vom Visual Basic-Compiler als Hilfsmethode für Redim verwendet.</summary>
      <returns>Das kopierte Array.</returns>
      <param name="arySrc">Das zu kopierende Array.</param>
      <param name="aryDest">Das Zielarray.</param>
    </member>
  </members>
</doc>