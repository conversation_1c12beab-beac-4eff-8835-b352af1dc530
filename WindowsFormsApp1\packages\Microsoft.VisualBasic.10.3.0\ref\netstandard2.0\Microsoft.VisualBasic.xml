﻿<?xml version="1.0" encoding="utf-8"?><doc>
  <assembly>
    <name>Microsoft.VisualBasic</name>
  </assembly>
  <members>
    <member name="T:Microsoft.VisualBasic.CompilerServices.NewLateBinding">
      <summary>This class provides helpers that the Visual Basic compiler uses for late binding calls; it is not meant to be called directly from your code.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.NewLateBinding.FallbackCall(System.Object,System.String,System.Object[],System.String[],System.Boolean)">
      <summary>Executes a late-bound method or function call. This helper method is not meant to be called directly from your code.</summary>
      <param name="Instance">An instance of the call object exposing the property or method.</param>
      <param name="MemberName">The name of the property or method on the call object.</param>
      <param name="Arguments">An array containing the arguments to be passed to the property or method being called.</param>
      <param name="ArgumentNames">An array of argument names.</param>
      <param name="IgnoreReturn">A Boolean value indicating whether or not the return value can be ignored.</param>
      <returns>An instance of the call object.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.NewLateBinding.FallbackGet(System.Object,System.String,System.Object[],System.String[])">
      <summary>Executes a late-bound property get or field access call. This helper method is not meant to be called directly from your code.</summary>
      <param name="Instance">An instance of the call object exposing the property or method.</param>
      <param name="MemberName">The name of the property or method on the call object.</param>
      <param name="Arguments">An array containing the arguments to be passed to the property or method being called.</param>
      <param name="ArgumentNames">An array of argument names.</param>
      <returns>An instance of the call object.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.NewLateBinding.FallbackIndexSet(System.Object,System.Object[],System.String[])">
      <summary>Executes a late-bound property set or field write call. This helper method is not meant to be called directly from your code.</summary>
      <param name="Instance">An instance of the call object exposing the property or method.</param>
      <param name="Arguments">An array containing the arguments to be passed to the property or method being called.</param>
      <param name="ArgumentNames">An array of argument names.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.NewLateBinding.FallbackIndexSetComplex(System.Object,System.Object[],System.String[],System.Boolean,System.Boolean)">
      <summary>Executes a late-bound property set or field write call. This helper method is not meant to be called directly from your code.</summary>
      <param name="Instance">An instance of the call object exposing the property or method.</param>
      <param name="Arguments">An array containing the arguments to be passed to the property or method being called.</param>
      <param name="ArgumentNames">An array of argument names.</param>
      <param name="OptimisticSet">A Boolean value used to determine whether the set operation will work. Set to True when you believe that an intermediate value has been set in the property or field; otherwise False.</param>
      <param name="RValueBase">A Boolean value that specifies when the base reference of the late reference is an RValue. Set to True when the base reference of the late reference is an RValue; this allows you to generate a run-time exception for late assignments to fields of RValues of value types. Otherwise, set to False.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.NewLateBinding.FallbackInvokeDefault1(System.Object,System.Object[],System.String[],System.Boolean)">
      <summary>Executes a late-bound get of the default property or field, or call to the default method or function. This helper method is not meant to be called directly from your code.</summary>
      <param name="Instance">An instance of the call object exposing the property or method. If Instance is of type <see cref="T:System.Dynamic.IDynamicMetaObjectProvider"></see>, then bind using the Dynamic Language Runtime; otherwise perform standard late-binding.</param>
      <param name="Arguments">An array containing the arguments to be passed to the property or method being called.</param>
      <param name="ArgumentNames">An array of argument names.</param>
      <param name="ReportErrors">A Boolean value used to specify whether to throw exceptions when an error is encountered. Set to True to throw exceptions. Set to False to return Nothing when an error is encountered.</param>
      <returns>An instance of the call object.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.NewLateBinding.FallbackInvokeDefault2(System.Object,System.Object[],System.String[],System.Boolean)">
      <summary>Executes a late-bound get of the default property or field, or call to the default method or function. This helper method is not meant to be called directly from your code.</summary>
      <param name="Instance">An instance of the call object exposing the property or method.</param>
      <param name="Arguments">An array containing the arguments to be passed to the property or method being called.</param>
      <param name="ArgumentNames">An array of argument names.</param>
      <param name="ReportErrors">A Boolean value used to specify whether to throw exceptions when an error is encountered. Set to True to throw exceptions. Set to False to return Nothing when an error is encountered.</param>
      <returns>An instance of the call object.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.NewLateBinding.FallbackSet(System.Object,System.String,System.Object[])">
      <summary>Executes a late-bound property set or field write call. This helper method is not meant to be called directly from your code.</summary>
      <param name="Instance">An instance of the call object exposing the property or method.</param>
      <param name="MemberName">The name of the property or method on the call object.</param>
      <param name="Arguments">An array containing the arguments to be passed to the property or method being called.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.NewLateBinding.FallbackSetComplex(System.Object,System.String,System.Object[],System.Boolean,System.Boolean)">
      <summary>Executes a late-bound property set or field write call. This helper method is not meant to be called directly from your code.</summary>
      <param name="Instance">An instance of the call object exposing the property or method.</param>
      <param name="MemberName">The name of the property or method on the call object.</param>
      <param name="Arguments">An array containing the arguments to be passed to the property or method being called.</param>
      <param name="OptimisticSet">A Boolean value used to determine whether the set operation will work. Set to True when you believe that an intermediate value has been set in the property or field; otherwise False.</param>
      <param name="RValueBase">A Boolean value that specifies when the base reference of the late reference is an RValue. Set to True when the base reference of the late reference is an RValue; this allows you to generate a run-time exception for late assignments to fields of RValues of value types. Otherwise, set to False.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.NewLateBinding.LateCall(System.Object,System.Type,System.String,System.Object[],System.String[],System.Type[],System.Boolean[],System.Boolean)">
      <summary>Executes a late-bound method or function call. This helper method is not meant to be called directly from your code.</summary>
      <param name="Instance">An instance of the call object exposing the property or method.</param>
      <param name="Type">The type of the call object.</param>
      <param name="MemberName">The name of the property or method on the call object.</param>
      <param name="Arguments">An array containing the arguments to be passed to the property or method being called.</param>
      <param name="ArgumentNames">An array of argument names.</param>
      <param name="TypeArguments">An array of argument types; used only for generic calls to pass argument types.</param>
      <param name="CopyBack">An array of Boolean values that the late binder uses to communicate back to the call site which arguments match ByRef parameters. Each True value indicates that the arguments matched and should be copied out after the call to LateCall is complete.</param>
      <param name="IgnoreReturn">A Boolean value indicating whether or not the return value can be ignored.</param>
      <returns>An instance of the call object.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.NewLateBinding.LateCallInvokeDefault(System.Object,System.Object[],System.String[],System.Boolean)">
      <summary>Executes a late-bound call to the default method or function. This helper method is not meant to be called directly from your code.</summary>
      <param name="Instance">An instance of the call object exposing the property or method.</param>
      <param name="Arguments">An array containing the arguments to be passed to the property or method being called.</param>
      <param name="ArgumentNames">An array of argument names.</param>
      <param name="ReportErrors">A Boolean value used to specify whether to throw exceptions when an error is encountered. Set to True to throw exceptions. Set to False to return Nothing when an error is encountered.</param>
      <returns>An instance of the call object.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.NewLateBinding.LateCanEvaluate(System.Object,System.Type,System.String,System.Object[],System.Boolean,System.Boolean)">
      <summary>Indicates whether a call requires late-bound evaluation. This helper method is not meant to be called directly from your code.</summary>
      <param name="instance">An instance of the call object exposing the property or method.</param>
      <param name="type">The type of the call object.</param>
      <param name="memberName">The name of the property or method on the call object.</param>
      <param name="arguments">An array containing the arguments to be passed to the property or method being called.</param>
      <param name="allowFunctionEvaluation">A Boolean value that specifies whether to allow function evaluation.</param>
      <param name="allowPropertyEvaluation">A Boolean value that specifies whether to allow property evaluation.</param>
      <returns>A Boolean value that indicates whether the expression requires late-bound evaluation.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.NewLateBinding.LateGet(System.Object,System.Type,System.String,System.Object[],System.String[],System.Type[],System.Boolean[])">
      <summary>Executes a late-bound property get or field access call. This helper method is not meant to be called directly from your code.</summary>
      <param name="Instance">An instance of the call object exposing the property or method.</param>
      <param name="Type">The type of the call object.</param>
      <param name="MemberName">The name of the property or method on the call object.</param>
      <param name="Arguments">An array containing the arguments to be passed to the property or method being called.</param>
      <param name="ArgumentNames">An array of argument names.</param>
      <param name="TypeArguments">An array of argument types; used only for generic calls to pass argument types.</param>
      <param name="CopyBack">An array of Boolean values that the late binder uses to communicate back to the call site which arguments match ByRef parameters. Each True value indicates that the arguments matched and should be copied out after the call to LateCall is complete.</param>
      <returns>An instance of the call object.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.NewLateBinding.LateGetInvokeDefault(System.Object,System.Object[],System.String[],System.Boolean)">
      <summary>Executes a late-bound get of the default property or field. This helper method is not meant to be called directly from your code.</summary>
      <param name="Instance">An instance of the call object exposing the property or method.</param>
      <param name="Arguments">An array containing the arguments to be passed to the property or method being called.</param>
      <param name="ArgumentNames">An array of argument names.</param>
      <param name="ReportErrors">A Boolean value used to specify whether to throw exceptions when an error is encountered. Set to True to throw exceptions. Set to False to return Nothing when an error is encountered.</param>
      <returns>An instance of the call object.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.NewLateBinding.LateIndexGet(System.Object,System.Object[],System.String[])">
      <summary>Executes a late-bound property get or field access call. This helper method is not meant to be called directly from your code.</summary>
      <param name="Instance">An instance of the call object exposing the property or method.</param>
      <param name="Arguments">An array containing the arguments to be passed to the property or method being called.</param>
      <param name="ArgumentNames">An array of argument names.</param>
      <returns>An instance of the call object.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.NewLateBinding.LateIndexSet(System.Object,System.Object[],System.String[])">
      <summary>Executes a late-bound property set or field write call. This helper method is not meant to be called directly from your code.</summary>
      <param name="Instance">An instance of the call object exposing the property or method.</param>
      <param name="Arguments">An array containing the arguments to be passed to the property or method being called.</param>
      <param name="ArgumentNames">An array of argument names.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.NewLateBinding.LateIndexSetComplex(System.Object,System.Object[],System.String[],System.Boolean,System.Boolean)">
      <summary>Executes a late-bound property set or field write call. This helper method is not meant to be called directly from your code.</summary>
      <param name="Instance">An instance of the call object exposing the property or method.</param>
      <param name="Arguments">An array containing the arguments to be passed to the property or method being called.</param>
      <param name="ArgumentNames">An array of argument names.</param>
      <param name="OptimisticSet">A Boolean value used to determine whether the set operation will work. Set to True when you believe that an intermediate value has been set in the property or field; otherwise False.</param>
      <param name="RValueBase">A Boolean value that specifies when the base reference of the late reference is an RValue. Set to True when the base reference of the late reference is an RValue; this allows you to generate a run-time exception for late assignments to fields of RValues of value types. Otherwise, set to False.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.NewLateBinding.LateSet(System.Object,System.Type,System.String,System.Object[],System.String[],System.Type[])">
      <summary>Executes a late-bound property set or field write call. This helper method is not meant to be called directly from your code.</summary>
      <param name="Instance">An instance of the call object exposing the property or method.</param>
      <param name="Type">The type of the call object.</param>
      <param name="MemberName">The name of the property or method on the call object.</param>
      <param name="Arguments">An array containing the arguments to be passed to the property or method being called.</param>
      <param name="ArgumentNames">An array of argument names.</param>
      <param name="TypeArguments">An array of argument types; used only for generic calls to pass argument types.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.NewLateBinding.LateSet(System.Object,System.Type,System.String,System.Object[],System.String[],System.Type[],System.Boolean,System.Boolean,Microsoft.VisualBasic.CallType)">
      <summary>Executes a late-bound property set or field write call. This helper method is not meant to be called directly from your code.</summary>
      <param name="Instance">An instance of the call object exposing the property or method.</param>
      <param name="Type">The type of the call object.</param>
      <param name="MemberName">The name of the property or method on the call object.</param>
      <param name="Arguments">An array containing the arguments to be passed to the property or method being called.</param>
      <param name="ArgumentNames">An array of argument names.</param>
      <param name="TypeArguments">An array of argument types; used only for generic calls to pass argument types.</param>
      <param name="OptimisticSet">A Boolean value used to determine whether the set operation will work. Set to True when you believe that an intermediate value has been set in the property or field; otherwise False.</param>
      <param name="RValueBase">A Boolean value that specifies when the base reference of the late reference is an RValue. Set to True when the base reference of the late reference is an RValue; this allows you to generate a run-time exception for late assignments to fields of RValues of value types. Otherwise, set to False.</param>
      <param name="CallType">An enumeration member of type <see cref="T:Microsoft.VisualBasic.CallType"></see> representing the type of procedure being called. The value of CallType can be Method, Get, or Set. Only Set is used.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.NewLateBinding.LateSetComplex(System.Object,System.Type,System.String,System.Object[],System.String[],System.Type[],System.Boolean,System.Boolean)">
      <summary>Executes a late-bound property set or field write call. This helper method is not meant to be called directly from your code.</summary>
      <param name="Instance">An instance of the call object exposing the property or method.</param>
      <param name="Type">The type of the call object.</param>
      <param name="MemberName">The name of the property or method on the call object.</param>
      <param name="Arguments">An array containing the arguments to be passed to the property or method being called.</param>
      <param name="ArgumentNames">An array of argument names.</param>
      <param name="TypeArguments">An array of argument types; used only for generic calls to pass argument types.</param>
      <param name="OptimisticSet">A Boolean value used to determine whether the set operation will work. Set to True when you believe that an intermediate value has been set in the property or field; otherwise False.</param>
      <param name="RValueBase">A Boolean value that specifies when the base reference of the late reference is an RValue. Set to True when the base reference of the late reference is an RValue; this allows you to generate a run-time exception for late assignments to fields of RValues of value types. Otherwise, set to False.</param>
    </member>
    <member name="T:Microsoft.VisualBasic.Strings">
      <summary>The Strings module contains procedures used to perform string operations.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.Strings.Asc(System.Char)">
      <summary>Returns an Integer value representing the character code corresponding to a character.</summary>
      <param name="String">Required. Any valid Char or String expression. If String is a String expression, only the first character of the string is used for input. If String is Nothing or contains no characters, an <see cref="T:System.ArgumentException"></see> error occurs.</param>
      <returns>Returns an Integer value representing the character code corresponding to a character.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.Strings.Asc(System.String)">
      <summary>Returns an Integer value representing the character code corresponding to a character.</summary>
      <param name="String">Required. Any valid Char or String expression. If String is a String expression, only the first character of the string is used for input. If String is Nothing or contains no characters, an <see cref="T:System.ArgumentException"></see> error occurs.</param>
      <returns>Returns an Integer value representing the character code corresponding to a character.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.Strings.AscW(System.Char)">
      <summary>Returns an Integer value representing the character code corresponding to a character.</summary>
      <param name="String">Required. Any valid Char or String expression. If String is a String expression, only the first character of the string is used for input. If String is Nothing or contains no characters, an <see cref="T:System.ArgumentException"></see> error occurs.</param>
      <returns>Returns an Integer value representing the character code corresponding to a character.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.Strings.AscW(System.String)">
      <summary>Returns an Integer value representing the character code corresponding to a character.</summary>
      <param name="String">Required. Any valid Char or String expression. If String is a String expression, only the first character of the string is used for input. If String is Nothing or contains no characters, an <see cref="T:System.ArgumentException"></see> error occurs.</param>
      <returns>Returns an Integer value representing the character code corresponding to a character.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.Strings.Chr(System.Int32)">
      <summary>Returns the character associated with the specified character code.</summary>
      <param name="CharCode">Required. An Integer expression representing the code point, or character code, for the character.</param>
      <returns>Returns the character associated with the specified character code.</returns>
      <exception cref="T:System.ArgumentException"><paramref name="CharCode">CharCode</paramref> &amp;lt; 0 or &amp;gt; 255 for Chr.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.Strings.ChrW(System.Int32)">
      <summary>Returns the character associated with the specified character code.</summary>
      <param name="CharCode">Required. An Integer expression representing the code point, or character code, for the character.</param>
      <returns>Returns the character associated with the specified character code.</returns>
      <exception cref="T:System.ArgumentException"><paramref name="CharCode">CharCode</paramref> &amp;lt; -32768 or &amp;gt; 65535 for ChrW.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.Strings.Filter(System.Object[],System.String,System.Boolean,Microsoft.VisualBasic.CompareMethod)">
      <summary>Returns a zero-based array containing a subset of a String array based on specified filter criteria.</summary>
      <param name="Source">Required. One-dimensional array of strings to be searched.</param>
      <param name="Match">Required. String to search for.</param>
      <param name="Include">Optional. Boolean value indicating whether to return substrings that include or exclude Match. If Include is True, the Filter function returns the subset of the array that contains Match as a substring. If Include is False, the Filter function returns the subset of the array that does not contain Match as a substring.</param>
      <param name="Compare">Optional. Numeric value indicating the kind of string comparison to use. See &amp;quot;Settings&amp;quot; for values.</param>
      <returns>Returns a zero-based array containing a subset of a String array based on specified filter criteria.</returns>
      <exception cref="T:System.ArgumentException"><paramref name="Source">Source</paramref> is Nothing or is not a one-dimensional array.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.Strings.Filter(System.String[],System.String,System.Boolean,Microsoft.VisualBasic.CompareMethod)">
      <summary>Returns a zero-based array containing a subset of a String array based on specified filter criteria.</summary>
      <param name="Source">Required. One-dimensional array of strings to be searched.</param>
      <param name="Match">Required. String to search for.</param>
      <param name="Include">Optional. Boolean value indicating whether to return substrings that include or exclude Match. If Include is True, the Filter function returns the subset of the array that contains Match as a substring. If Include is False, the Filter function returns the subset of the array that does not contain Match as a substring.</param>
      <param name="Compare">Optional. Numeric value indicating the kind of string comparison to use. See &amp;quot;Settings&amp;quot; for values.</param>
      <returns>Returns a zero-based array containing a subset of a String array based on specified filter criteria.</returns>
      <exception cref="T:System.ArgumentException"><paramref name="Source">Source</paramref> is Nothing or is not a one-dimensional array.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.Strings.Format(System.Object,System.String)">
      <summary>Returns a string formatted according to instructions contained in a format String expression.</summary>
      <param name="Expression">Required. Any valid expression.</param>
      <param name="Style">Optional. A valid named or user-defined format String expression.</param>
      <returns>Returns a string formatted according to instructions contained in a format String expression.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.Strings.FormatCurrency(System.Object,System.Int32,Microsoft.VisualBasic.TriState,Microsoft.VisualBasic.TriState,Microsoft.VisualBasic.TriState)">
      <summary>Returns an expression formatted as a currency value using the currency symbol defined in the system control panel.</summary>
      <param name="Expression">Required. Expression to be formatted.</param>
      <param name="NumDigitsAfterDecimal">Optional. Numeric value indicating how many places are displayed to the right of the decimal. Default value is –1, which indicates that the computer&amp;#39;s regional settings are used.</param>
      <param name="IncludeLeadingDigit">Optional. <see cref="T:Microsoft.VisualBasic.TriState"></see> enumeration that indicates whether or not a leading zero is displayed for fractional values. See &amp;quot;Remarks&amp;quot; for values.</param>
      <param name="UseParensForNegativeNumbers">Optional. <see cref="T:Microsoft.VisualBasic.TriState"></see> enumeration that indicates whether or not to place negative values within parentheses. See &amp;quot;Remarks&amp;quot; for values.</param>
      <param name="GroupDigits">Optional. <see cref="T:Microsoft.VisualBasic.TriState"></see> enumeration that indicates whether or not numbers are grouped using the group delimiter specified in the computer&amp;#39;s regional settings. See &amp;quot;Remarks&amp;quot; for values.</param>
      <returns>Returns an expression formatted as a currency value using the currency symbol defined in the system control panel.</returns>
      <exception cref="T:System.ArgumentException">Number of digits after decimal point is greater than 99.</exception>
      <exception cref="T:System.InvalidCastException">Type is not numeric.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.Strings.FormatDateTime(System.DateTime,Microsoft.VisualBasic.DateFormat)">
      <summary>Returns a string expression representing a date/time value.</summary>
      <param name="Expression">Required. Date expression to be formatted.</param>
      <param name="NamedFormat">Optional. Numeric value that indicates the date/time format used. If omitted, DateFormat.GeneralDate is used.</param>
      <returns>Returns a string expression representing a date/time value.</returns>
      <exception cref="T:System.ArgumentException"><paramref name="NamedFormat">NamedFormat</paramref> setting is not valid.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.Strings.FormatNumber(System.Object,System.Int32,Microsoft.VisualBasic.TriState,Microsoft.VisualBasic.TriState,Microsoft.VisualBasic.TriState)">
      <summary>Returns an expression formatted as a number.</summary>
      <param name="Expression">Required. Expression to be formatted.</param>
      <param name="NumDigitsAfterDecimal">Optional. Numeric value indicating how many places are displayed to the right of the decimal. The default value is –1, which indicates that the computer&amp;#39;s regional settings are used.</param>
      <param name="IncludeLeadingDigit">Optional. <see cref="T:Microsoft.VisualBasic.TriState"></see> constant that indicates whether a leading 0 is displayed for fractional values. See &amp;quot;Settings&amp;quot; for values.</param>
      <param name="UseParensForNegativeNumbers">Optional. <see cref="T:Microsoft.VisualBasic.TriState"></see> constant that indicates whether to place negative values within parentheses. See &amp;quot;Settings&amp;quot; for values.</param>
      <param name="GroupDigits">Optional. <see cref="T:Microsoft.VisualBasic.TriState"></see> constant that indicates whether or not numbers are grouped using the group delimiter specified in the locale settings. See &amp;quot;Settings&amp;quot; for values.</param>
      <returns>Returns an expression formatted as a number.</returns>
      <exception cref="T:System.InvalidCastException">Type is not numeric.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.Strings.FormatPercent(System.Object,System.Int32,Microsoft.VisualBasic.TriState,Microsoft.VisualBasic.TriState,Microsoft.VisualBasic.TriState)">
      <summary>Returns an expression formatted as a percentage (that is, multiplied by 100) with a trailing % character.</summary>
      <param name="Expression">Required. Expression to be formatted.</param>
      <param name="NumDigitsAfterDecimal">Optional. Numeric value indicating how many places to the right of the decimal are displayed. Default value is –1, which indicates that the locale settings are used.</param>
      <param name="IncludeLeadingDigit">Optional. <see cref="T:Microsoft.VisualBasic.TriState"></see> constant that indicates whether or not a leading zero displays for fractional values. See &amp;quot;Settings&amp;quot; for values.</param>
      <param name="UseParensForNegativeNumbers">Optional. <see cref="T:Microsoft.VisualBasic.TriState"></see> constant that indicates whether or not to place negative values within parentheses. See &amp;quot;Settings&amp;quot; for values.</param>
      <param name="GroupDigits">Optional. <see cref="T:Microsoft.VisualBasic.TriState"></see> constant that indicates whether or not numbers are grouped using the group delimiter specified in the locale settings. See &amp;quot;Settings&amp;quot; for values.</param>
      <returns>Returns an expression formatted as a percentage (that is, multiplied by 100) with a trailing % character.</returns>
      <exception cref="T:System.InvalidCastException">Type is not numeric.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.Strings.GetChar(System.String,System.Int32)">
      <summary>Returns a Char value representing the character from the specified index in the supplied string.</summary>
      <param name="str">Required. Any valid String expression.</param>
      <param name="Index">Required. Integer expression. The (1-based) index of the character in str to be returned.</param>
      <returns>Char value representing the character from the specified index in the supplied string.</returns>
      <exception cref="T:System.ArgumentException"><paramref name="str">str</paramref> is Nothing, <paramref name="Index">Index</paramref> &amp;lt; 1, or <paramref name="Index">Index</paramref> is greater than index of last character of <paramref name="str">str</paramref>.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.Strings.InStr(System.Int32,System.String,System.String,Microsoft.VisualBasic.CompareMethod)">
      <summary>Returns an integer specifying the start position of the first occurrence of one string within another.</summary>
      <param name="Start">Optional. Numeric expression that sets the starting position for each search. If omitted, search begins at the first character position. The start index is 1-based.</param>
      <param name="String1">Required. String expression being searched.</param>
      <param name="String2">Required. String expression sought.</param>
      <param name="Compare">Optional. Specifies the type of string comparison. If Compare is omitted, the Option Compare setting determines the type of comparison.</param>
      <returns> If  

  InStr returns  

 
          <paramref name="String1">String1</paramref> is zero length or Nothing 0  

 
          <paramref name="String2">String2</paramref> is zero length or Nothing
          <paramref name="start">start</paramref>
        
          <paramref name="String2">String2</paramref> is not found  

  0  

 
          <paramref name="String2">String2</paramref> is found within <paramref name="String1">String1</paramref> Position where match begins  

 
          <paramref name="Start">Start</paramref> &amp;gt; length of <paramref name="String1">String1</paramref> 0  

 </returns>
      <exception cref="T:System.ArgumentException"><paramref name="Start">Start</paramref> &amp;lt; 1.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.Strings.InStr(System.String,System.String,Microsoft.VisualBasic.CompareMethod)">
      <summary>Returns an integer specifying the start position of the first occurrence of one string within another.</summary>
      <param name="String1">Required. String expression being searched.</param>
      <param name="String2">Required. String expression sought.</param>
      <param name="Compare">Optional. Specifies the type of string comparison. If Compare is omitted, the Option Compare setting determines the type of comparison.</param>
      <returns> If  

  InStr returns  

 
          <paramref name="String1">String1</paramref> is zero length or Nothing 0  

 
          <paramref name="String2">String2</paramref> is zero length or Nothing The starting position for the search, which defaults to the first character position.  

 
          <paramref name="String2">String2</paramref> is not found  

  0  

 
          <paramref name="String2">String2</paramref> is found within <paramref name="String1">String1</paramref> Position where match begins  

 </returns>
    </member>
    <member name="M:Microsoft.VisualBasic.Strings.InStrRev(System.String,System.String,System.Int32,Microsoft.VisualBasic.CompareMethod)">
      <summary>Returns the position of the first occurrence of one string within another, starting from the right side of the string.</summary>
      <param name="StringCheck">Required. String expression being searched.</param>
      <param name="StringMatch">Required. String expression being searched for.</param>
      <param name="Start">Optional. Numeric expression setting the one-based starting position for each search, starting from the left side of the string. If Start is omitted then –1 is used, meaning the search begins at the last character position. Search then proceeds from right to left.</param>
      <param name="Compare">Optional. Numeric value indicating the kind of comparison to use when evaluating substrings. If omitted, a binary comparison is performed. See Settings for values.</param>
      <returns> If  

  InStrRev returns  

 
          <paramref name="StringCheck">StringCheck</paramref> is zero-length  

  0  

 
          <paramref name="StringMatch">StringMatch</paramref> is zero-length  

 
          <paramref name="Start">Start</paramref>
        
          <paramref name="StringMatch">StringMatch</paramref> is not found  

  0  

 
          <paramref name="StringMatch">StringMatch</paramref> is found within <paramref name="StringCheck">StringCheck</paramref> Position at which the first match is found, starting with the right side of the string.  

 
          <paramref name="Start">Start</paramref> is greater than length of <paramref name="StringMatch">StringMatch</paramref> 0  

 </returns>
      <exception cref="T:System.ArgumentException"><paramref name="Start">Start</paramref> = 0 or <paramref name="Start">Start</paramref> &amp;lt; -1.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.Strings.Join(System.Object[],System.String)">
      <summary>Returns a string created by joining a number of substrings contained in an array.</summary>
      <param name="SourceArray">Required. One-dimensional array containing substrings to be joined.</param>
      <param name="Delimiter">Optional. Any string, used to separate the substrings in the returned string. If omitted, the space character (&amp;quot; &amp;quot;) is used. If Delimiter is a zero-length string (&amp;quot;&amp;quot;) or Nothing, all items in the list are concatenated with no delimiters.</param>
      <returns>Returns a string created by joining a number of substrings contained in an array.</returns>
      <exception cref="T:System.ArgumentException"><paramref name="SourceArray">SourceArray</paramref> is not one dimensional.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.Strings.Join(System.String[],System.String)">
      <summary>Returns a string created by joining a number of substrings contained in an array.</summary>
      <param name="SourceArray">Required. One-dimensional array containing substrings to be joined.</param>
      <param name="Delimiter">Optional. Any string, used to separate the substrings in the returned string. If omitted, the space character (&amp;quot; &amp;quot;) is used. If Delimiter is a zero-length string (&amp;quot;&amp;quot;) or Nothing, all items in the list are concatenated with no delimiters.</param>
      <returns>Returns a string created by joining a number of substrings contained in an array.</returns>
      <exception cref="T:System.ArgumentException"><paramref name="SourceArray">SourceArray</paramref> is not one dimensional.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.Strings.LCase(System.Char)">
      <summary>Returns a string or character converted to lowercase.</summary>
      <param name="Value">Required. Any valid String or Char expression.</param>
      <returns>Returns a string or character converted to lowercase.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.Strings.LCase(System.String)">
      <summary>Returns a string or character converted to lowercase.</summary>
      <param name="Value">Required. Any valid String or Char expression.</param>
      <returns>Returns a string or character converted to lowercase.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.Strings.Left(System.String,System.Int32)">
      <summary>Returns a string containing a specified number of characters from the left side of a string.</summary>
      <param name="str">Required. String expression from which the leftmost characters are returned.</param>
      <param name="Length">Required. Integer expression. Numeric expression indicating how many characters to return. If 0, a zero-length string (&amp;quot;&amp;quot;) is returned. If greater than or equal to the number of characters in str, the entire string is returned.</param>
      <returns>Returns a string containing a specified number of characters from the left side of a string.</returns>
      <exception cref="T:System.ArgumentException"><paramref name="Length">Length</paramref> &amp;lt; 0.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.Strings.Len(System.UInt64)">
      <summary>Returns an integer containing either the number of characters in a string or the nominal number of bytes required to store a variable.</summary>
      <param name="Expression">Any valid String expression or variable name. If Expression is of type Object, the Len function returns the size as it will be written to the file by the FilePut function.</param>
      <returns>Returns an integer containing either the number of characters in a string or the nominal number of bytes required to store a variable.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.Strings.Len(System.UInt32)">
      <summary>Returns an integer containing either the number of characters in a string or the nominal number of bytes required to store a variable.</summary>
      <param name="Expression">Any valid String expression or variable name. If Expression is of type Object, the Len function returns the size as it will be written to the file by the FilePut function.</param>
      <returns>Returns an integer containing either the number of characters in a string or the nominal number of bytes required to store a variable.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.Strings.Len(System.UInt16)">
      <summary>Returns an integer containing either the number of characters in a string or the nominal number of bytes required to store a variable.</summary>
      <param name="Expression">Any valid String expression or variable name. If Expression is of type Object, the Len function returns the size as it will be written to the file by the FilePut function.</param>
      <returns>Returns an integer containing either the number of characters in a string or the nominal number of bytes required to store a variable.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.Strings.Len(System.String)">
      <summary>Returns an integer containing either the number of characters in a string or the nominal number of bytes required to store a variable.</summary>
      <param name="Expression">Any valid String expression or variable name. If Expression is of type Object, the Len function returns the size as it will be written to the file by the FilePut function.</param>
      <returns>Returns an integer containing either the number of characters in a string or the nominal number of bytes required to store a variable.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.Strings.Len(System.Single)">
      <summary>Returns an integer containing either the number of characters in a string or the nominal number of bytes required to store a variable.</summary>
      <param name="Expression">Any valid String expression or variable name. If Expression is of type Object, the Len function returns the size as it will be written to the file by the FilePut function.</param>
      <returns>Returns an integer containing either the number of characters in a string or the nominal number of bytes required to store a variable.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.Strings.Len(System.SByte)">
      <summary>Returns an integer containing either the number of characters in a string or the nominal number of bytes required to store a variable.</summary>
      <param name="Expression">Any valid String expression or variable name. If Expression is of type Object, the Len function returns the size as it will be written to the file by the FilePut function.</param>
      <returns>Returns an integer containing either the number of characters in a string or the nominal number of bytes required to store a variable.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.Strings.Len(System.Object)">
      <summary>Returns an integer containing either the number of characters in a string or the nominal number of bytes required to store a variable.</summary>
      <param name="Expression">Any valid String expression or variable name. If Expression is of type Object, the Len function returns the size as it will be written to the file by the FilePut function.</param>
      <returns>Returns an integer containing either the number of characters in a string or the nominal number of bytes required to store a variable.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.Strings.Len(System.Int64)">
      <summary>Returns an integer containing either the number of characters in a string or the nominal number of bytes required to store a variable.</summary>
      <param name="Expression">Any valid String expression or variable name. If Expression is of type Object, the Len function returns the size as it will be written to the file by the FilePut function.</param>
      <returns>Returns an integer containing either the number of characters in a string or the nominal number of bytes required to store a variable.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.Strings.Len(System.Int16)">
      <summary>Returns an integer containing either the number of characters in a string or the nominal number of bytes required to store a variable.</summary>
      <param name="Expression">Any valid String expression or variable name. If Expression is of type Object, the Len function returns the size as it will be written to the file by the FilePut function.</param>
      <returns>Returns an integer containing either the number of characters in a string or the nominal number of bytes required to store a variable.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.Strings.Len(System.Double)">
      <summary>Returns an integer containing either the number of characters in a string or the nominal number of bytes required to store a variable.</summary>
      <param name="Expression">Any valid String expression or variable name. If Expression is of type Object, the Len function returns the size as it will be written to the file by the FilePut function.</param>
      <returns>Returns an integer containing either the number of characters in a string or the nominal number of bytes required to store a variable.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.Strings.Len(System.Decimal)">
      <summary>Returns an integer containing either the number of characters in a string or the nominal number of bytes required to store a variable.</summary>
      <param name="Expression">Any valid String expression or variable name. If Expression is of type Object, the Len function returns the size as it will be written to the file by the FilePut function.</param>
      <returns>Returns an integer containing either the number of characters in a string or the nominal number of bytes required to store a variable.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.Strings.Len(System.DateTime)">
      <summary>Returns an integer containing either the number of characters in a string or the nominal number of bytes required to store a variable.</summary>
      <param name="Expression">Any valid String expression or variable name. If Expression is of type Object, the Len function returns the size as it will be written to the file by the FilePut function.</param>
      <returns>Returns an integer containing either the number of characters in a string or the nominal number of bytes required to store a variable.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.Strings.Len(System.Char)">
      <summary>Returns an integer containing either the number of characters in a string or the nominal number of bytes required to store a variable.</summary>
      <param name="Expression">Any valid String expression or variable name. If Expression is of type Object, the Len function returns the size as it will be written to the file by the FilePut function.</param>
      <returns>Returns an integer containing either the number of characters in a string or the nominal number of bytes required to store a variable.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.Strings.Len(System.Byte)">
      <summary>Returns an integer containing either the number of characters in a string or the nominal number of bytes required to store a variable.</summary>
      <param name="Expression">Any valid String expression or variable name. If Expression is of type Object, the Len function returns the size as it will be written to the file by the FilePut function.</param>
      <returns>Returns an integer containing either the number of characters in a string or the nominal number of bytes required to store a variable.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.Strings.Len(System.Boolean)">
      <summary>Returns an integer containing either the number of characters in a string or the nominal number of bytes required to store a variable.</summary>
      <param name="Expression">Any valid String expression or variable name. If Expression is of type Object, the Len function returns the size as it will be written to the file by the FilePut function.</param>
      <returns>Returns an integer containing either the number of characters in a string or the nominal number of bytes required to store a variable.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.Strings.Len(System.Int32)">
      <summary>Returns an integer containing either the number of characters in a string or the nominal number of bytes required to store a variable.</summary>
      <param name="Expression">Any valid String expression or variable name. If Expression is of type Object, the Len function returns the size as it will be written to the file by the FilePut function.</param>
      <returns>Returns an integer containing either the number of characters in a string or the nominal number of bytes required to store a variable.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.Strings.LSet(System.String,System.Int32)">
      <summary>Returns a left-aligned string containing the specified string adjusted to the specified length.</summary>
      <param name="Source">Required. String expression. Name of string variable.</param>
      <param name="Length">Required. Integer expression. Length of returned string.</param>
      <returns>Returns a left-aligned string containing the specified string adjusted to the specified length.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.Strings.LTrim(System.String)">
      <summary>Returns a string containing a copy of a specified string with no leading spaces (LTrim), no trailing spaces (RTrim), or no leading or trailing spaces (Trim).</summary>
      <param name="str">Required. Any valid String expression.</param>
      <returns>Returns a string containing a copy of a specified string with no leading spaces (LTrim), no trailing spaces (RTrim), or no leading or trailing spaces (Trim).</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.Strings.Mid(System.String,System.Int32)">
      <summary>Returns a string that contains all the characters starting from a specified position in a string.</summary>
      <param name="str">Required. String expression from which characters are returned.</param>
      <param name="Start">Required. Integer expression. Starting position of the characters to return. If Start is greater than the number of characters in str, the Mid function returns a zero-length string (&amp;quot;&amp;quot;). Start is one-based.</param>
      <returns>A string that consists of all the characters starting from the specified position in the string.</returns>
      <exception cref="T:System.ArgumentException"><paramref name="Start">Start</paramref> &amp;lt;= 0 or <paramref name="Length">Length</paramref> &amp;lt; 0.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.Strings.Mid(System.String,System.Int32,System.Int32)">
      <summary>Returns a string that contains a specified number of characters starting from a specified position in a string.</summary>
      <param name="str">Required. String expression from which characters are returned.</param>
      <param name="Start">Required. Integer expression. Starting position of the characters to return. If Start is greater than the number of characters in str, the Mid function returns a zero-length string (&amp;quot;&amp;quot;). Start is one based.</param>
      <param name="Length">Optional. Integer expression. Number of characters to return. If omitted or if there are fewer than Length characters in the text (including the character at position Start), all characters from the start position to the end of the string are returned.</param>
      <returns>A string that consists of the specified number of characters starting from the specified position in the string.</returns>
      <exception cref="T:System.ArgumentException"><paramref name="Start">Start</paramref> &amp;lt;= 0 or <paramref name="Length">Length</paramref> &amp;lt; 0.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.Strings.Replace(System.String,System.String,System.String,System.Int32,System.Int32,Microsoft.VisualBasic.CompareMethod)">
      <summary>Returns a string in which a specified substring has been replaced with another substring a specified number of times.</summary>
      <param name="Expression">Required. String expression containing substring to replace.</param>
      <param name="Find">Required. Substring being searched for.</param>
      <param name="Replacement">Required. Replacement substring.</param>
      <param name="Start">Optional. Position within Expression that starts a substring used for replacement. The return value of Replace is a string that begins at Start, with appropriate substitutions. If omitted, 1 is assumed.</param>
      <param name="Count">Optional. Number of substring substitutions to perform. If omitted, the default value is –1, which means &amp;quot;make all possible substitutions.&amp;quot;</param>
      <param name="Compare">Optional. Numeric value indicating the kind of comparison to use when evaluating substrings. See Settings for values.</param>
      <returns>Replace returns the following values.  
  If  

  Replace returns  

 <paramref name="Find">Find</paramref> is zero-length or Nothing Copy of <paramref name="Expression">Expression</paramref><paramref name="Replace">Replace</paramref> is zero-length  

  Copy of <paramref name="Expression">Expression</paramref> with no occurrences of <paramref name="Find">Find</paramref><paramref name="Expression">Expression</paramref> is zero-length or Nothing, or <paramref name="Start">Start</paramref> is greater than length of <paramref name="Expression">Expression</paramref>Nothing<paramref name="Count">Count</paramref> is 0  

  Copy of <paramref name="Expression">Expression</paramref></returns>
      <exception cref="T:System.ArgumentException"><paramref name="Count">Count</paramref> &amp;lt; -1 or <paramref name="Start">Start</paramref> &amp;lt;= 0.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.Strings.Right(System.String,System.Int32)">
      <summary>Returns a string containing a specified number of characters from the right side of a string.</summary>
      <param name="str">Required. String expression from which the rightmost characters are returned.</param>
      <param name="Length">Required. Integer. Numeric expression indicating how many characters to return. If 0, a zero-length string (&amp;quot;&amp;quot;) is returned. If greater than or equal to the number of characters in str, the entire string is returned.</param>
      <returns>Returns a string containing a specified number of characters from the right side of a string.</returns>
      <exception cref="T:System.ArgumentException"><paramref name="Length">Length</paramref> &amp;lt; 0.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.Strings.RSet(System.String,System.Int32)">
      <summary>Returns a right-aligned string containing the specified string adjusted to the specified length.</summary>
      <param name="Source">Required. String expression. Name of string variable.</param>
      <param name="Length">Required. Integer expression. Length of returned string.</param>
      <returns>Returns a right-aligned string containing the specified string adjusted to the specified length.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.Strings.RTrim(System.String)">
      <summary>Returns a string containing a copy of a specified string with no leading spaces (LTrim), no trailing spaces (RTrim), or no leading or trailing spaces (Trim).</summary>
      <param name="str">Required. Any valid String expression.</param>
      <returns>Returns a string containing a copy of a specified string with no leading spaces (LTrim), no trailing spaces (RTrim), or no leading or trailing spaces (Trim).</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.Strings.Space(System.Int32)">
      <summary>Returns a string consisting of the specified number of spaces.</summary>
      <param name="Number">Required. Integer expression. The number of spaces you want in the string.</param>
      <returns>Returns a string consisting of the specified number of spaces.</returns>
      <exception cref="T:System.ArgumentException"><paramref name="Number">Number</paramref> &amp;lt; 0.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.Strings.Split(System.String,System.String,System.Int32,Microsoft.VisualBasic.CompareMethod)">
      <summary>Returns a zero-based, one-dimensional array containing a specified number of substrings.</summary>
      <param name="Expression">Required. String expression containing substrings and delimiters.</param>
      <param name="Delimiter">Optional. Any single character used to identify substring limits. If Delimiter is omitted, the space character (&amp;quot; &amp;quot;) is assumed to be the delimiter.</param>
      <param name="Limit">Optional. Maximum number of substrings into which the input string should be split. The default, –1, indicates that the input string should be split at every occurrence of the Delimiter string.</param>
      <param name="Compare">Optional. Numeric value indicating the comparison to use when evaluating substrings. See &amp;quot;Settings&amp;quot; for values.</param>
      <returns>String array. If <paramref name="Expression">Expression</paramref> is a zero-length string (&amp;quot;&amp;quot;), Split returns a single-element array containing a zero-length string. If <paramref name="Delimiter">Delimiter</paramref> is a zero-length string, or if it does not appear anywhere in <paramref name="Expression">Expression</paramref>, Split returns a single-element array containing the entire <paramref name="Expression">Expression</paramref> string.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.Strings.StrComp(System.String,System.String,Microsoft.VisualBasic.CompareMethod)">
      <summary>Returns -1, 0, or 1, based on the result of a string comparison.</summary>
      <param name="String1">Required. Any valid String expression.</param>
      <param name="String2">Required. Any valid String expression.</param>
      <param name="Compare">Optional. Specifies the type of string comparison. If Compare is omitted, the Option Compare setting determines the type of comparison.</param>
      <returns>The StrComp function has the following return values.  
  If  

  StrComp returns  

 <paramref name="String1">String1</paramref> sorts ahead of <paramref name="String2">String2</paramref> -1  

 <paramref name="String1">String1</paramref> is equal to <paramref name="String2">String2</paramref> 0  

 <paramref name="String1">String1</paramref> sorts after <paramref name="String2">String2</paramref> 1  

 </returns>
      <exception cref="T:System.ArgumentException"><paramref name="Compare">Compare</paramref> value is not valid.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.Strings.StrConv(System.String,Microsoft.VisualBasic.VbStrConv,System.Int32)">
      <summary>Returns a string converted as specified.</summary>
      <param name="str">Required. String expression to be converted.</param>
      <param name="Conversion">Required. <see cref="T:Microsoft.VisualBasic.VbStrConv"></see> member. The enumeration value specifying the type of conversion to perform.</param>
      <param name="LocaleID">Optional. The LocaleID value, if different from the system LocaleID value. (The system LocaleID value is the default.)</param>
      <returns>Returns a string converted as specified.</returns>
      <exception cref="T:System.ArgumentException">Unsupported <paramref name="LocaleID">LocaleID</paramref>, <paramref name="Conversion">Conversion</paramref> &amp;lt; 0 or &amp;gt; 2048, or unsupported conversion for specified locale.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.Strings.StrDup(System.Int32,System.Char)">
      <summary>Returns a string or object consisting of the specified character repeated the specified number of times.</summary>
      <param name="Number">Required. Integer expression. The length to the string to be returned.</param>
      <param name="Character">Required. Any valid Char, String, or Object expression. Only the first character of the expression will be used. If Character is of type Object, it must contain either a Char or a String value.</param>
      <returns>Returns a string or object consisting of the specified character repeated the specified number of times.</returns>
      <exception cref="T:System.ArgumentException"><paramref name="Number">Number</paramref> is less than 0 or <paramref name="Character">Character</paramref> type is not valid.</exception>
      <exception cref="T:System.ArgumentNullException"><paramref name="Character">Character</paramref> is Nothing.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.Strings.StrDup(System.Int32,System.Object)">
      <summary>Returns a string or object consisting of the specified character repeated the specified number of times.</summary>
      <param name="Number">Required. Integer expression. The length to the string to be returned.</param>
      <param name="Character">Required. Any valid Char, String, or Object expression. Only the first character of the expression will be used. If Character is of type Object, it must contain either a Char or a String value.</param>
      <returns>Returns a string or object consisting of the specified character repeated the specified number of times.</returns>
      <exception cref="T:System.ArgumentException"><paramref name="Number">Number</paramref> is less than 0 or <paramref name="Character">Character</paramref> type is not valid.</exception>
      <exception cref="T:System.ArgumentNullException"><paramref name="Character">Character</paramref> is Nothing.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.Strings.StrDup(System.Int32,System.String)">
      <summary>Returns a string or object consisting of the specified character repeated the specified number of times.</summary>
      <param name="Number">Required. Integer expression. The length to the string to be returned.</param>
      <param name="Character">Required. Any valid Char, String, or Object expression. Only the first character of the expression will be used. If Character is of type Object, it must contain either a Char or a String value.</param>
      <returns>Returns a string or object consisting of the specified character repeated the specified number of times.</returns>
      <exception cref="T:System.ArgumentException"><paramref name="Number">Number</paramref> is less than 0 or <paramref name="Character">Character</paramref> type is not valid.</exception>
      <exception cref="T:System.ArgumentNullException"><paramref name="Character">Character</paramref> is Nothing.</exception>
    </member>
    <member name="M:Microsoft.VisualBasic.Strings.StrReverse(System.String)">
      <summary>Returns a string in which the character order of a specified string is reversed.</summary>
      <param name="Expression">Required. String expression whose characters are to be reversed. If Expression is a zero-length string (&amp;quot;&amp;quot;), a zero-length string is returned.</param>
      <returns>Returns a string in which the character order of a specified string is reversed.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.Strings.Trim(System.String)">
      <summary>Returns a string containing a copy of a specified string with no leading spaces (LTrim), no trailing spaces (RTrim), or no leading or trailing spaces (Trim).</summary>
      <param name="str">Required. Any valid String expression.</param>
      <returns>Returns a string containing a copy of a specified string with no leading spaces (LTrim), no trailing spaces (RTrim), or no leading or trailing spaces (Trim).</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.Strings.UCase(System.Char)">
      <summary>Returns a string or character containing the specified string converted to uppercase.</summary>
      <param name="Value">Required. Any valid String or Char expression.</param>
      <returns>Returns a string or character containing the specified string converted to uppercase.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.Strings.UCase(System.String)">
      <summary>Returns a string or character containing the specified string converted to uppercase.</summary>
      <param name="Value">Required. Any valid String or Char expression.</param>
      <returns>Returns a string or character containing the specified string converted to uppercase.</returns>
    </member>
    <member name="T:Microsoft.VisualBasic.CallType">
      <summary>Indicates the type of procedure being invoked when calling the CallByName function.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.CallType.Get">
      <summary>A property value is being retrieved.  This member is equivalent to the Visual Basic constant vbGet.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.CallType.Let">
      <summary>An Object property value is being determined. This member is equivalent to the Visual Basic constant vbLet.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.CallType.Method">
      <summary>A method is being invoked.  This member is equivalent to the Visual Basic constant vbMethod.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.CallType.Set">
      <summary>A property value is being determined.  This member is equivalent to the Visual Basic constant vbSet.</summary>
      <returns></returns>
    </member>
    <member name="T:Microsoft.VisualBasic.CompilerServices.DesignerGeneratedAttribute">
      <summary>When applied to a class, the compiler implicitly calls a component-initializing method from the default synthetic constructor.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.DesignerGeneratedAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.CompilerServices.DesignerGeneratedAttribute"></see> attribute.</summary>
    </member>
    <member name="T:Microsoft.VisualBasic.CompilerServices.IncompleteInitialization">
      <summary>The Visual Basic compiler uses this class during static local initialization; it is not meant to be called directly from your code. An exception of this type is thrown if a static local variable fails to initialize.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.IncompleteInitialization.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.CompilerServices.IncompleteInitialization"></see> class.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.IncompleteInitialization.#ctor(System.String)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.CompilerServices.IncompleteInitialization"></see> class.</summary>
      <param name="message">A string representing the message to be sent.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.IncompleteInitialization.#ctor(System.String,System.Exception)">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.CompilerServices.IncompleteInitialization"></see> class.</summary>
      <param name="message">A string representing the message to be sent.</param>
      <param name="innerException">An <see cref="T:System.Exception"></see> object.</param>
    </member>
    <member name="T:Microsoft.VisualBasic.CompilerServices.Conversions">
      <summary>Provides methods that perform various type conversions.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ChangeType(System.Object,System.Type)">
      <summary>Converts an object to the specified type.</summary>
      <param name="Expression">The object to convert.</param>
      <param name="TargetType">The type to which to convert the object.</param>
      <returns>An object of the specified target type.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.FallbackUserDefinedConversion(System.Object,System.Type)">
      <summary>Converts an object to the specified type.</summary>
      <param name="Expression">The object to convert.</param>
      <param name="TargetType">The type to which to convert the object.</param>
      <returns>An object of the specified target type.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.FromCharAndCount(System.Char,System.Int32)">
      <summary>Converts a <see cref="T:System.Char"></see> value to a string, given a byte count.</summary>
      <param name="Value">The Char value to convert.</param>
      <param name="Count">The byte count of the Char value.</param>
      <returns>The string representation of the specified value.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.FromCharArray(System.Char[])">
      <summary>Converts a <see cref="T:System.Char"></see> array to a string.</summary>
      <param name="Value">The Char array to convert.</param>
      <returns>The string representation of the specified array.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.FromCharArraySubset(System.Char[],System.Int32,System.Int32)">
      <summary>Converts a subset of a <see cref="T:System.Char"></see> array to a string.</summary>
      <param name="Value">The Char array to convert.</param>
      <param name="StartIndex">Zero-based index of the start position.</param>
      <param name="Length">Length of the subset in bytes.</param>
      <returns>The string representation of the specified array from the start position to the specified length.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToBoolean(System.Object)">
      <summary>Converts an object to a <see cref="T:System.Boolean"></see> value.</summary>
      <param name="Value">The object to convert.</param>
      <returns>A Boolean value. Returns False if the object is null; otherwise, True.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToBoolean(System.String)">
      <summary>Converts a string to a <see cref="T:System.Boolean"></see> value.</summary>
      <param name="Value">The string to convert.</param>
      <returns>A Boolean value. Returns False if the string is null; otherwise, True.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToByte(System.Object)">
      <summary>Converts an object to a <see cref="T:System.Byte"></see> value.</summary>
      <param name="Value">The object to convert.</param>
      <returns>The Byte value of the object.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToByte(System.String)">
      <summary>Converts a string to a <see cref="T:System.Byte"></see> value.</summary>
      <param name="Value">The string to convert.</param>
      <returns>The Byte value of the string.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToChar(System.Object)">
      <summary>Converts an object to a <see cref="T:System.Char"></see> value.</summary>
      <param name="Value">The object to convert.</param>
      <returns>The Char value of the object.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToChar(System.String)">
      <summary>Converts a string to a <see cref="T:System.Char"></see> value.</summary>
      <param name="Value">The string to convert.</param>
      <returns>The Char value of the string.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToCharArrayRankOne(System.String)">
      <summary>Converts a string to a one-dimensional <see cref="T:System.Char"></see> array.</summary>
      <param name="Value">The string to convert.</param>
      <returns>A one-dimensional Char array.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToCharArrayRankOne(System.Object)">
      <summary>Converts an object to a one-dimensional <see cref="T:System.Char"></see> array.</summary>
      <param name="Value">The object to convert.</param>
      <returns>A one-dimensional Char array.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToDate(System.Object)">
      <summary>Converts an object to a <see cref="T:System.DateTime"></see> value.</summary>
      <param name="Value">The object to convert.</param>
      <returns>The DateTime value of the object.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToDate(System.String)">
      <summary>Converts a string to a <see cref="T:System.DateTime"></see> value.</summary>
      <param name="Value">The string to convert.</param>
      <returns>The DateTime value of the string.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToDecimal(System.Boolean)">
      <summary>Converts a <see cref="T:System.Boolean"></see> value to a <see cref="T:System.Decimal"></see> value.</summary>
      <param name="Value">A Boolean value to convert.</param>
      <returns>The Decimal value of the Boolean value.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToDecimal(System.Object)">
      <summary>Converts an object to a <see cref="T:System.Decimal"></see> value.</summary>
      <param name="Value">The object to convert.</param>
      <returns>The Decimal value of the object.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToDecimal(System.String)">
      <summary>Converts a string to a <see cref="T:System.Decimal"></see> value.</summary>
      <param name="Value">The string to convert.</param>
      <returns>The Decimal value of the string.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToDouble(System.Object)">
      <summary>Converts an object to a <see cref="T:System.Double"></see> value.</summary>
      <param name="Value">The object to convert.</param>
      <returns>The Double value of the object.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToDouble(System.String)">
      <summary>Converts a string to a <see cref="T:System.Double"></see> value.</summary>
      <param name="Value">The string to convert.</param>
      <returns>The Double value of the string.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToGenericParameter``1(System.Object)">
      <summary>Converts an object to a generic type <paramref name="T">T</paramref>.</summary>
      <param name="Value">The object to convert.</param>
      <typeparam name="T">The type to convert Value to.</typeparam>
      <returns>A structure or object of generic type <paramref name="T">T</paramref>.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToInteger(System.Object)">
      <summary>Converts an object to an integer value.</summary>
      <param name="Value">The object to convert.</param>
      <returns>The int value of the object.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToInteger(System.String)">
      <summary>Converts a string to an integer value.</summary>
      <param name="Value">The string to convert.</param>
      <returns>The int value of the string.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToLong(System.Object)">
      <summary>Converts an object to a Long value.</summary>
      <param name="Value">The object to convert.</param>
      <returns>The Long value of the object.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToLong(System.String)">
      <summary>Converts a string to a Long value.</summary>
      <param name="Value">The string to convert.</param>
      <returns>The Long value of the string.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToSByte(System.String)">
      <summary>Converts a string to an <see cref="T:System.SByte"></see> value.</summary>
      <param name="Value">The string to convert.</param>
      <returns>The SByte value of the string.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToSByte(System.Object)">
      <summary>Converts an object to an <see cref="T:System.SByte"></see> value.</summary>
      <param name="Value">The object to convert.</param>
      <returns>The SByte value of the object.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToShort(System.Object)">
      <summary>Converts an object to a Short value.</summary>
      <param name="Value">The object to convert.</param>
      <returns>The Short value of the object.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToShort(System.String)">
      <summary>Converts a string to a Short value.</summary>
      <param name="Value">The string to convert.</param>
      <returns>The Short value of the string.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToSingle(System.Object)">
      <summary>Converts an object to a <see cref="T:System.Single"></see> value.</summary>
      <param name="Value">The object to convert.</param>
      <returns>The Single value of the object.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToSingle(System.String)">
      <summary>Converts a <see cref="T:System.String"></see> to a <see cref="T:System.Single"></see> value.</summary>
      <param name="Value">The string to convert.</param>
      <returns>The Single value of the string.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToString(System.Int64)">
      <summary>Converts a Long value to a <see cref="T:System.String"></see> value.</summary>
      <param name="Value">The Long value to convert.</param>
      <returns>The String representation of the Long value.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToString(System.Single,System.Globalization.NumberFormatInfo)">
      <summary>Converts a <see cref="T:System.Single"></see> value to a <see cref="T:System.String"></see> value, using the specified number format.</summary>
      <param name="Value">The Single value to convert.</param>
      <param name="NumberFormat">The number format to use, according to <see cref="T:System.Globalization.NumberFormatInfo"></see>.</param>
      <returns>The String representation of the Single value.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToString(System.Double,System.Globalization.NumberFormatInfo)">
      <summary>Converts a <see cref="T:System.Double"></see> value to a <see cref="T:System.String"></see> value, using the specified number format.</summary>
      <param name="Value">The Double value to convert.</param>
      <param name="NumberFormat">The number format to use, according to <see cref="T:System.Globalization.NumberFormatInfo"></see>.</param>
      <returns>The String representation of the Double value.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToString(System.Decimal,System.Globalization.NumberFormatInfo)">
      <summary>Converts a <see cref="T:System.Decimal"></see> value to a <see cref="T:System.String"></see> value, using the specified number format.</summary>
      <param name="Value">The decimal value to convert.</param>
      <param name="NumberFormat">The number format to use, according to <see cref="T:System.Globalization.NumberFormatInfo"></see>.</param>
      <returns>The String representation of the Decimal value.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToString(System.UInt64)">
      <summary>Converts a Ulong value to a <see cref="T:System.String"></see> value.</summary>
      <param name="Value">The Ulong value to convert.</param>
      <returns>The String representation of the Ulong value.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToString(System.UInt32)">
      <summary>Converts a uint value to a <see cref="T:System.String"></see> value.</summary>
      <param name="Value">The Uint value to convert.</param>
      <returns>The String representation of the Uint value.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToString(System.Single)">
      <summary>Converts a <see cref="T:System.Single"></see> value (a single-precision floating point number) to a <see cref="T:System.String"></see> value.</summary>
      <param name="Value">The Single value to convert.</param>
      <returns>The String representation of the Single value.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToString(System.Object)">
      <summary>Converts an object to a <see cref="T:System.String"></see> value.</summary>
      <param name="Value">The object to convert.</param>
      <returns>The String representation of the object.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToString(System.Int32)">
      <summary>Converts an integer value to a <see cref="T:System.String"></see> value.</summary>
      <param name="Value">The int value to convert.</param>
      <returns>The String representation of the int value.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToString(System.Int16)">
      <summary>Converts a Short value to a <see cref="T:System.String"></see> value.</summary>
      <param name="Value">The Short value to convert.</param>
      <returns>The String representation of the Short value.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToString(System.Double)">
      <summary>Converts a <see cref="T:System.Double"></see> value to a <see cref="T:System.String"></see> value.</summary>
      <param name="Value">The Double value to convert.</param>
      <returns>The String representation of the Double value.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToString(System.Decimal)">
      <summary>Converts a <see cref="T:System.Decimal"></see> value to a <see cref="T:System.String"></see> value.</summary>
      <param name="Value">The Decimal value to convert.</param>
      <returns>The String representation of the Decimal value.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToString(System.DateTime)">
      <summary>Converts a <see cref="T:System.DateTime"></see> value to a <see cref="T:System.String"></see> value.</summary>
      <param name="Value">The DateTime value to convert.</param>
      <returns>The String representation of the DateTime value.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToString(System.Char)">
      <summary>Converts a <see cref="T:System.Char"></see> value to a <see cref="T:System.String"></see>.</summary>
      <param name="Value">The Char value to convert.</param>
      <returns>The String representation of the Char value.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToString(System.Byte)">
      <summary>Converts a <see cref="T:System.Byte"></see> value to a <see cref="T:System.String"></see>.</summary>
      <param name="Value">The Byte value to convert.</param>
      <returns>The String representation of the Byte value.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToString(System.Boolean)">
      <summary>Converts a <see cref="T:System.Boolean"></see> value to a <see cref="T:System.String"></see>.</summary>
      <param name="Value">The Boolean value to convert.</param>
      <returns>The String representation of the Boolean value.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToUInteger(System.Object)">
      <summary>Converts an object to a Uint value.</summary>
      <param name="Value">The object to convert.</param>
      <returns>The Uint value of the object.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToUInteger(System.String)">
      <summary>Converts a string to a Uint value.</summary>
      <param name="Value">The string to convert.</param>
      <returns>The Uint value of the string.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToULong(System.Object)">
      <summary>Converts an object to a Ulong value.</summary>
      <param name="Value">The object to convert.</param>
      <returns>The Ulong value of the object.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToULong(System.String)">
      <summary>Converts a string to a Ulong value.</summary>
      <param name="Value">The string to convert.</param>
      <returns>The Ulong value of the string.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToUShort(System.Object)">
      <summary>Converts an object to a Ushort value.</summary>
      <param name="Value">The object to convert.</param>
      <returns>The Ushort value of the object.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToUShort(System.String)">
      <summary>Converts a string to a Ushort value.</summary>
      <param name="Value">The string to convert.</param>
      <returns>The Ushort value of the string.</returns>
    </member>
    <member name="T:Microsoft.VisualBasic.CompilerServices.StandardModuleAttribute">
      <summary>This class provides attributes that are applied to the standard module construct when it is emitted to Intermediate Language (IL). It is not intended to be called directly from your code.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.StandardModuleAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.CompilerServices.StandardModuleAttribute"></see> class.</summary>
    </member>
    <member name="T:Microsoft.VisualBasic.CompilerServices.StaticLocalInitFlag">
      <summary>The Visual Basic compiler uses this class internally when initializing static local members; it is not meant to be called directly from your code.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.StaticLocalInitFlag.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.CompilerServices.StaticLocalInitFlag"></see> class.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.CompilerServices.StaticLocalInitFlag.State">
      <summary>Returns the state of the static local member&amp;#39;s initialization flag (initialized or not).</summary>
      <returns></returns>
    </member>
    <member name="T:Microsoft.VisualBasic.CompilerServices.Utils">
      <summary>Contains utilities that the Visual Basic compiler uses.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Utils.CopyArray(System.Array,System.Array)">
      <summary>Used by the Visual Basic compiler as a helper for Redim.</summary>
      <param name="arySrc">The array to be copied.</param>
      <param name="aryDest">The destination array.</param>
      <returns>The copied array.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Utils.GetResourceString(System.String,System.String[])">
      <summary>Retrieves and formats a localized resource string or error message.</summary>
      <param name="ResourceKey">The identifier of the string or error message to retrieve.</param>
      <param name="Args">An array of parameters to replace placeholders in the string or error message.</param>
      <returns>A formatted resource string or error message.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Utils.MethodToString(System.Reflection.MethodBase)">
      <summary>Returns a Visual Basic method signature.</summary>
      <param name="Method">A <see cref="T:System.Reflection.MethodBase"></see> object to return a Visual Basic method signature for.</param>
      <returns>The Visual Basic method signature for the supplied <see cref="System.Reflection.MethodBase"></see> object.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Utils.SetCultureInfo(System.Globalization.CultureInfo)">
      <summary>Sets the culture of the current thread.</summary>
      <param name="Culture">A <see cref="T:System.Globalization.CultureInfo"></see> object to set as the culture of the current thread.</param>
      <returns>The previous value of the <see cref="System.Threading.Thread.CurrentCulture"></see> property for the current thread.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Utils.ThrowException(System.Int32)">
      <summary>Throws a localized Visual Basic exception.</summary>
      <param name="hr">The Visual Basic error identifier.</param>
    </member>
    <member name="T:Microsoft.VisualBasic.Constants">
      <summary>The Constants module contains miscellaneous constants. These constants can be used anywhere in your code.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbAbort">
      <summary>Indicates that the Abort button was clicked in a message box. Returned by the MsgBox function.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbAbortRetryIgnore">
      <summary>Indicates that the Abort, Retry, and Ignore buttons will be displayed when the MsgBox function is called.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbApplicationModal">
      <summary>Indicates that the message box will be displayed as a modal dialog box when the MsgBox function is called.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbArchive">
      <summary>Indicates that the file has changed since the last backup operation for file-access functions.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbArray">
      <summary>Indicates that the type of a variant object is an array. Returned by the VarType function.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbBack">
      <summary>Represents a backspace character for print and display functions.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbBinaryCompare">
      <summary>Specifies that a binary comparison should be performed when comparison functions are called.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbBoolean">
      <summary>Indicates that the type of a variant object is Boolean. Returned by the VarType function.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbByte">
      <summary>Indicates that the type of a variant object is Byte. Returned by the VarType function.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbCancel">
      <summary>Indicates that the Cancel button was clicked in a message box. Returned by the MsgBox function.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbCr">
      <summary>Represents a carriage-return character for print and display functions.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbCritical">
      <summary>Indicates that the critical message icon will be displayed when the MsgBox function is called.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbCrLf">
      <summary>Represents a carriage-return character combined with a linefeed character for print and display functions.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbCurrency">
      <summary>Indicates that the type of a variant object is Currency. Returned by the VarType function.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbDate">
      <summary>Indicates that the type of a variant object is Date. Returned by the VarType function.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbDecimal">
      <summary>Indicates that the type of a variant object is Decimal. Returned by the VarType function.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbDefaultButton1">
      <summary>Indicates that the leftmost button is selected as the default button when the message box appears.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbDefaultButton2">
      <summary>Indicates that the second button from the left is selected as the default button when the message box appears.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbDefaultButton3">
      <summary>Indicates that the third button from the left is selected as the default button when the message box appears.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbDirectory">
      <summary>Indicates that the file is a directory or folder for file-access functions.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbDouble">
      <summary>Indicates that the type of a variant object is Double. Returned by the VarType function.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbEmpty">
      <summary>Indicates that the type of a variant object is Empty. Returned by the VarType function.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbExclamation">
      <summary>Indicates that the exclamation icon will be displayed when the MsgBox function is called.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbFalse">
      <summary>Indicates that a Boolean value of False should be used when number-formatting functions are called.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbFirstFourDays">
      <summary>Indicates that the first week of the year that has at least four days should be used when date-related functions are called.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbFirstFullWeek">
      <summary>Indicates that the first full week of the year should be used when date-related functions are called.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbFirstJan1">
      <summary>Indicates that the week of the year in which January 1 occurs should be used when date-related functions are called.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbFormFeed">
      <summary>Represents a form-feed character for print functions.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbFriday">
      <summary>Specifies that Friday should be used as the first day of the week when date-related functions are called.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbGeneralDate">
      <summary>Indicates that the general date format for the current culture should be used when the FormatDateTime function is called.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbGet">
      <summary>Specifies that a property value should be retrieved when the CallByName function is called.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbHidden">
      <summary>Indicates that the file is a hidden file for file-access functions.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbHide">
      <summary>Indicates that the window style is hidden for the invoked program when the Shell function is called.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbHiragana">
      <summary>Indicates Hiragana characters should be converted to Katakana characters when the StrConv function is called.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbIgnore">
      <summary>Indicates that the Ignore button was clicked in a message box. Returned by the MsgBox function.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbInformation">
      <summary>Indicates that the information icon will display when the MsgBox function is called.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbInteger">
      <summary>Indicates that the type of a variant object is Integer. Returned by the VarType function.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbKatakana">
      <summary>Indicates that Katakana characters should be converted to Hiragana characters when the StrConv function is called.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbLet">
      <summary>Indicates that a property value should be set to an object instance when the CallByName function is called.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbLf">
      <summary>Represents a linefeed character for print and display functions.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbLinguisticCasing">
      <summary>Indicates that characters should be converted to use linguistic rules for casing instead of file system rules for casing to when the StrConv function is called.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbLong">
      <summary>Indicates the type of a variant object is Long. Returned by the VarType function.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbLongDate">
      <summary>Indicates that the long date format for the current culture should be used when the FormatDateTime function is called.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbLongTime">
      <summary>Indicates that the long time format for the current culture should be used when the FormatDateTime function is called.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbLowerCase">
      <summary>Indicates that characters should be converted to lowercase when the StrConv function is called.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbMaximizedFocus">
      <summary>Indicates that the window style is maximized with focus for the invoked program when the Shell function is called.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbMethod">
      <summary>Specifies that a method should be called when the CallByName function is called.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbMinimizedFocus">
      <summary>Indicates that the window style is minimized with focus for the invoked program when the Shell function is called.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbMinimizedNoFocus">
      <summary>Indicates that the window style is minimized without focus for the invoked program when the Shell function is called.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbMonday">
      <summary>Specifies that Monday should be used as the first day of the week when date-related functions are called.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbMsgBoxHelp">
      <summary>Indicates that the Help button will be displayed when the MsgBox function is called.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbMsgBoxRight">
      <summary>Indicates that text will be right-aligned when the MsgBox function is called.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbMsgBoxRtlReading">
      <summary>Indicates that right-to-left reading text (Hebrew and Arabic systems) will be displayed when the MsgBox function is called.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbMsgBoxSetForeground">
      <summary>Indicates that the message box will display in the foreground when the MsgBox function is called.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbNarrow">
      <summary>Indicates that wide (double-byte) characters should be converted to narrow (single-byte) characters when the StrConv function is called.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbNewLine">
      <summary>Represents a newline character for print and display functions.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbNo">
      <summary>Indicates that the No button was clicked in a message box. Returned by the MsgBox function.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbNormal">
      <summary>Indicates that the file is a normal file for file-access functions.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbNormalFocus">
      <summary>Indicates that the window style is normal with focus for the invoked program when the Shell function is called.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbNormalNoFocus">
      <summary>Indicates that the window style is normal without focus for the invoked program when the Shell function is called.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbNull">
      <summary>Indicates that the type of a variant object is Nothing. Returned by the VarType function.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbNullChar">
      <summary>Represents a null character for print and display functions.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbNullString">
      <summary>Represents a zero-length string for print and display functions, and for calling external procedures.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbObject">
      <summary>Indicates that the type of a variant object is Object. Returned by the VarType function.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbObjectError">
      <summary>Represents the object error number. User-defined error numbers should be greater than this value.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbOK">
      <summary>Indicates that the OK button was clicked in a message box. Returned by the MsgBox function.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbOKCancel">
      <summary>Indicates that the OK and Cancel buttons will be displayed when the MsgBox function is called.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbOKOnly">
      <summary>Indicates that only the OK button will be displayed when the MsgBox function is called.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbProperCase">
      <summary>Indicates that the first letter of every word in a string should be converted to uppercase and the remaining characters to lowercase when the StrConv function is called.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbQuestion">
      <summary>Indicates that the question icon will be displayed when the MsgBox function is called.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbReadOnly">
      <summary>Indicates that the file is a read-only file for file-access functions.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbRetry">
      <summary>Indicates that the Retry button was clicked in a message box. Returned by the MsgBox function.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbRetryCancel">
      <summary>Indicates that the Retry and Cancel buttons will be displayed when the MsgBox function is called.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbSaturday">
      <summary>Specifies that Saturday should be used as the first day of the week when date-related functions are called.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbSet">
      <summary>Indicates that a property value should be set when the CallByName function is called.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbShortDate">
      <summary>Indicates that the short-date format for the current culture should be used when the FormatDateTime function is called.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbShortTime">
      <summary>Indicates that the short-time format for the current culture should be used when the FormatDateTime function is called.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbSimplifiedChinese">
      <summary>Indicates that characters should be converted to Simplified Chinese when the StrConv function is called.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbSingle">
      <summary>Indicates that the type of a variant object is Single. Returned by the VarType function.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbString">
      <summary>Indicates that the type of a variant object is String. Returned by the VarType function.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbSunday">
      <summary>Specifies that Sunday should be used as the first day of the week when date-related functions are called.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbSystem">
      <summary>Indicates that the file is a system file for file-access functions.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbSystemModal">
      <summary>Indicates that the message box will be displayed as a modal dialog box when the MsgBox function is called.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbTab">
      <summary>Represents a tab character for print and display functions.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbTextCompare">
      <summary>Indicates that a text comparison should be performed when comparison functions are called.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbThursday">
      <summary>Specifies that Thursday should be used as the first day of the week when date-related functions are called.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbTraditionalChinese">
      <summary>Indicates that characters should be converted to Traditional Chinese when the StrConv function is called.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbTrue">
      <summary>Indicates that a Boolean value of True should be used when number-formatting functions are called.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbTuesday">
      <summary>Specifies that Tuesday should be used as the first day of the week when date-related functions are called.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbUpperCase">
      <summary>Indicates that characters should be converted to uppercase when the StrConv function is called.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbUseDefault">
      <summary>Indicates that the default Boolean value should be used when number-formatting functions are called.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbUserDefinedType">
      <summary>Indicates that the type of a variant object is a user-defined type. Returned by the VarType function.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbUseSystem">
      <summary>Indicates that the week specified by your system as the first week of the year should be used when date-related functions are called.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbUseSystemDayOfWeek">
      <summary>Indicates that the day specified by your system as the first day of the week should be used when date-related functions are called.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbVariant">
      <summary>Indicates that the type of a variant object is Variant. Returned by the VarType function.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbVerticalTab">
      <summary>Represents a carriage-return character for print functions.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbVolume">
      <summary>Indicates the volume label file attribute for file-access functions.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbWednesday">
      <summary>Specifies that Wednesday should be used as the first day of the week when date-related functions are called.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbWide">
      <summary>Indicates that narrow (single-byte) characters should be converted to wide (double-byte) characters when the StrConv function is called.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbYes">
      <summary>Indicates that the Yes button was clicked in a message box. Returned by the MsgBox function.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbYesNo">
      <summary>Indicates that the Yes and No buttons will be displayed when the MsgBox function is called.</summary>
      <returns></returns>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbYesNoCancel">
      <summary>Indicates that the Yes, No, and Cancel buttons will be displayed when the MsgBox function is called.</summary>
      <returns></returns>
    </member>
    <member name="T:Microsoft.VisualBasic.CompilerServices.ObjectFlowControl.ForLoopControl">
      <summary>Provides services to the Visual Basic compiler for compiling For...Next loops.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.ObjectFlowControl.ForLoopControl.ForLoopInitObj(System.Object,System.Object,System.Object,System.Object,System.Object@,System.Object@)">
      <summary>Initializes a For...Next loop.</summary>
      <param name="Counter">The loop counter variable.</param>
      <param name="Start">The initial value of the loop counter.</param>
      <param name="Limit">The value of the To option.</param>
      <param name="StepValue">The value of the Step option.</param>
      <param name="LoopForResult">An object that contains verified values for loop values.</param>
      <param name="CounterResult">The counter value for the next loop iteration.</param>
      <returns>False if the loop has terminated; otherwise, True.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.ObjectFlowControl.ForLoopControl.ForNextCheckDec(System.Decimal,System.Decimal,System.Decimal)">
      <summary>Checks for valid values for the loop counter, Step, and To values.</summary>
      <param name="count">Required. A Decimal value that represents the initial value passed for the loop counter variable.</param>
      <param name="limit">Required. A Decimal value that represents the value passed by using the To keyword.</param>
      <param name="StepValue">Required. A Decimal value that represents the value passed by using the Step keyword.</param>
      <returns>True if <paramref name="StepValue">StepValue</paramref> is greater than zero and <paramref name="count">count</paramref> is less than or equal to <paramref name="limit">limit</paramref> or <paramref name="StepValue">StepValue</paramref> is less than or equal to zero and <paramref name="count">count</paramref> is greater than or equal to <paramref name="limit">limit</paramref>; otherwise, False.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.ObjectFlowControl.ForLoopControl.ForNextCheckObj(System.Object,System.Object,System.Object@)">
      <summary>Increments a For...Next loop.</summary>
      <param name="Counter">The loop counter variable.</param>
      <param name="LoopObj">An object that contains verified values for loop values.</param>
      <param name="CounterResult">The counter value for the next loop iteration.</param>
      <returns>False if the loop has terminated; otherwise, True.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.ObjectFlowControl.ForLoopControl.ForNextCheckR4(System.Single,System.Single,System.Single)">
      <summary>Checks for valid values for the loop counter, Step, and To values.</summary>
      <param name="count">Required. A Single value that represents the initial value passed for the loop counter variable.</param>
      <param name="limit">Required. A Single value that represents the value passed by using the To keyword.</param>
      <param name="StepValue">Required. A Single value that represents the value passed by using the Step keyword.</param>
      <returns>True if <paramref name="StepValue">StepValue</paramref> is greater than zero and <paramref name="count">count</paramref> is less than or equal to <paramref name="limit">limit</paramref>, or if <paramref name="StepValue">StepValue</paramref> is less than or equal to zero and <paramref name="count">count</paramref> is greater than or equal to <paramref name="limit">limit</paramref>; otherwise, False.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.ObjectFlowControl.ForLoopControl.ForNextCheckR8(System.Double,System.Double,System.Double)">
      <summary>Checks for valid values for the loop counter, Step, and To values.</summary>
      <param name="count">Required. A Double value that represents the initial value passed for the loop counter variable.</param>
      <param name="limit">Required. A Double value that represents the value passed by using the To keyword.</param>
      <param name="StepValue">Required. A Double value that represents the value passed by using the Step keyword.</param>
      <returns>True if <paramref name="StepValue">StepValue</paramref> is greater than zero and <paramref name="count">count</paramref> is less than or equal to <paramref name="limit">limit</paramref>, or if <paramref name="StepValue">StepValue</paramref> is less than or equal to zero and <paramref name="count">count</paramref> is greater than or equal to <paramref name="limit">limit</paramref>; otherwise, False.</returns>
    </member>
    <member name="T:Microsoft.VisualBasic.CompilerServices.ObjectFlowControl">
      <summary>The Visual Basic compiler uses this class for object flow control; it is not meant to be called directly from your code.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.ObjectFlowControl.CheckForSyncLockOnValueType(System.Object)">
      <summary>Checks for a synchronization lock on the specified type.</summary>
      <param name="Expression">The data type for which to check for synchronization lock.</param>
    </member>
    <member name="T:Microsoft.VisualBasic.CompilerServices.Operators">
      <summary>Provides late-bound math operators, such as <see cref="M:Microsoft.VisualBasic.CompilerServices.Operators.AddObject(System.Object,System.Object)"></see> and <see cref="M:Microsoft.VisualBasic.CompilerServices.Operators.CompareObject(System.Object,System.Object,System.Boolean)"></see>, which the Visual Basic compiler uses internally.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.AddObject(System.Object,System.Object)">
      <summary>Represents the Visual Basic addition (+) operator.</summary>
      <param name="Left">Required. Any numeric expression.</param>
      <param name="Right">Required. Any numeric expression.</param>
      <returns>The sum of <paramref name="Left">Left</paramref> and <paramref name="Right">Right</paramref>.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.AndObject(System.Object,System.Object)">
      <summary>Represents the Visual Basic And operator.</summary>
      <param name="Left">Required. Any Boolean or numeric expression.</param>
      <param name="Right">Required. Any Boolean or numeric expression.</param>
      <returns>For Boolean operations, True if both <paramref name="Left">Left</paramref> and <paramref name="Right">Right</paramref> evaluate to True; otherwise, False. For bitwise operations, 1 if both <paramref name="Left">Left</paramref> and <paramref name="Right">Right</paramref> evaluate to 1; otherwise, 0.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.CompareObject(System.Object,System.Object,System.Boolean)">
      <summary>Represents Visual Basic comparison operators.</summary>
      <param name="Left">Required. Any expression.</param>
      <param name="Right">Required. Any expression.</param>
      <param name="TextCompare">Required. True to perform a case-insensitive string comparison; otherwise, False.</param>
      <returns> Value  

  Condition  

  -1  

 
          <paramref name="Left">Left</paramref> is less than <paramref name="Right">Right</paramref>.  

  0  

 
          <paramref name="Left">Left</paramref> and <paramref name="Right">Right</paramref> are equal.  

  1  

 
          <paramref name="Left">Left</paramref> is greater than <paramref name="Right">Right</paramref>.  

 </returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.CompareObjectEqual(System.Object,System.Object,System.Boolean)">
      <summary>Represents the Visual Basic equal (=) operator.</summary>
      <param name="Left">Required. Any expression.</param>
      <param name="Right">Required. Any expression.</param>
      <param name="TextCompare">Required. True to perform a case-insensitive string comparison; otherwise, False.</param>
      <returns>True if <paramref name="Left">Left</paramref> and <paramref name="Right">Right</paramref> are equal; otherwise, False.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.CompareObjectGreater(System.Object,System.Object,System.Boolean)">
      <summary>Represents the Visual Basic greater-than (&amp;gt;) operator.</summary>
      <param name="Left">Required. Any expression.</param>
      <param name="Right">Required. Any expression.</param>
      <param name="TextCompare">Required. True to perform a case-insensitive string comparison; otherwise, False.</param>
      <returns>True if <paramref name="Left">Left</paramref> is greater than <paramref name="Right">Right</paramref>; otherwise, False.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.CompareObjectGreaterEqual(System.Object,System.Object,System.Boolean)">
      <summary>Represents the Visual Basic greater-than or equal-to (&amp;gt;=) operator.</summary>
      <param name="Left">Required. Any expression.</param>
      <param name="Right">Required. Any expression.</param>
      <param name="TextCompare">Required. True to perform a case-insensitive string comparison; otherwise, False.</param>
      <returns>True if <paramref name="Left">Left</paramref> is greater than or equal to <paramref name="Right">Right</paramref>; otherwise, False.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.CompareObjectLess(System.Object,System.Object,System.Boolean)">
      <summary>Represents the Visual Basic less-than (&amp;lt;) operator.</summary>
      <param name="Left">Required. Any expression.</param>
      <param name="Right">Required. Any expression.</param>
      <param name="TextCompare">Required. True to perform a case-insensitive string comparison; otherwise, False.</param>
      <returns>True if <paramref name="Left">Left</paramref> is less than <paramref name="Right">Right</paramref>; otherwise, False.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.CompareObjectLessEqual(System.Object,System.Object,System.Boolean)">
      <summary>Represents the Visual Basic less-than or equal-to (&amp;lt;=) operator.</summary>
      <param name="Left">Required. Any expression.</param>
      <param name="Right">Required. Any expression.</param>
      <param name="TextCompare">Required. True to perform a case-insensitive string comparison; otherwise, False.</param>
      <returns>True if <paramref name="Left">Left</paramref> is less than or equal to <paramref name="Right">Right</paramref>; otherwise, False.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.CompareObjectNotEqual(System.Object,System.Object,System.Boolean)">
      <summary>Represents the Visual Basic not-equal (&amp;lt;&amp;gt;) operator.</summary>
      <param name="Left">Required. Any expression.</param>
      <param name="Right">Required. Any expression.</param>
      <param name="TextCompare">Required. True to perform a case-insensitive string comparison; otherwise, False.</param>
      <returns>True if <paramref name="Left">Left</paramref> is not equal to <paramref name="Right">Right</paramref>; otherwise, False.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.CompareString(System.String,System.String,System.Boolean)">
      <summary>Performs binary or text string comparison when given two strings.</summary>
      <param name="Left">Required. Any String expression.</param>
      <param name="Right">Required. Any String expression.</param>
      <param name="TextCompare">Required. True to perform a case-insensitive string comparison; otherwise, False.</param>
      <returns> Value  

  Condition  

  -1  

 
          <paramref name="Left">Left</paramref> is less than <paramref name="Right">Right</paramref>.  

  0  

 
          <paramref name="Left">Left</paramref> is equal to <paramref name="Right">Right</paramref>.  

  1  

 
          <paramref name="Left">Left</paramref> is greater than <paramref name="Right">Right</paramref>.  

 </returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.ConcatenateObject(System.Object,System.Object)">
      <summary>Represents the Visual Basic concatenation (&amp;amp;) operator.</summary>
      <param name="Left">Required. Any expression.</param>
      <param name="Right">Required. Any expression.</param>
      <returns>A string representing the concatenation of <paramref name="Left">Left</paramref> and <paramref name="Right">Right</paramref>.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.ConditionalCompareObjectEqual(System.Object,System.Object,System.Boolean)">
      <summary>Represents the overloaded Visual Basic equals (=) operator.</summary>
      <param name="Left">Required. Any expression.</param>
      <param name="Right">Required. Any expression.</param>
      <param name="TextCompare">Required. True to perform a case-insensitive string comparison; otherwise, False.</param>
      <returns>The result of the overloaded equals operator. False if operator overloading is not supported.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.ConditionalCompareObjectGreater(System.Object,System.Object,System.Boolean)">
      <summary>Represents the overloaded Visual Basic greater-than (&amp;gt;) operator.</summary>
      <param name="Left">Required. Any expression.</param>
      <param name="Right">Required. Any expression.</param>
      <param name="TextCompare">Required. True to perform a case-insensitive string comparison; otherwise, False.</param>
      <returns>The result of the overloaded greater-than operator. False if operator overloading is not supported.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.ConditionalCompareObjectGreaterEqual(System.Object,System.Object,System.Boolean)">
      <summary>Represents the overloaded Visual Basic greater-than or equal-to (&amp;gt;=) operator.</summary>
      <param name="Left">Required. Any expression.</param>
      <param name="Right">Required. Any expression.</param>
      <param name="TextCompare">Required. True to perform a case-insensitive string comparison; otherwise, False.</param>
      <returns>The result of the overloaded greater-than or equal-to operator. False if operator overloading is not supported.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.ConditionalCompareObjectLess(System.Object,System.Object,System.Boolean)">
      <summary>Represents the overloaded Visual Basic less-than (&amp;lt;) operator.</summary>
      <param name="Left">Required. Any expression.</param>
      <param name="Right">Required. Any expression.</param>
      <param name="TextCompare">Required. True to perform a case-insensitive string comparison; otherwise, False.</param>
      <returns>The result of the overloaded less-than operator. False if operator overloading is not supported.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.ConditionalCompareObjectLessEqual(System.Object,System.Object,System.Boolean)">
      <summary>Represents the overloaded Visual Basic less-than or equal-to (&amp;lt;=) operator.</summary>
      <param name="Left">Required. Any expression.</param>
      <param name="Right">Required. Any expression.</param>
      <param name="TextCompare">Required. True to perform a case-insensitive string comparison; otherwise, False.</param>
      <returns>The result of the overloaded less-than or equal-to operator. False if operator overloading is not supported.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.ConditionalCompareObjectNotEqual(System.Object,System.Object,System.Boolean)">
      <summary>Represents the overloaded Visual Basic not-equal (&amp;lt;&amp;gt;) operator.</summary>
      <param name="Left">Required. Any expression.</param>
      <param name="Right">Required. Any expression.</param>
      <param name="TextCompare">Required. True to perform a case-insensitive string comparison; otherwise, False.</param>
      <returns>The result of the overloaded not-equal operator. False if operator overloading is not supported.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.DivideObject(System.Object,System.Object)">
      <summary>Represents the Visual Basic division (/) operator.</summary>
      <param name="Left">Required. Any numeric expression.</param>
      <param name="Right">Required. Any numeric expression.</param>
      <returns>The full quotient of <paramref name="Left">Left</paramref> divided by <paramref name="Right">Right</paramref>, including any remainder.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.ExponentObject(System.Object,System.Object)">
      <summary>Represents the Visual Basic exponent (^) operator.</summary>
      <param name="Left">Required. Any numeric expression.</param>
      <param name="Right">Required. Any numeric expression.</param>
      <returns>The result of <paramref name="Left">Left</paramref> raised to the power of <paramref name="Right">Right</paramref>.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.FallbackInvokeUserDefinedOperator(System.Object,System.Object[])">
      <summary>Executes a late-bound evaluation of a user-defined operator. This helper method is not meant to be called directly from your code.</summary>
      <param name="vbOp">The user-defined operator.</param>
      <param name="Arguments">Argument values to pass to the user-defined operator.</param>
      <returns>The result returned from the user-defined operator.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.IntDivideObject(System.Object,System.Object)">
      <summary>Represents the Visual Basic integer division (\) operator.</summary>
      <param name="Left">Required. Any numeric expression.</param>
      <param name="Right">Required. Any numeric expression.</param>
      <returns>The integer quotient of <paramref name="Left">Left</paramref> divided by <paramref name="Right">Right</paramref>, which discards any remainder and retains only the integer portion.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.LeftShiftObject(System.Object,System.Object)">
      <summary>Represents the Visual Basic arithmetic left shift (&amp;lt;&amp;lt;) operator.</summary>
      <param name="Operand">Required. Integral numeric expression. The bit pattern to be shifted. The data type must be an integral type (SByte, Byte, Short, UShort, Integer, UInteger, Long, or ULong).</param>
      <param name="Amount">Required. Numeric expression. The number of bits to shift the bit pattern. The data type must be Integer or widen to Integer.</param>
      <returns>An integral numeric value. The result of shifting the bit pattern. The data type is the same as that of <paramref name="Operand">Operand</paramref>.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.LikeObject(System.Object,System.Object,Microsoft.VisualBasic.CompareMethod)">
      <summary>Represents the Visual Basic Like operator.</summary>
      <param name="Source">Required. Any expression.</param>
      <param name="Pattern">Required. Any string expression conforming to the pattern-matching conventions described in Like Operator.</param>
      <param name="CompareOption">Required. A <see cref="T:Microsoft.VisualBasic.CompareMethod"></see> value that specifies that the operation use either text or binary comparison.</param>
      <returns>True if the string representation of the value in <paramref name="Source">Source</paramref> satisfies the pattern that is contained in <paramref name="Pattern">Pattern</paramref>; otherwise, False. True if both <paramref name="Source">Source</paramref> and <paramref name="Pattern">Pattern</paramref> are Nothing.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.LikeString(System.String,System.String,Microsoft.VisualBasic.CompareMethod)">
      <summary>Represents the Visual Basic Like operator.</summary>
      <param name="Source">Required. Any String expression.</param>
      <param name="Pattern">Required. Any String expression conforming to the pattern-matching conventions described in Like Operator.</param>
      <param name="CompareOption">Required. A <see cref="T:Microsoft.VisualBasic.CompareMethod"></see> value that specifies that the operation use either text or binary comparison.</param>
      <returns>True if the value in <paramref name="Source">Source</paramref> satisfies the pattern that is contained in <paramref name="Pattern">Pattern</paramref>; otherwise, False. True if both <paramref name="Source">Source</paramref> and <paramref name="Pattern">Pattern</paramref> are empty.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.ModObject(System.Object,System.Object)">
      <summary>Represents the Visual Basic Mod operator.</summary>
      <param name="Left">Required. Any numeric expression.</param>
      <param name="Right">Required. Any numeric expression.</param>
      <returns>The remainder after <paramref name="Left">Left</paramref> is divided by <paramref name="Right">Right</paramref>.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.MultiplyObject(System.Object,System.Object)">
      <summary>Represents the Visual Basic multiply (*) operator.</summary>
      <param name="Left">Required. Any numeric expression.</param>
      <param name="Right">Required. Any numeric expression.</param>
      <returns>The product of <paramref name="Left">Left</paramref> and <paramref name="Right">Right</paramref>.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.NegateObject(System.Object)">
      <summary>Represents the Visual Basic unary minus (–) operator.</summary>
      <param name="Operand">Required. Any numeric expression.</param>
      <returns>The negative value of <paramref name="Operand">Operand</paramref>.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.NotObject(System.Object)">
      <summary>Represents the Visual Basic Not operator.</summary>
      <param name="Operand">Required. Any Boolean or numeric expression.</param>
      <returns>For Boolean operations, False if <paramref name="Operand">Operand</paramref> is True; otherwise, True. For bitwise operations, 1 if <paramref name="Operand">Operand</paramref> is 0; otherwise, 0.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.OrObject(System.Object,System.Object)">
      <summary>Represents the Visual Basic Or operator.</summary>
      <param name="Left">Required. Any Boolean or numeric expression.</param>
      <param name="Right">Required. Any Boolean or numeric expression.</param>
      <returns>For Boolean operations, False if both <paramref name="Left">Left</paramref> and <paramref name="Right">Right</paramref> evaluate to False; otherwise, True. For bitwise operations, 0 if both <paramref name="Left">Left</paramref> and <paramref name="Right">Right</paramref> evaluate to 0; otherwise, 1.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.PlusObject(System.Object)">
      <summary>Represents the Visual Basic unary plus (+) operator.</summary>
      <param name="Operand">Required. Any numeric expression.</param>
      <returns>The value of <paramref name="Operand">Operand</paramref>. (The sign of the <paramref name="Operand">Operand</paramref> is unchanged.)</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.RightShiftObject(System.Object,System.Object)">
      <summary>Represents the Visual Basic arithmetic right shift (&amp;gt;&amp;gt;) operator.</summary>
      <param name="Operand">Required. Integral numeric expression. The bit pattern to be shifted. The data type must be an integral type (SByte, Byte, Short, UShort, Integer, UInteger, Long, or ULong).</param>
      <param name="Amount">Required. Numeric expression. The number of bits to shift the bit pattern. The data type must be Integer or widen to Integer.</param>
      <returns>An integral numeric value. The result of shifting the bit pattern. The data type is the same as that of <paramref name="Operand">Operand</paramref>.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.SubtractObject(System.Object,System.Object)">
      <summary>Represents the Visual Basic subtraction (–) operator.</summary>
      <param name="Left">Required. Any numeric expression.</param>
      <param name="Right">Required. Any numeric expression.</param>
      <returns>The difference between <paramref name="Left">Left</paramref> and <paramref name="Right">Right</paramref>.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.XorObject(System.Object,System.Object)">
      <summary>Represents the Visual Basic Xor operator.</summary>
      <param name="Left">Required. Any Boolean or numeric expression.</param>
      <param name="Right">Required. Any Boolean or numeric expression.</param>
      <returns>A Boolean or numeric value. For a Boolean comparison, the return value is the logical exclusion (exclusive logical disjunction) of two Boolean values. For bitwise (numeric) operations, the return value is a numeric value that represents the bitwise exclusion (exclusive bitwise disjunction) of two numeric bit patterns. For more information, see Xor Operator.</returns>
    </member>
    <member name="T:Microsoft.VisualBasic.CompilerServices.OptionCompareAttribute">
      <summary>Specifies that the current Option Compare setting should be passed as the default value for an argument.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.OptionCompareAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.CompilerServices.OptionCompareAttribute"></see> class.</summary>
    </member>
    <member name="T:Microsoft.VisualBasic.CompilerServices.OptionTextAttribute">
      <summary>The Visual Basic compiler emits this helper class to indicate (for Visual Basic debugging) which comparison option, binary or text, is being used</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.OptionTextAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.CompilerServices.OptionTextAttribute"></see> class. This is a helper method.</summary>
    </member>
    <member name="T:Microsoft.VisualBasic.CompilerServices.ProjectData">
      <summary>Provides helpers for the Visual Basic Err object.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.ProjectData.ClearProjectError">
      <summary>Performs the work for the Clear method of the Err object. A helper method.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.ProjectData.CreateProjectError(System.Int32)">
      <summary>Performs the work for the Raise method of the Err object. A helper method.</summary>
      <param name="hr">An integer value that identifies the nature of the error. Visual Basic errors are in the range 0–65535; the range 0–512 is reserved for system errors; the range 513–65535 is available for user-defined errors.</param>
      <returns>An <see cref="System.Exception"></see> object.</returns>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.ProjectData.EndApp">
      <summary>Closes all files for the calling assembly and stops the process.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.ProjectData.SetProjectError(System.Exception)">
      <summary>The Visual Basic compiler uses this helper method to capture exceptions in the Err object.</summary>
      <param name="ex">The <see cref="T:System.Exception"></see> object to be caught.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.ProjectData.SetProjectError(System.Exception,System.Int32)">
      <summary>The Visual Basic compiler uses this helper method to capture exceptions in the Err object.</summary>
      <param name="ex">The <see cref="T:System.Exception"></see> object to be caught.</param>
      <param name="lErl">The line number of the exception.</param>
    </member>
    <member name="T:Microsoft.VisualBasic.HideModuleNameAttribute">
      <summary>The HideModuleNameAttribute attribute, when applied to a module, allows the module members to be accessed using only the qualification needed for the module.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.HideModuleNameAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.HideModuleNameAttribute"></see> attribute.</summary>
    </member>
  </members>
</doc>