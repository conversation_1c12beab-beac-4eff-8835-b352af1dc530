<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:tns="http://tempuri.org/" elementFormDefault="qualified" targetNamespace="http://tempuri.org/" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:import schemaLocation="http://localhost:60827/Wcf/GeneralService.svc?xsd=xsd2" namespace="http://schemas.microsoft.com/2003/10/Serialization/Arrays" />
  <xs:import schemaLocation="http://localhost:60827/Wcf/GeneralService.svc?xsd=xsd3" namespace="http://schemas.datacontract.org/2004/07/MetierRvMedical.Model" />
  <xs:element name="GetPhoneNumbersForAutoComplete">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="limit" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetPhoneNumbersForAutoCompleteResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" minOccurs="0" name="GetPhoneNumbersForAutoCompleteResult" nillable="true" type="q1:ArrayOfstring" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetListSoins">
    <xs:complexType>
      <xs:sequence />
    </xs:complexType>
  </xs:element>
  <xs:element name="GetListSoinsResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q2="http://schemas.datacontract.org/2004/07/MetierRvMedical.Model" minOccurs="0" name="GetListSoinsResult" nillable="true" type="q2:ArrayOfSoin" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="GetListeGroupesSanguins">
    <xs:complexType>
      <xs:sequence />
    </xs:complexType>
  </xs:element>
  <xs:element name="GetListeGroupesSanguinsResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q3="http://schemas.datacontract.org/2004/07/MetierRvMedical.Model" minOccurs="0" name="GetListeGroupesSanguinsResult" nillable="true" type="q3:ArrayOfGroupeSanguin" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
</xs:schema>