﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <metadata name="toolStrip1.TrayLocation" type="System.Drawing.Point, System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a">
    <value>17, 17</value>
  </metadata>
  <assembly alias="System.Drawing" name="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
  <data name="btnAjouter.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAAEnQAABJ0Ad5mH3gAAAZnSURBVFhHrZV/UJP3Hce9Xu/mfiB6iBAIhN8Bw48Wdtva
        u117Z73Wu/3R7o/tbL2yCbNIZQooImBBaPgR8oM01lpXB3u+DxFwWEaIASwGhFaMYAghP6kEsWPTYgFd
        XYtb370HJUe+iYht33eve557vp/n/fpcLrmsWfOYUY2HPq+0B+ep7CFKlVNwQjXOP65y8MrfcfC2K52h
        fHr+B8mxa5G/VjnCzjZ/9lucnxWjb16KXo45CXrnHtzflkN7Mwd/GU+aVo2HHSrFmifonsfOBxMRApUj
        Yqj7Vgl652rAXt+GOnsQZNb1kFk3UPjjxEQKOm7uRf+8Cuz1rVA5w/bSnatOnTMk6+/Tr+L8XDVUjkhI
        reuhsG1CnZ0HpSPEJwp7MOS2AEgtfjg9/Sp652uhtAuG612CtXT/ipFZg+q6b5WCXNsGicUfclsI6uz8
        VaOw8yGzBkJuC4Z+vhbv2VJnj11L2kB7fEZh4ZWd++IIjjoTILUEQmEL9Ql3JrEELCKz8rzOOeTWENSY
        fwrd50V4z5Y2q3TG/Ij2eaTOHrWl7UY2jjpEkI4FQ24N80mV2R+nXK9Adz0Hus/24ahNCKmF5zXHIePm
        R9dBN1MIpTXGQDs98oH9uS8bXC9BYg6CzML3SY05EK2TO0BHbPKDdMx7noN7Xm0OQMeNfMgtgizau5ha
        a+jbrdO7UGXaCOlY+EOpNAWg/9/VtB/ysShIzKFe80tIRkNxzJGGkxMv3KPdixEPvPSNYkwIyWgYas2C
        hyIe2Qj9dCXth8QciZpRvtf8cqpMQVBPbYfUEuX585SPR/yu4epvUDnCQ/Vo+IpUGDfivK8FTJGoMvG9
        5pfDnb9r/wXesaU6PRaoGRM0v+/cgkpjGKpNEStSfiUQPf/0XqBmJAqVI49+X2wMwfvOrVC4BOvdCyhs
        z/yr0hgJ8ZUIiK9w1/uUDQWh2LDOgwODT6Jr6jDtx+GhABQZfuYxW2LwR8Uw36Pz7eFwqGzPQmqPe9m9
        gHzs56gYEkB8JcpN6eVg6KYO4+7CLP6z8DnuLPH1Dfz3f3doP25/fQN3Fm66575cmMHsV9ehHP0VyofC
        3L0VwxGQm9NQY4ktuv/xDwj9qo1JKB+KRMVwtJuCwXW04zvF8oUWxYZAdy/nqTImomosWrG4QNVo+IYj
        hhiUGaLBXZfY//EPs8DoTDsOXdzk0V1miILYFK1cXID723zLEItSiryB9XTXd4pppg0HLwZ59ZcbY8Tu
        70DmoOBeyWAcllP4STgYawau3TbCNX/ZzdW5Qdy6O0V7MDFngGue4/7c5PwQnLMXcORSIoovRnt0c5Sb
        4jLdCxQPCgdKPhGC5uCAAPl9Qchbxh69HzQT5bQf+y+EIrcv0GM2/0Iwij6O8erlKDcJk9wLlFwSFhcO
        xGM15PWF4eyEhPajuH8zDvbHes37pJ234JZzKboczyu8kIDVkKsPh/ZqLe1HYb8IBX1xXvO+KOiLP+mx
        AJcDvQn6/b2b8Sj+3CNAx6dS2o+C3kTk64Ve874oHhBF0/41BRdFCfk9IjyKPd0R0H6qpP04cD4FuR/F
        e83T5H0kaqPd7uzrER3P7RFhJXLOxUFm+L2H/N7/F7CrMxT7fMx7kogDA0I/2uuRnHPJ43vPJWMldnfG
        oqj3OUgvbYds8DVkdwmR053oNedFd/KLtM8ru4bSfpKjTZ7J6UrBSmTrRNitS1jkzc4kr3OaPV1J2bTr
        oUnXC9Zm6USO3bpk7NalfC+yuI6ziTtox6ryhuZpZYZ2M3ZpU5ClTV01b2ifwp+0icjsSJra054WT/c+
        VqSNh+Jzz2zrfL0jFn/QJGKnJgUZmqeRqUlFpibtAanI0DyFnZpkpHckIKMt9WZZy84cumvFEEJSCSHp
        LMseIoTUsCx7spE01qtJU1tzQ+uHDX87qa9ksm+l1798L/PDX2JHewJ2/CMBr7XH4/W2ZLz57pZvDp/+
        411VY6W9iTlzRs00awg51c4S9q9c14NOrjudc3nIS0tLn2AYZish5C2WZY8TQloJIZ0Mw/QxDGNkWXaE
        YRqN6ga1vYk57WpqaJ1sZk67CENcTUyLq2XxWdPkqYamyUaiHieEmLh3OAgh/SzLdnGdDMOcYFm2lHNx
        To8lloc71Ov1T9bX168lhKxbgmGYTYQQ/kqo1eqglpYW/6V3mpubf8x1PUz4LfkDUrX6F3nyAAAAAElF
        TkSuQmCC
</value>
  </data>
  <data name="btnModifier.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAAEnQAABJ0Ad5mH3gAABAfSURBVGhD7Vl5VNRXlv7NTGamZyazb5lMTyfGLSCrpk3a
        SLRNOspS0SxurR21RRRQdgoXtFhUcImigii4hUVcIooCgigoiOz7vogUJeAaQWWH+vp8r6gSKgnaJjPz
        T99z7ql33na/+97d3q8k6U/0J/r/J8O5ir8ytnaxMLN28ze28Uo1snRpHjfNrcN02oIBMysXNdtGlvJm
        jok51i4WXKO/z/85Gdu4TDSVeUSYW3k8mee47/EG3/T+sPgaxGW3IrX0IbKq2wWzHZfVivCEGnj7ZfRz
        rrmN51Ou5R76+/6vk6mVs5mpTJ7+4ZeKTv9jaQOJ+fcQl/MAB5JbEBjbhA1Rt+BxrAEuh+sFs82+gDNK
        HEhqwbmcB+AaxbZz6mkLt3SYWcuvm8xyM9eX85PTtGmKn5naeO6fumBjp2J3ovpS8UMEJ9yG29F6OBys
        E2A9v76JdZEN2BB9CxuPa5ht9nGMczjX9Ui9WJtc9ACK3QnqqQs3dRrLPA9Qhr7cn4TMZa5jjGSutYsU
        kd0J+fewN/427PbXwi60Fk6HeMo3sT7qFnxONmLLN0pxE9vPapht9nGMcziXa7h2RXANgi6okJB3D9zb
        TOZx08TSbZy+/B9FZjZOv5w4U/5IsSNefSbrvjjBJXuqBQDnw/XwimjApphbAuTOcyrsib+N4MRmhCa1
        CGabfRzjHM7lGq7lHkuCqmB/oAanM+9C8VWCepLN2jbK1MfxUmRm7Wpq/Km8PeR8DQ4mt2DhV5X4XVCV
        OH2eovzrBihiGhFwpgm7zquw/2IzDqW04uvUO4i6dlcw2+zjGOdwLtdwrbiJkBos3l2B+TvKEHrxNkLi
        qkCZP1oJcyv3N3jye85WY/cFFT7bVo6Fu6qwPLgGq8PqhCnQxnmqBMbTPnKlFcfT74I3FZf7QDDb7OMY
        53Au13At9+Bey/dVY8HOcny6tQQ7zzZiT2wlJs7xemQ60/lNfVwvRJMm2f2lqY28bOPW82pev2xrGebu
        qBCmszK0VjghHdPvlBI7zqmEmRBkUcMT3G3rQXfvAEj9A2p09gzgzqMeMXbi+j0xl2u4lntwr5X7a/Bl
        UCW+2FYKG78iBJ1XQrE1Tm0ic698qZxhIvPcvNj7dGdE2h1Yby7DnADN6f9+8PTdB09/6zdKET4rmjow
        oME8IqnVQH1rFw5fbhVruQf34p7L9lWJW5i9pQSWvoU4dqUZxGAq8wzQxzcimchWj3p3rndb1NVWfLat
        Qijw+fYKLA6qwor9NTrb9znRiOj0u+jofgHketTbr0Z8/kOxh9YXVoRUY9GucnwWWAor3yLM2VKEqLTb
        ePdTefvEWWtG6+P8QTK28Yz38o1TM1J8pCiBzZZn5sOowVi+NrIBMRn3hIm8LPE2Lpd8K/binnZDzMja
        rwgzvPPgebQGXr7n1KY28ov6OL+XzKydxr7/qV877XnGxhJ87FOKT7aWY9WBWizdWy1+abP7k5pHBP+0
        qx+tj3qEPzzu7Bdgf4h4ENxzVWgNluypxLztZbDxL8ZHGwswfV0uoq+2YOoCxWPjmc7j9fF+h5gNF+5L
        G5BHNOCDDcX42LcUswPKUdvSKaLJ6vA6EcMfPe3TxyF8oLq5Q0Se0zfuITbrvmC2uTav7jG6er5rbj19
        avifUg5TQLa5GB9tKsBUrxy4H6mGmyJGbSLzCNfHO4ymTVO8Ymjj1BaR2iyAT/MuxsxBBXJqHwthd9t6
        Udr4VB8DunoHBHCGyoird0REisnQMNvs41jk1bsiIulT4c0nOgWYD6jAbzYVwGJtDj7cmIeIK00gNkZH
        fdw6MrNaM8XGdl87C67J8sJhCpzPe6AvU0d9/WqRsEIuNiPsUouIMMdS74gERmabfRzjnNCkZnz7ZPgN
        0sTWRzUIH9DegFaBd9yyEJKghMw2uJ2luD5uHZlZuW9arUjuZ3LRKsCboA+Ep7QOEziULpc+GlZCMKwS
        LNeQ2Wbf0JIi+tpd/W0Ql3MfvwuqxFytDwxRwO1QFRwVSf3mNq5++rh1ZGIjTwmILhHlAhUQPuBTKqLQ
        5tNKfXk6utfWK26AwHZfuC0KPYIdyuzjGOewlGASYw6gP9GBWV8xGzOMfh6oiUIfeecLH6ACcwOLEBBd
        AD6K9HHryETmpgy7eAsfKkrwS89CWKwv1oTRzWVwCKvVxz2MaAL0jeCEZnwVpxLlAgGT2WYfK1MteBZ0
        zMJMYmsO1WmKur2VIpF9FsA8UCjC6PvyHExyuYHp67MRnlgHU5mrSh+3jkyt3J+cSG/FO54FmORRgPfX
        FWHGphJY+msS2cAPhE06cPGtpziVeV8HkiX0tkFmm32sf3xPNoqTp73TVFmR8vQDzyiRVHBf2P8nm4sx
        06cQ0zfk4Vee2TB3zsREpwycuNoEPk/1cevoDZslfbFZ92DklIeJ7gX4lVcRpm/UOLJsa7mIQFpq7+jH
        9ao2UZx5RTaIZOR9/JaoMln385SHMvs4xjmcS/BMXjSpSlUH1Go1+vr78duvymHtr3HgD9bl4F33bJg5
        ZeLtlVdx9kYLDCe59uvj1hEVOJt9HwZrcmHmmq/zA5oRywlmzfi8hwLQ8mBNVmYNw1OkKbAkoFnw0UKg
        2hcZ2+zjGOdw7s44FcqbnoXjgYEB9Pf3Y2N0vSgjaD5a+zdZnYFxdqmIzWweWQGaUEx6K0xd82Hskq8z
        o19vLBG3QGf+fFuFcPIv91SLwo5KOIbViVqGzkhwfDoSKB2UzDb7OLb9rEr4ytDMzNPXKhCZeluc/rT1
        z8xngmM6JtinISZNObIJ0YkPJjYI0BOc8mDuVoD3vJ7dgpV/mcgJX2zXKMGHzbJ91aLAY4nB26BD8kao
        zFBmH+2/5dvhSWwo+L6+PhTUP9Kd/mT3LJg6ZcJg1VW865qOg/E1Izsxw+iWqGIBcvzqXJi45OMdjwJM
        WafxBYZUmtJQJVihskbiI4evNL4V7A/WCsfkzZDZZh/HyPQb5f1unQJa8L29vWh/2oUPvfPwnmcOJjpn
        wsgxHWNXXIGV4ga2RuaPHEbNrNwUTBZrwuswxj5Hdwv0hanrNabEkkCf/E81itugIpobqcWqUCqhYbbZ
        xzHO4VyWF/qn39PTg+7ubtjuKxO2rz39Ub9PgX1wERwUiSMnMm0pseeCCm+tysY4x1wYOeeLiERTYl5Y
        tLvqO5Vly8MeLA+pxpd79cxpkNlmH8c4h1Gop29gGPjunl6dAkHnborIY+iQjjG2V/DG0mTsiq2FzHbv
        yKWEtpg7ckmFSe55GGOfDYM1ecKU6NAMq1QirbRtuAYA6lq7hK3bH9QAHuoHbLOPY8wTjzv7hoGn6RxO
        UcEhtALXy+/hcuEdGDlmYPxKzembOV7B4Yv1zy/mSCyn5+5OHWBl+NbKLIy1z4bhmlwRmbRKzN1RKZKX
        PrHuZ+m8LkoTKhl5tNHHJ6YRKcUP0dvb/x3wyjtPhOO+654Dc5cb+MS/AG+vvIbRyzWnbxtUgDWKiOeX
        0yR+VDKfr3h8+HILxjtm65SY4EQl8oQSNCfFiUZ9/Dpixr7b3ovq2x3irdz8sBv9/c9MZij4Jx1dWBJU
        hsnu2QK88ZrrMLDXgr+EMcuTcehiA8xf9EFDMpHJE9wUZ9S2wVUYZXdDKDHOIRuGq3Ng4sIsnY+p6wtx
        5HLrd/xBS3RQLX8fcHJHRzfWHavBFHkOzJxvwGj1dby96hrGrEgV4N9YkoRlX+XBTXHyxZ+UJD7qJ9t4
        t4UlN+Ed91yhBJk3YSCUyMXUdYWY5VeM4AQVunr4ZHwGdigPBa4FT2dtffgU676uwUxFnlDAaM11jF91
        DaNtU/HmshQB3twxBWEJ9eIj1x/1qCfxs8r8teGd+xKaMGZVJkbZaXj0yhswds6BxfoCWPmVYMGOcngd
        q0N+bTt6+/p0gPVBa4F3dnbjctFdOO4vx7xtxZilyMdUeRaMVqfjreVXdOBHL0tE0NkazPcK7zSxkQfq
        43suaT9syX1i1d5R9Xhr5XWMsiNnwsgpG1PX5sPan2+HMqwIqYLL4RrsiL2Fy8X3Ud/8BA/bu9DZ1SP4
        QVsnqlRtSMy7A/8TdXAKq8DyvaWYt60Qlj75mOKRCUP7qzrwby5JxIZjpZArTrz8hy0SPy2az/R8tCW6
        FJ5Ha/GWXQZG22XAzDkbFmvzYOVXiAU7SrEiuAJuh2uwKboOgd80YM+FRoRebEJ4sgrhSU0ITVQiKK4B
        Aafq4R1ZDdfwSizfW4J5gYWYtSkLU9wzYOyQilFLCT4BbgeLEBBVgIlz5C//aVFL4uPuHHn79pNlUByv
        h4FjJsxdskSxZeNXiIU7S2AXUg73w1XwOV6HnbENCE1sxNGUJkSmqgQfuaTE/vgG7DhTD0VUNVzDymC7
        pwjzA3NhufEGprpfg6lDCsbZJsH76zJs48trzk/wcVeSpL+QJOmvDWYstWAS2eB3Rn3gogqWvgX49YZc
        yPwKsHhnCexDyiE/WiXMY/e5BoRdbER0WhO+ybgtOCpVCb70dsXWwSeqEu5hJVgRlI8FW7Nh6Z0BC/cr
        +HjdVYTG12GD7ym18WzPdsqk7EEMfzS9IkkS/yn5O0mS/lGSpH99Y+Ks90wtXRqWOgT3nExvhm9MHb4I
        KMSiHcWwDymF55FK+MfUYE/cTRxJbsTpdBUSc1uQkNOMU1eVCE+sx64z1VBElMH1QCFsd+Vg3uZMzPa5
        hk0RpTiRdgtLHIJ7TGa5KX9hZjWFMgdlEwOxENMLEVP130iS9PeSJP2zJEn/LknSf0mS9ItXX33NYMJv
        HKJNZzt3KbbHqRPy7iDoXAM8DlXA41A5fKOrRM0SnngTMWm3EJ/VhAtZSkRfqUfo+RpsP1kO76NFcA7J
        hXNIHnaersD5LBUUgbHqyfO9uw0/to+hDMoalEnZxEAsxDRyGSFJ0p8PXpv25P9NkqTXJEn6H0mSRkmS
        xL9+DF83+GCekaVz4eRFim5F4Bn1pfxWcdrH05Q4knxT1C1Hk2oRlVKLyEs1OJxYjbD4KoQnVCPyUr1Q
        LDlPBUXAaTX3MJ7lXPy6wfT53HtQBmVRJmUTg/YmiI0YRyRq+beSJP3D4DX+pyRJPx/cdOygEBNG2tfe
        fn+JwQy7JFOZc9cij4OdioBT6pOptUgvaUFJ/QNUKx+hSvktiusf4FpxM05croZi60n1bz0PdE6UuXdz
        7evjpyzlXoN7cm/KoCzKpGxiIBZieu4NaIn2pjWjf5Ek6T8kSfpvPpslSWJGfFuSJCMGKUmS3nnlZ69a
        vG443XG8xaKICR+vLjK2dH1gau3WzT+5yWybWLo94NhYi8WRPzec7sg1XDu4B/fintybMiiLMilbaz4v
        7ANDiRGADvTqoC1qTYqno1WGV067JQjTQUD885qnSoBkttnHMc7hXK7hWi1o7qk1GcqiTMp+qSj0fUTb
        05oXT+WfBk+IjsarptPx5Gi7dEKCGsrs4xjncC7XcC334F7cU2smz7Xzn5L+bPCUKJgpn47Gk+O1ExCZ
        bfZxjHM4l2u49kfRHwBF0ZDwIXIwZgAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="toolStripRechercher.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAADAAAAAwCAYAAABXAvmHAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAAEnQAABJ0Ad5mH3gAAA7GSURBVGhD7Vl7VJvneX8cYqckTbbEbu1tduyzZqfdmWPH
        a5Y07eqlm9sm2+K2cZ34ss12Ese5NMde6oJtbOMYY2MMCJAAIy4CITAIxM0CYXERuktIQkIIEAiQ9X1C
        AhPuAqTv/dR35yVyj4/b7SwxdP5jv3Oeo/PpSPB7n/f3XAXw//iDWAUAGwBge3R09J7Nmzcl7HhhG3/H
        jhdEO3ZsE23bupW/fv36hDVRUXsB4LsA8OeR7/yfIwoAngOAHx88uE9UWS4eqZTUYmHFTSyWtmJxfQuu
        rG/FVdJWXCquxeIKMS4oyPP84JXvCQDg5wDwNwCw+v4/+sfCNwFg5949b3JksqbJ2qZ2fIGTjw+eiQ++
        c5yzuP/TlPl/P50dOHSSP3foZN7ch5+mzL8bn7PA4ZfiWy3tuKioaHT79uczAeBfAWATADxy/z9YSWx5
        +umnXysXCQxNze340KEUtC82dfbdOO5sXLpkmldjmijXUWMNXdMjt3pnqDr7DF2mG/Fxax13TvHaJk5c
        uzGZwOGFdDo9Pht7Ug8ABwDg+ciNrji27N69e6/dbh+/zOHjX3wQP3rkfNokp1QxJu0co/QexqMdZSi9
        j/EY/MxtLY2GjT7ksowiZ4cf9ep9qK9SP+25JLL6L6ZXj9VI5Tgvj+99BOBXALBjpSX1zaioqJ84ex3z
        v45Lwj/cd8r5cWKet840Qpu9aMg0itxqGrk1FBomphxGgyo3cqndqF9NoV4dzTo6fGyX2c9adRTrEGrG
        qCtFyts1ja34ekY6BQBHVzIuyPXu7NCp2s5dSsH/sP90797TQrPSOeuyjSOXwYtchCwxpRsNKAZQf7sb
        OVUu1KceQj0qN9utcbM2NcV26mjWZBlhjRqKtVQZp4fTy4wupc6E9+/9hQIA3onE17LL6bkrVxLPVNfV
        49cPnXS9fiJXx1WMttsn0IDBh/pUFOojZIkpB1GvYgj1tA+xDrWbtbcT4kNsp2aYNatvs0btbdbQ4UF6
        M4U0ahdrLFPdcRXVmoe6u2x43bp1OQDwUwB4ajkDm+TsXWq1evrQiZNzrx652HGyzCWpd7HK7lHk1NNs
        N/EwIRuxLuUga1UOsZ2KIdaiHmItKjfboRlk9WqK1WopVq2jkMroDSnM3pBC0c1a8qXDjjZj10xCXJwH
        AI6TWAOAx+4n8lWx4dixYzGFhUV418FPHB9ktsrStExJ+yBrsHhRt87D2ojpPaxV62GtSg9r1VOsRUuz
        Zj3Ndhho1qjzsHotzap1NKsyeFiFzhNqM1OhZgsduqUfDrXUKqe6S5q6Om3WTvz16OhiAPgnAHhmuW5h
        W1mpUHPiVHzwp0cTNGmtUyWZGkZo8aPObi/qtvjYTgPNmomXlYOsvtXFqhr7w203nWG51BluauxnG2X9
        4UbFYLhJ42blWoqVm2lWbqNCzd2+ULPdH5KpbKwur8ZpGXC75/bte9sFAP+xbLfw2GOP7aqrrcYHP4wZ
        fPdyVWNuRzg/UzVb1NQfaqmwhWqztIuiq4pgfnxziBfTgNJiGlHKbxrQtZgGlBTTiBJjGlDCqQaUcEaG
        Prt4K/TZlZbQxQzV4pV83XxKhW0hS+GaF6mdoaaKJl+n2uZy83i8EABcII4DgD+5n8+Xxisvv3y8qrIS
        //JYrOWcyFJRYEL8TPV03tXWGd7F5lnOpeaF9DRF8Pp1AxIUmRjRDStTJnYwFTW9YbHUGRbf7A1XNPQx
        NxqdTOnNPkZY40AF4q5gjsi6kC40zSWXWGYuiwzTqQWVn0tFNzv0Bo0aAwAfAP4ukpEeTEY/eOWVxOoq
        Cf7Zh/GajGZfYZ5+ISdTOZNd3b0gbneHmvRUqMVEsS2dI2yryce2WOiw3ESHZQZPuEFPhW8a6HCdng7X
        mGi22uRlJWYvW9XlZSSdXkZi94aqbCOLFRYqUCFtmm/hlqpUvQ47XrVqVRXxHQBsfOC68I21a1OlUil+
        8+Mr7cWWQHaRboaXrZ7J1AyFGrtHkbLDG2oyedhGHcVKtRRbpxliqzXDYYnaHa5UucMV6qHwDc0wU6bx
        MKVaDyPSeZgSHcUIzRQj7PQES2zeRZGVDtxQq1llqqDdMNjvxKtXr5YS3wHAXz5wHKx75plkWaMM7/91
        cku5LcAVmacyszWT1xocgRKbLyQ3UUy9jmZqDR5WovOwVRqKFWsppvwuYQ3FCDUUU6ylkEB3GxUabqNC
        E00sWGihgwLbyLzA5gmUaNrDLSnF7RqXsw+vXrWqEQD+PtLtfu1+Tl8Kf/rUU2dlDY34aDxPXmqc4FVY
        pjlF+slkiXUm2zYSarDSTK3Zw1SZKKbCQDM39B6mlHhZSzFFd0nrPSjf4EF5JgrlmjyIb6KCuRZPkG+l
        g3lW33y+1T0rbJPPy/MlOpXDbiMxUBM5wF898AEeeeSRwzWVlTg2md9eqhnhSTqnU8ssk8lllunkTu+i
        pNMbkpjoxXILzZSaKEbYQTFFBvp3pPk6CuXqKZRjoFG2iUZZFjqYZaWD2VYqmNNFBXO6/XO5nb3zJc3S
        yQapqqujtVlODiCKSOhbD3wAAPhRyrVrn6dmF/TnyqyZ9d2zKRLb5NVy89QljWu+oMcfkizp2MsUE2l0
        0F942kyh6x1fkOaZacQ10yjT4kUZVm8o0+pdyLDRQZ6dDmQ5RmeyLJZAheSmq8o+SA1/dvz4IgBwAOD7
        kVqw5n5CXxbbfv7GG4WiMjE+lyUuUAyFkhpsk8n19qnEhu6ZJLtvsczhDZbYfMFCqw/l27wo1zqCcmwj
        iGf1oUyrD2VYfSjd5kUcmy/EsftC6d2+hYwe3wK3ZzTAdVAzOVYtW5VfrS3v6R9kXti61QEAJwHge5Hx
        89H7CX1ZbHjqiSf2tTY14bTCqvablpHk5p7JpGbHRGKjfTLeMLyQ7RxbLO3xBQvtfsR3+FGOfQTx7D6U
        vmR+xLH7UZrdH0rr8S+m9fgDqb2+hXTnaCDTOT6b2WWbL2mVj92Q6ez6xpu1+NFHH5UAwNsA8AIArH3g
        OnC3mTt25Ii24VYbzhQ388ye+YT2vonLbX1TF5V9Mxf6RufzXeNBgdMfvN7rD2Y7RxHXOYoy+vyI0+dH
        aX3+UOoXtpjW719Ic/kC6a7xAKd3aCHHrmPKRPXGMjc1gv/ltdecAJB1TwA/sVwLgOfWrVv3erlQiIV1
        LdYaVddlm2cmQeeaSdAMTJ43u2cSBsZRnns8yB+8g7IG7yDu0ATKGJpA6UPjiDM4EUobHF9csqGxBc7w
        xGzaAL3A7TUwoqaW/ixDj6svN4uLo6OjbwDARwDwIgD82QMXsXuwNNC89OKLFzVKNc4pv9mqsA6e7fHP
        X7C6Zy5ahqfPOkbmEqjPgzneSZRLT6Es7xTK9E6hdO8E4lBLFuJQkwtp1GQgddCzwHPZUKHGSKXLDXaj
        RqXGGzduJMXrGgD8MOL9J5dDPveC9CU/OrB3r7a5SY6zRPXyOlUnIX3W6QvE9/hmz7rGFi8Q0ndmUPad
        QJg7HghnjM0ijn8apfmnQyn+iYW04dvBLLofZTdrerhyo91g1Bvw9196yQgAZJgh2icD/jeW0/v3YsuR
        I0c8ooIC3NQow8JqmamgVpml76XOTS7gU95pNs47ycSNzaDE2VA4dTYUTp8OhDkTAcSZmAhzJsfCqaZO
        L0fSbhGY+weHdVot3vb88zoAyAWA9yKD/V8AQPRye38JfH7JRmFxCZbJZbhOIsGFfD5ubm5npe0d5sI6
        hUim60nqHfadG5+ajQkyzG8Yho39fHr2nPO2/5Lc4MyokBvFaseA3Wix4quJiXPPPvtsPQBcB4BjEfLP
        AsDXV2ImBkW9Yl3+9TJXl60XtynacFxcHI47E4ffeuMNurSoGKvVOixT66dkSrO1qsXUXi43yonVKi0K
        hdHRpdc7powdFlwiKMTf3b7dHB0dXQQA6ZG90NaI5wn5B877vwdCPimJP9Np7sFarRmfORNHDhDYvHmz
        ipT9NWvW5H7n299ufGvPnoH4+HimtLh46YZqJRIsKi7G8efPL/xs9+6Bb23Z0rpmzZqySKr8z0i1JQ0b
        ia/HV8TzdWV163m8Eo9SYcK33TSOjT2D8/n5eMOGDXUA8Gkk8H4MAPsjFZQHACUAUB1pyiojzxkAcB4A
        Dt9DnKRKsoEgLfPya56Am5pNyxqUODAXwleuXMXCYiE+cOBAf2TsIysQMvqR7TS5fhJ8pHqSHobIgmyj
        yWRF9E2eyfukPSCfIWmSNGrE68tSrH4PWRn5iurKWzi0GMZ8fiG+lJCAhcKSxaioqAIA+DcAeCkyNd3N
        GoQIeSWkSBNGCBJpkIpKXskzeZ/o/O7nVwa8DH5p/vUKvDiPsEqpxR9/9BFubWnFmzZtIsMG0e/OSMH5
        n5ZQhOAfspXFdW4+N4cnwtMTAdzXP4DfO3oUt7Up8Pvvv09WHp8BwD9HpLNiBecrI5eX/04GR4Ddg17M
        Mr/FMTGxmM/PxwKBYC4qahUpOCTt3Sudlffo/xZ5WXlvp6bwcX+PGxNcvpyEExMTcZetCz/55JNiAPg4
        0quQDEKks/xp76tCkCPYEh8f/1utyrxEvvyGGH9w7ANstVrxq6++Ssr9mUi6JCtwkkUeHukIBIKvZSZn
        jcqkyiXyRqMJHz58BKtUanz69Bk3ACQBwJsA8LeR3E2yycMjnax0gam8TLpE3uv14SOHD2OxuBLX1NQs
        rlq1pPtDkRFvcyQl/ndZ54+PAn5ZjYAvxiwTxizD4k8++QSnpqRih92BN2xYXxvR/d2USXaVD4/uK0rr
        fpXNFeGpO9ORoL2CT5w4gb3eEbxr1y6i+7MA8JNIFV0X0f3DI53kqxmuEc/4EnlpfQN+7933sNVqw7Gx
        sYMR3f/yodU9QUlJSZCQt9pseM+ePbhF3owlEkkg0qMfAYCXH0rd38W+t/bFmzvMmMvl4gapFNfX1zOP
        P/44aXeJ7u9uBojul79HXy7s3Lkzubi4eOTatWuetWvXkrV2DJl9AeCv78n3D5d07gORxncA4K2IbP4x
        Qp4MGaRPf6jJ3wU5BGl3icfXR35gI0H7UOn+vwC/94cxW1yI8wAAAABJRU5ErkJggg==
</value>
  </data>
  <data name="btn_close.Image" type="System.Drawing.Bitmap, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
    <value>
        iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8
        YQUAAAAJcEhZcwAAEnQAABJ0Ad5mH3gAAAeSSURBVFhHrZV9bBPnHcfPwXHsu/Od784+2zFREtE2IMQf
        HfzRPzqEisakvpFKVSUk1KrSoNUquirtPwzmc14htIGYl7ySmNydnTivzgspLxXR1EpTt7VsmjS1IAra
        BmtZGQLtRQOp3+n3xPbsYzCQ9pV+Unz3PN/v5/d7Hjsc94iaCYc3zUUiTfORSPLDmpqBkytX9i1Goy0L
        0ei2xVhspXP9/0Uz0ej3Z6LRxV899xyu7dmDbxIJXE8k8I1h4LphLH9uacGFHTvw0bp112aj0d0Gx1U4
        fR5Zk3V1tZOx2K8vvfMOrsbj+MXmzciFQsjJMuYUBScVBadUFYuqijlJwrm1a3Fx5078pb0dn2zciMlo
        9CdOz4fWiKa9+enWrfjT3r2YrqnBqChiStMwq+s4GY3iw+pqnInF8FEshrOxGE5VV2NB1zGjqsgKAj5/
        6SVcMwyMx2KfLdXWep3+D9SQJHVfamrCuU2bkPJ6MRYMYiocxrSuYy4SwUI0isVIhIWejkZxurqaARHY
        bDiMXCSCrKJgTFVxLR7HXEPDzYV16xRnzn/VoKIkLr33HrL19Uj5/RgNhTCm65gMhRjEbCSC+XCYhREE
        C45E2Od5AohEMKPrmAiFkA0GkaqqwoVdu5BraLi5+NhjVc68MqV0ffNnr76K0bo6DEsSzGAQmVAIo8Eg
        JnSdAZA5hVCnNIlCERQ9zxFsOMzWE0A6FMKgx4Mv33oLdnX1L52ZZUo/8cTfF59+Gn1+P05oGkZUFWlN
        YwC21wurspLdA4KgMbPAfNHfk5KEdGUlspKEcV1n8LTf1DQM8Dx+t2MHjuv6m85cph5Zbvt02zYc8Xpx
        XNMwlAewNA09HIePUylcPH8eg3V1LGg6HF6eSL7GVqzA9Btv4MpXX2H0xRdhezwMwA4GYaoqhgIBTDQ0
        IFVbe9eZzdT11FPfDcVi6A0EMKgoDCClquhxuXD+7FkU9E8A/TU1mJAkTNK4dR3pigrMNDUV15DsxkaM
        CAILp2kOaxp6eR5Lzz+P/mCw/OvZqyivLGzciKQgoCcQQJ+q4rii4LggYOr118uMSQWIrN8Py+W6J7yg
        pNuNE9S9pmFQ09AnyxhbvRojq1ZdKAM4JMvZ8Q0b0C2K6FEU9AUCGFBVDEoSBtavd/oyEQStnXn7becr
        pj9evYpelwvDBQBFQa+qsianNmxASpYDRYATjz/+525ZRlKWcVSWlyFUFQOKgn6PB+bWrU5/pr/dvet8
        xHTj1i0kRRHDdJz58H5VZcd7xO9HZu1a9GtaYxFgoL4eH/j9OCRJSNKiUgjauGLFfSGcYuGCgOOSxC5z
        f0n4MUVBUpIwVF9PR/1TFt6paf7DkQgOiCIO0RQkCUdKIQIBZnDsISC+vXED3YLAjo66ZvAl4eTb7ffj
        WCSCw4HAIQawT5aVfX4/3pckdNEU8hA0CTqOo3R2gQAz6uY4nOvrc+YW1b1qFXo9HjY5Wk8XmpqgZiic
        fA/KMrokCd2SlGQA9G+zQxDQWQLRJcu0gCiL06Dw6XffdWaW6V8AjsRiOOrzsWDq+rAsMx8Kp+bI/8By
        TnvxDrSL4t19BQhRZCAH/X5GSyBdHIeZ/xFe0B0Ah2MxJL1eNkUKpgtOTVE4ee8XRXwgij8qArTy/Cft
        oogCRCctkCR2MQ9Q5/f5nl//+mvnIyaaRDIWw0GeZ12TD/lRc+RPE+8ShHVFgA6e39PC8yCIdkHAfr+f
        Ue6nn+VnnnH6M/319m10ulwwX3jB+Yrp1p076OQ4vJ/vmi453TVq0hDFO8VwUhvPRxOCgFaeR6sooiM/
        jbbKStgvv+z0xs3bt3HA50MXz7MJWY2NziWgX4gOjkNnfqod+c6pyVafb6gMgNTs8y0RRDPPo3QaLRyH
        pd7eojGF7/f50On1Lo+VzpTjYDogetevR4fHw4LJh/zaeJ75t0rSKmc+t18U1zQLAgiiAEIb2uhvjsPC
        7t34fGoK+2hCVVXLdyVfdFztHIfhLVvw+zNn0PPkk2itqPhPMHXN88zXEIScM7uoZkHoIwgnCE0k4XIh
        wXE0PnZEVCygpFrdbramxeNhR0n7aH8iH06+9MPnzC1TM89fLECUgpAJG18eiIo6K1ThmTO0EMyK53/o
        zLtHBsfxhsfzbSnEPTAlUMVyvHPubRbFHzuz7iuD47wJr/fLhM93r9GjFIH5fDC83u3OjIdSgueTcZcL
        hsdzr/kDioW63Yi73X8wNG210/eRdHjnztUda9ac2stxiFdUMBg2GTrr0sCqquVQjqM11w8+++wup9cD
        ZVnW9yzLes227d2WZXXatj1kZTKpdDabG83lZkaGhpaObt9+o7O29m6LJIGAflYotxtGbe13xpYt/+iP
        x79Iz85Opycn563R0TnbtoeZ17Ineb9GWWXhhmFUmKb5A8uy4rZt91mWNWVZ1inTNH9umuZ527Z/Y6bT
        59OZzBfWxMTl0VzuijU7e9kaG7tMn635+cuZXO5Kemzsip3JXLQs67e0h8qyrI9t2z5NnqZpDti2bVAW
        ZZZBlIpeLi0tuVOplNeyLKlQpmnqlmWtfFBlMpnw+Pi4XNiTzWZ95HW/wH8DqE2pmtncaXcAAAAASUVO
        RK5CYII=
</value>
  </data>
  <metadata name="$this.TrayHeight" type="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
    <value>25</value>
  </metadata>
</root>