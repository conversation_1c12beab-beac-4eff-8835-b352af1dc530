<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.Extensions.Logging</name>
    </assembly>
    <members>
        <member name="T:Microsoft.Extensions.Logging.ActivityTrackingOptions">
            <summary>
            Flags to indicate which trace context parts should be included with the logging scopes.
            </summary>
        </member>
        <member name="F:Microsoft.Extensions.Logging.ActivityTrackingOptions.None">
            <summary>
            None of the trace context part will be included in the logging.
            </summary>
        </member>
        <member name="F:Microsoft.Extensions.Logging.ActivityTrackingOptions.SpanId">
            <summary>
            Span ID will be included in the logging.
            </summary>
        </member>
        <member name="F:Microsoft.Extensions.Logging.ActivityTrackingOptions.TraceId">
            <summary>
            Trace ID will be included in the logging.
            </summary>
        </member>
        <member name="F:Microsoft.Extensions.Logging.ActivityTrackingOptions.ParentId">
            <summary>
            Parent ID will be included in the logging.
            </summary>
        </member>
        <member name="F:Microsoft.Extensions.Logging.ActivityTrackingOptions.TraceState">
            <summary>
            Trace State will be included in the logging.
            </summary>
        </member>
        <member name="F:Microsoft.Extensions.Logging.ActivityTrackingOptions.TraceFlags">
            <summary>
            Trace flags will be included in the logging.
            </summary>
        </member>
        <member name="F:Microsoft.Extensions.Logging.ActivityTrackingOptions.Tags">
            <summary>
            Tags will be included in the logging.
            </summary>
        </member>
        <member name="F:Microsoft.Extensions.Logging.ActivityTrackingOptions.Baggage">
            <summary>
            Items of baggage will be included in the logging.
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.Logging.FilterLoggingBuilderExtensions">
            <summary>
            Extension methods for setting up logging services in an <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Logging.FilterLoggingBuilderExtensions.AddFilter(Microsoft.Extensions.Logging.ILoggingBuilder,System.Func{System.String,System.String,Microsoft.Extensions.Logging.LogLevel,System.Boolean})">
            <summary>
            Adds a log filter to the factory.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> to add the filter to.</param>
            <param name="filter">The filter to be added.</param>
            <returns>The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> so that additional calls can be chained.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Logging.FilterLoggingBuilderExtensions.AddFilter(Microsoft.Extensions.Logging.ILoggingBuilder,System.Func{System.String,Microsoft.Extensions.Logging.LogLevel,System.Boolean})">
            <summary>
            Adds a log filter to the factory.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> to add the filter to.</param>
            <param name="categoryLevelFilter">The filter to be added.</param>
            <returns>The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> so that additional calls can be chained.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Logging.FilterLoggingBuilderExtensions.AddFilter``1(Microsoft.Extensions.Logging.ILoggingBuilder,System.Func{System.String,Microsoft.Extensions.Logging.LogLevel,System.Boolean})">
            <summary>
            Adds a log filter for the given <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider"/>.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> to add the filter to.</param>
            <param name="categoryLevelFilter">The filter to be added.</param>
            <typeparam name="T">The <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider"/> which this filter will be added for.</typeparam>
            <returns>The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> so that additional calls can be chained.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Logging.FilterLoggingBuilderExtensions.AddFilter(Microsoft.Extensions.Logging.ILoggingBuilder,System.Func{Microsoft.Extensions.Logging.LogLevel,System.Boolean})">
            <summary>
            Adds a log filter to the factory.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> to add the filter to.</param>
            <param name="levelFilter">The filter to be added.</param>
            <returns>The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> so that additional calls can be chained.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Logging.FilterLoggingBuilderExtensions.AddFilter``1(Microsoft.Extensions.Logging.ILoggingBuilder,System.Func{Microsoft.Extensions.Logging.LogLevel,System.Boolean})">
            <summary>
            Adds a log filter for the given <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider"/>.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> to add the filter to.</param>
            <param name="levelFilter">The filter to be added.</param>
            <typeparam name="T">The <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider"/> which this filter will be added for.</typeparam>
            <returns>The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> so that additional calls can be chained.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Logging.FilterLoggingBuilderExtensions.AddFilter(Microsoft.Extensions.Logging.ILoggingBuilder,System.String,Microsoft.Extensions.Logging.LogLevel)">
            <summary>
            Adds a log filter to the factory.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> to add the filter to.</param>
            <param name="category">The category to filter.</param>
            <param name="level">The level to filter.</param>
            <returns>The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> so that additional calls can be chained.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Logging.FilterLoggingBuilderExtensions.AddFilter``1(Microsoft.Extensions.Logging.ILoggingBuilder,System.String,Microsoft.Extensions.Logging.LogLevel)">
            <summary>
            Adds a log filter for the given <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider"/>.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> to add the filter to.</param>
            <param name="category">The category to filter.</param>
            <param name="level">The level to filter.</param>
            <typeparam name="T">The <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider"/> which this filter will be added for.</typeparam>
            <returns>The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> so that additional calls can be chained.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Logging.FilterLoggingBuilderExtensions.AddFilter(Microsoft.Extensions.Logging.ILoggingBuilder,System.String,System.Func{Microsoft.Extensions.Logging.LogLevel,System.Boolean})">
            <summary>
            Adds a log filter to the factory.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> to add the filter to.</param>
            <param name="category">The category to filter.</param>
            <param name="levelFilter">The filter function to apply.</param>
            <returns>The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> so that additional calls can be chained.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Logging.FilterLoggingBuilderExtensions.AddFilter``1(Microsoft.Extensions.Logging.ILoggingBuilder,System.String,System.Func{Microsoft.Extensions.Logging.LogLevel,System.Boolean})">
            <summary>
            Adds a log filter for the given <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider"/>.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> to add the filter to.</param>
            <param name="category">The category to filter.</param>
            <param name="levelFilter">The filter function to apply.</param>
            <typeparam name="T">The <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider"/> which this filter will be added for.</typeparam>
            <returns>The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> so that additional calls can be chained.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Logging.FilterLoggingBuilderExtensions.AddFilter(Microsoft.Extensions.Logging.LoggerFilterOptions,System.Func{System.String,System.String,Microsoft.Extensions.Logging.LogLevel,System.Boolean})">
            <summary>
            Adds a log filter to the factory.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> to add the filter to.</param>
            <param name="filter">The filter function to apply.</param>
            <returns>The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> so that additional calls can be chained.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Logging.FilterLoggingBuilderExtensions.AddFilter(Microsoft.Extensions.Logging.LoggerFilterOptions,System.Func{System.String,Microsoft.Extensions.Logging.LogLevel,System.Boolean})">
            <summary>
            Adds a log filter to the factory.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.LoggerFilterOptions"/> to add the filter to.</param>
            <param name="categoryLevelFilter">The filter function to apply.</param>
            <returns>The <see cref="T:Microsoft.Extensions.Logging.LoggerFilterOptions"/> so that additional calls can be chained.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Logging.FilterLoggingBuilderExtensions.AddFilter``1(Microsoft.Extensions.Logging.LoggerFilterOptions,System.Func{System.String,Microsoft.Extensions.Logging.LogLevel,System.Boolean})">
            <summary>
            Adds a log filter for the given <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider"/>.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.LoggerFilterOptions"/> to add the filter to.</param>
            <param name="categoryLevelFilter">The filter function to apply.</param>
            <typeparam name="T">The <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider"/> which this filter will be added for.</typeparam>
            <returns>The <see cref="T:Microsoft.Extensions.Logging.LoggerFilterOptions"/> so that additional calls can be chained.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Logging.FilterLoggingBuilderExtensions.AddFilter(Microsoft.Extensions.Logging.LoggerFilterOptions,System.Func{Microsoft.Extensions.Logging.LogLevel,System.Boolean})">
            <summary>
            Adds a log filter to the factory.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.LoggerFilterOptions"/> to add the filter to.</param>
            <param name="levelFilter">The filter function to apply.</param>
            <returns>The <see cref="T:Microsoft.Extensions.Logging.LoggerFilterOptions"/> so that additional calls can be chained.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Logging.FilterLoggingBuilderExtensions.AddFilter``1(Microsoft.Extensions.Logging.LoggerFilterOptions,System.Func{Microsoft.Extensions.Logging.LogLevel,System.Boolean})">
            <summary>
            Adds a log filter for the given <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider"/>.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.LoggerFilterOptions"/> to add the filter to.</param>
            <param name="levelFilter">The filter function to apply.</param>
            <typeparam name="T">The <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider"/> which this filter will be added for.</typeparam>
            <returns>The <see cref="T:Microsoft.Extensions.Logging.LoggerFilterOptions"/> so that additional calls can be chained.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Logging.FilterLoggingBuilderExtensions.AddFilter(Microsoft.Extensions.Logging.LoggerFilterOptions,System.String,Microsoft.Extensions.Logging.LogLevel)">
            <summary>
            Adds a log filter to the factory.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.LoggerFilterOptions"/> to add the filter to.</param>
            <param name="category">The category to filter.</param>
            <param name="level">The level to filter.</param>
            <returns>The <see cref="T:Microsoft.Extensions.Logging.LoggerFilterOptions"/> so that additional calls can be chained.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Logging.FilterLoggingBuilderExtensions.AddFilter``1(Microsoft.Extensions.Logging.LoggerFilterOptions,System.String,Microsoft.Extensions.Logging.LogLevel)">
            <summary>
            Adds a log filter for the given <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider"/>.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.LoggerFilterOptions"/> to add the filter to.</param>
            <param name="category">The category to filter.</param>
            <param name="level">The level to filter.</param>
            <typeparam name="T">The <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider"/> which this filter will be added for.</typeparam>
            <returns>The <see cref="T:Microsoft.Extensions.Logging.LoggerFilterOptions"/> so that additional calls can be chained.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Logging.FilterLoggingBuilderExtensions.AddFilter(Microsoft.Extensions.Logging.LoggerFilterOptions,System.String,System.Func{Microsoft.Extensions.Logging.LogLevel,System.Boolean})">
            <summary>
            Adds a log filter to the factory.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.LoggerFilterOptions"/> to add the filter to.</param>
            <param name="category">The category to filter.</param>
            <param name="levelFilter">The filter function to apply.</param>
            <returns>The <see cref="T:Microsoft.Extensions.Logging.LoggerFilterOptions"/> so that additional calls can be chained.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Logging.FilterLoggingBuilderExtensions.AddFilter``1(Microsoft.Extensions.Logging.LoggerFilterOptions,System.String,System.Func{Microsoft.Extensions.Logging.LogLevel,System.Boolean})">
            <summary>
            Adds a log filter for the given <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider"/>.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> to add the filter to.</param>
            <param name="category">The category to filter.</param>
            <param name="levelFilter">The filter function to apply.</param>
            <typeparam name="T">The <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider"/> which this filter will be added for.</typeparam>
            <returns>The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> so that additional calls can be chained.</returns>
        </member>
        <member name="T:Microsoft.Extensions.Logging.LoggerFactory">
            <summary>
            Produces instances of <see cref="T:Microsoft.Extensions.Logging.ILogger"/> classes based on the given providers.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Logging.LoggerFactory.#ctor">
            <summary>
            Creates a new <see cref="T:Microsoft.Extensions.Logging.LoggerFactory"/> instance.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Logging.LoggerFactory.#ctor(System.Collections.Generic.IEnumerable{Microsoft.Extensions.Logging.ILoggerProvider})">
            <summary>
            Creates a new <see cref="T:Microsoft.Extensions.Logging.LoggerFactory"/> instance.
            </summary>
            <param name="providers">The providers to use in producing <see cref="T:Microsoft.Extensions.Logging.ILogger"/> instances.</param>
        </member>
        <member name="M:Microsoft.Extensions.Logging.LoggerFactory.#ctor(System.Collections.Generic.IEnumerable{Microsoft.Extensions.Logging.ILoggerProvider},Microsoft.Extensions.Logging.LoggerFilterOptions)">
            <summary>
            Creates a new <see cref="T:Microsoft.Extensions.Logging.LoggerFactory"/> instance.
            </summary>
            <param name="providers">The providers to use in producing <see cref="T:Microsoft.Extensions.Logging.ILogger"/> instances.</param>
            <param name="filterOptions">The filter options to use.</param>
        </member>
        <member name="M:Microsoft.Extensions.Logging.LoggerFactory.#ctor(System.Collections.Generic.IEnumerable{Microsoft.Extensions.Logging.ILoggerProvider},Microsoft.Extensions.Options.IOptionsMonitor{Microsoft.Extensions.Logging.LoggerFilterOptions})">
            <summary>
            Creates a new <see cref="T:Microsoft.Extensions.Logging.LoggerFactory"/> instance.
            </summary>
            <param name="providers">The providers to use in producing <see cref="T:Microsoft.Extensions.Logging.ILogger"/> instances.</param>
            <param name="filterOption">The filter option to use.</param>
        </member>
        <member name="M:Microsoft.Extensions.Logging.LoggerFactory.#ctor(System.Collections.Generic.IEnumerable{Microsoft.Extensions.Logging.ILoggerProvider},Microsoft.Extensions.Options.IOptionsMonitor{Microsoft.Extensions.Logging.LoggerFilterOptions},Microsoft.Extensions.Options.IOptions{Microsoft.Extensions.Logging.LoggerFactoryOptions})">
            <summary>
            Creates a new <see cref="T:Microsoft.Extensions.Logging.LoggerFactory"/> instance.
            </summary>
            <param name="providers">The providers to use in producing <see cref="T:Microsoft.Extensions.Logging.ILogger"/> instances.</param>
            <param name="filterOption">The filter option to use.</param>
            <param name="options">The <see cref="T:Microsoft.Extensions.Logging.LoggerFactoryOptions"/>.</param>
        </member>
        <member name="M:Microsoft.Extensions.Logging.LoggerFactory.#ctor(System.Collections.Generic.IEnumerable{Microsoft.Extensions.Logging.ILoggerProvider},Microsoft.Extensions.Options.IOptionsMonitor{Microsoft.Extensions.Logging.LoggerFilterOptions},Microsoft.Extensions.Options.IOptions{Microsoft.Extensions.Logging.LoggerFactoryOptions},Microsoft.Extensions.Logging.IExternalScopeProvider)">
            <summary>
            Creates a new <see cref="T:Microsoft.Extensions.Logging.LoggerFactory"/> instance.
            </summary>
            <param name="providers">The providers to use in producing <see cref="T:Microsoft.Extensions.Logging.ILogger"/> instances.</param>
            <param name="filterOption">The filter option to use.</param>
            <param name="options">The <see cref="T:Microsoft.Extensions.Logging.LoggerFactoryOptions"/>.</param>
            <param name="scopeProvider">The <see cref="T:Microsoft.Extensions.Logging.IExternalScopeProvider"/>.</param>
        </member>
        <member name="M:Microsoft.Extensions.Logging.LoggerFactory.Create(System.Action{Microsoft.Extensions.Logging.ILoggingBuilder})">
            <summary>
            Creates new instance of <see cref="T:Microsoft.Extensions.Logging.ILoggerFactory"/> configured using provided <paramref name="configure"/> delegate.
            </summary>
            <param name="configure">A delegate to configure the <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/>.</param>
            <returns>The <see cref="T:Microsoft.Extensions.Logging.ILoggerFactory"/> that was created.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Logging.LoggerFactory.CreateLogger(System.String)">
            <summary>
            Creates an <see cref="T:Microsoft.Extensions.Logging.ILogger"/> with the given <paramref name="categoryName"/>.
            </summary>
            <param name="categoryName">The category name for messages produced by the logger.</param>
            <returns>The <see cref="T:Microsoft.Extensions.Logging.ILogger"/> that was created.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Logging.LoggerFactory.AddProvider(Microsoft.Extensions.Logging.ILoggerProvider)">
            <summary>
            Adds the given provider to those used in creating <see cref="T:Microsoft.Extensions.Logging.ILogger"/> instances.
            </summary>
            <param name="provider">The <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider"/> to add.</param>
        </member>
        <member name="M:Microsoft.Extensions.Logging.LoggerFactory.CheckDisposed">
            <summary>
            Check if the factory has been disposed.
            </summary>
            <returns><see langword="true" /> when <see cref="M:Microsoft.Extensions.Logging.LoggerFactory.Dispose"/> as been called</returns>
        </member>
        <member name="M:Microsoft.Extensions.Logging.LoggerFactory.Dispose">
            <inheritdoc/>
        </member>
        <member name="T:Microsoft.Extensions.Logging.LoggerFactoryOptions">
            <summary>
            The options for a LoggerFactory.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Logging.LoggerFactoryOptions.#ctor">
            <summary>
            Creates a new <see cref="T:Microsoft.Extensions.Logging.LoggerFactoryOptions"/> instance.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Logging.LoggerFactoryOptions.ActivityTrackingOptions">
            <summary>
            Gets or sets <see cref="T:Microsoft.Extensions.Logging.LoggerFactoryOptions"/> value to indicate which parts of the tracing context information should be included with the logging scopes.
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.Logging.LoggerFactoryScopeProvider">
            <summary>
            Default implementation of <see cref="T:Microsoft.Extensions.Logging.IExternalScopeProvider"/>
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.Logging.LoggerFilterOptions">
            <summary>
            The options for a LoggerFilter.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Logging.LoggerFilterOptions.#ctor">
            <summary>
            Creates a new <see cref="T:Microsoft.Extensions.Logging.LoggerFilterOptions"/> instance.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Logging.LoggerFilterOptions.CaptureScopes">
            <summary>
            Gets or sets a value indicating whether logging scopes are being captured.
            </summary>
            <value>
            The default value is <see langword="true" />
            </value>
        </member>
        <member name="P:Microsoft.Extensions.Logging.LoggerFilterOptions.MinLevel">
            <summary>
            Gets or sets the minimum level of log messages if none of the rules match.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Logging.LoggerFilterOptions.Rules">
            <summary>
            Gets the collection of <see cref="T:Microsoft.Extensions.Logging.LoggerFilterRule"/> used for filtering log messages.
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.Logging.LoggerFilterRule">
            <summary>
            Defines a rule used to filter log messages
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Logging.LoggerFilterRule.#ctor(System.String,System.String,System.Nullable{Microsoft.Extensions.Logging.LogLevel},System.Func{System.String,System.String,Microsoft.Extensions.Logging.LogLevel,System.Boolean})">
            <summary>
            Creates a new <see cref="T:Microsoft.Extensions.Logging.LoggerFilterRule"/> instance.
            </summary>
            <param name="providerName">The provider name to use in this filter rule.</param>
            <param name="categoryName">The category name to use in this filter rule.</param>
            <param name="logLevel">The <see cref="P:Microsoft.Extensions.Logging.LoggerFilterRule.LogLevel"/> to use in this filter rule.</param>
            <param name="filter">The filter to apply.</param>
        </member>
        <member name="P:Microsoft.Extensions.Logging.LoggerFilterRule.ProviderName">
            <summary>
            Gets the logger provider type or alias this rule applies to.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Logging.LoggerFilterRule.CategoryName">
            <summary>
            Gets the logger category this rule applies to.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Logging.LoggerFilterRule.LogLevel">
            <summary>
            Gets the minimum <see cref="P:Microsoft.Extensions.Logging.LoggerFilterRule.LogLevel"/> of messages.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Logging.LoggerFilterRule.Filter">
            <summary>
            Gets the filter delegate that would be applied to messages that passed the <see cref="P:Microsoft.Extensions.Logging.LoggerFilterRule.LogLevel"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Logging.LoggerFilterRule.ToString">
            <inheritdoc/>
        </member>
        <member name="T:Microsoft.Extensions.Logging.LoggingBuilderExtensions">
            <summary>
            Extension methods for setting up logging services in an <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder" />.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Logging.LoggingBuilderExtensions.SetMinimumLevel(Microsoft.Extensions.Logging.ILoggingBuilder,Microsoft.Extensions.Logging.LogLevel)">
            <summary>
            Sets a minimum <see cref="T:Microsoft.Extensions.Logging.LogLevel"/> requirement for log messages to be logged.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> to set the minimum level on.</param>
            <param name="level">The <see cref="T:Microsoft.Extensions.Logging.LogLevel"/> to set as the minimum.</param>
            <returns>The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> so that additional calls can be chained.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Logging.LoggingBuilderExtensions.AddProvider(Microsoft.Extensions.Logging.ILoggingBuilder,Microsoft.Extensions.Logging.ILoggerProvider)">
            <summary>
            Adds the given <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider"/> to the <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/>
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> to add the <paramref name="provider"/> to.</param>
            <param name="provider">The <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider"/> to add to the <paramref name="builder"/>.</param>
            <returns>The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> so that additional calls can be chained.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Logging.LoggingBuilderExtensions.ClearProviders(Microsoft.Extensions.Logging.ILoggingBuilder)">
            <summary>
            Removes all <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider"/>s from <paramref name="builder"/>.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> to remove <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider"/>s from.</param>
            <returns>The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> so that additional calls can be chained.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Logging.LoggingBuilderExtensions.Configure(Microsoft.Extensions.Logging.ILoggingBuilder,System.Action{Microsoft.Extensions.Logging.LoggerFactoryOptions})">
            <summary>
            Configure the <paramref name="builder"/> with the <see cref="T:Microsoft.Extensions.Logging.LoggerFactoryOptions"/>.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> to be configured with <see cref="T:Microsoft.Extensions.Logging.LoggerFactoryOptions"/>.</param>
            <param name="action">The action used to configure the logger factory.</param>
            <returns>The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> so that additional calls can be chained.</returns>
        </member>
        <member name="T:Microsoft.Extensions.Logging.ProviderAliasAttribute">
            <summary>
            Defines alias for <see cref="T:Microsoft.Extensions.Logging.ILoggerProvider"/> implementation to be used in filtering rules.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Logging.ProviderAliasAttribute.#ctor(System.String)">
            <summary>
            Creates a new <see cref="T:Microsoft.Extensions.Logging.ProviderAliasAttribute"/> instance.
            </summary>
            <param name="alias">The alias to set.</param>
        </member>
        <member name="P:Microsoft.Extensions.Logging.ProviderAliasAttribute.Alias">
            <summary>
            Gets the alias of the provider.
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.Logging.NullExternalScopeProvider">
            <summary>
            Scope provider that does nothing.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Logging.NullExternalScopeProvider.Instance">
            <summary>
            Returns a cached instance of <see cref="T:Microsoft.Extensions.Logging.NullExternalScopeProvider"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Logging.NullExternalScopeProvider.Microsoft#Extensions#Logging#IExternalScopeProvider#ForEachScope``1(System.Action{System.Object,``0},``0)">
            <inheritdoc />
        </member>
        <member name="M:Microsoft.Extensions.Logging.NullExternalScopeProvider.Microsoft#Extensions#Logging#IExternalScopeProvider#Push(System.Object)">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.Extensions.Logging.NullScope">
            <summary>
            An empty scope without any logic
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Logging.NullScope.Dispose">
            <inheritdoc />
        </member>
        <member name="T:Microsoft.Extensions.DependencyInjection.LoggingServiceCollectionExtensions">
            <summary>
            Extension methods for setting up logging services in an <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.LoggingServiceCollectionExtensions.AddLogging(Microsoft.Extensions.DependencyInjection.IServiceCollection)">
            <summary>
            Adds logging services to the specified <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.
            </summary>
            <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> to add services to.</param>
            <returns>The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> so that additional calls can be chained.</returns>
        </member>
        <member name="M:Microsoft.Extensions.DependencyInjection.LoggingServiceCollectionExtensions.AddLogging(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Action{Microsoft.Extensions.Logging.ILoggingBuilder})">
            <summary>
            Adds logging services to the specified <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" />.
            </summary>
            <param name="services">The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection" /> to add services to.</param>
            <param name="configure">The <see cref="T:Microsoft.Extensions.Logging.ILoggingBuilder"/> configuration delegate.</param>
            <returns>The <see cref="T:Microsoft.Extensions.DependencyInjection.IServiceCollection"/> so that additional calls can be chained.</returns>
        </member>
        <member name="M:System.ThrowHelper.ThrowIfNull(System.Object,System.String)">
            <summary>Throws an <see cref="T:System.ArgumentNullException"/> if <paramref name="argument"/> is null.</summary>
            <param name="argument">The reference type argument to validate as non-null.</param>
            <param name="paramName">The name of the parameter with which <paramref name="argument"/> corresponds.</param>
        </member>
        <member name="M:System.ThrowHelper.IfNullOrWhitespace(System.String,System.String)">
            <summary>
            Throws either an <see cref="T:System.ArgumentNullException"/> or an <see cref="T:System.ArgumentException"/>
            if the specified string is <see langword="null"/> or whitespace respectively.
            </summary>
            <param name="argument">String to be checked for <see langword="null"/> or whitespace.</param>
            <param name="paramName">The name of the parameter being checked.</param>
            <returns>The original value of <paramref name="argument"/>.</returns>
        </member>
        <member name="P:System.SR.InvalidActivityTrackingOptions">
            <summary>{0} is invalid ActivityTrackingOptions value.</summary>
        </member>
        <member name="P:System.SR.MoreThanOneWildcard">
            <summary>Only one wildcard character is allowed in category name.</summary>
        </member>
    </members>
</doc>
