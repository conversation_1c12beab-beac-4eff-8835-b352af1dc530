﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>Microsoft.VisualBasic</name>
  </assembly>
  <members>
    <member name="T:Microsoft.VisualBasic.CallType">
      <summary>表示呼叫 CallByName 函式時所叫用的程序類型。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:Microsoft.VisualBasic.CallType.Get">
      <summary>正在擷取屬性值。這個成員相當於 Visual Basic 常數 vbGet。</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.CallType.Let">
      <summary>正在決定物件屬性值。這個成員相當於 Visual Basic 常數 vbLet。</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.CallType.Method">
      <summary>正在叫用方法。這個成員相當於 Visual Basic 常數 vbMethod。</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.CallType.Set">
      <summary>正在決定屬性值。這個成員相當於 Visual Basic 常數 vbSet。</summary>
    </member>
    <member name="T:Microsoft.VisualBasic.Constants">
      <summary>Constants 模組包含其他常數，這些常數可用在程式碼當中的任何地方。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbBack">
      <summary>表示呼叫列印和顯示功能時使用的退格鍵 (Backspace)。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbCr">
      <summary>表示列印及顯示函數的歸位字元。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbCrLf">
      <summary>表示列印及顯示函數之結合換行字元的歸位字元。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbFormFeed">
      <summary>表示呼叫列印功能時使用的換頁字元。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbLf">
      <summary>表示呼叫列印和顯示功能時使用的換行字元。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbNewLine">
      <summary>表示列印及顯示函數的新行字元。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbNullChar">
      <summary>表示呼叫列印和顯示功能時使用的 null 字元。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbNullString">
      <summary>表示列印及顯示函數之長度為零的字串，用來呼叫外部程序。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbTab">
      <summary>表示列印及顯示函數的定位字元。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbVerticalTab">
      <summary>表示呼叫列印功能時使用的歸位字元。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:Microsoft.VisualBasic.HideModuleNameAttribute">
      <summary>HideModuleNameAttribute 屬性在套用到模組時，只能利用模組所需的限定性條件來存取模組成員。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:Microsoft.VisualBasic.HideModuleNameAttribute.#ctor">
      <summary>初始化 <see cref="T:Microsoft.VisualBasic.HideModuleNameAttribute" /> 屬性的新執行個體。</summary>
    </member>
    <member name="T:Microsoft.VisualBasic.Strings">
      <summary>Strings 模組包含用來執行字串作業的程序。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:Microsoft.VisualBasic.Strings.AscW(System.Char)">
      <summary>傳回 Integer 值，表示對應至字元的字元碼。</summary>
      <returns>傳回 Integer 值，表示對應至字元的字元碼。</returns>
      <param name="String">必要項。任何有效的 Char 或 String 運算式。如果 <paramref name="String" /> 為 String 運算式，則只有字串的第一個字元才會用於輸入。如果 <paramref name="String" /> 為 Nothing 或者不包含任何字元，則會發生 <see cref="T:System.ArgumentException" /> 錯誤。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:Microsoft.VisualBasic.Strings.AscW(System.String)">
      <summary>傳回 Integer 值，表示對應至字元的字元碼。</summary>
      <returns>傳回 Integer 值，表示對應至字元的字元碼。</returns>
      <param name="String">必要項。任何有效的 Char 或 String 運算式。如果 <paramref name="String" /> 為 String 運算式，則只有字串的第一個字元才會用於輸入。如果 <paramref name="String" /> 為 Nothing 或者不包含任何字元，則會發生 <see cref="T:System.ArgumentException" /> 錯誤。</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:Microsoft.VisualBasic.Strings.ChrW(System.Int32)">
      <summary>傳回與指定的字元碼關聯的字元。</summary>
      <returns>傳回與指定的字元碼關聯的字元。</returns>
      <param name="CharCode">必要項。Integer 運算式，表示字元的 <paramref name="code point" /> 或字元碼。</param>
      <exception cref="T:System.ArgumentException">ChrW 的 <paramref name="CharCode" /> &lt; -32768 或 &gt; 65535。</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:Microsoft.VisualBasic.CompilerServices.Conversions">
      <summary>提供會執行各種型別轉換的方法。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ChangeType(System.Object,System.Type)">
      <summary>將物件轉換成指定的型別。</summary>
      <returns>指定之目標型別的物件。</returns>
      <param name="Expression">要進行轉換的物件。</param>
      <param name="TargetType">要將物件轉換成的型別。</param>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToBoolean(System.Object)">
      <summary>將物件轉換成 <see cref="T:System.Boolean" /> 值。</summary>
      <returns>Boolean 值。如果物件為 null，則傳回 False，否則傳回 True。</returns>
      <param name="Value">要進行轉換的物件。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToBoolean(System.String)">
      <summary>將字串轉換成 <see cref="T:System.Boolean" /> 值。</summary>
      <returns>Boolean 值。如果字串為 null，則傳回 False，否則傳回 True。</returns>
      <param name="Value">要轉換的字串。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToByte(System.Object)">
      <summary>將物件轉換成 <see cref="T:System.Byte" /> 值。</summary>
      <returns>物件的 Byte 值。</returns>
      <param name="Value">要進行轉換的物件。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToByte(System.String)">
      <summary>將字串轉換成 <see cref="T:System.Byte" /> 值。</summary>
      <returns>字串的 Byte 值。</returns>
      <param name="Value">要轉換的字串。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToChar(System.Object)">
      <summary>將物件轉換成 <see cref="T:System.Char" /> 值。</summary>
      <returns>物件的 Char 值。</returns>
      <param name="Value">要進行轉換的物件。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToChar(System.String)">
      <summary>將字串轉換成 <see cref="T:System.Char" /> 值。</summary>
      <returns>字串的 Char 值。</returns>
      <param name="Value">要轉換的字串。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToCharArrayRankOne(System.Object)">
      <summary>將物件轉換成一維 <see cref="T:System.Char" /> 陣列。</summary>
      <returns>一維 Char 陣列。</returns>
      <param name="Value">要進行轉換的物件。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToCharArrayRankOne(System.String)">
      <summary>將字串轉換成一維 <see cref="T:System.Char" /> 陣列。</summary>
      <returns>一維 Char 陣列。</returns>
      <param name="Value">要轉換的字串。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToDate(System.Object)">
      <summary>將物件轉換成 <see cref="T:System.DateTime" /> 值。</summary>
      <returns>物件的 DateTime 值。</returns>
      <param name="Value">要進行轉換的物件。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToDate(System.String)">
      <summary>將字串轉換成 <see cref="T:System.DateTime" /> 值。</summary>
      <returns>字串的 DateTime 值。</returns>
      <param name="Value">要轉換的字串。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToDecimal(System.Boolean)">
      <summary>將 <see cref="T:System.Boolean" /> 值轉換成 <see cref="T:System.Decimal" /> 值。</summary>
      <returns>布林值的 Decimal 值。</returns>
      <param name="Value">要轉換的布林值。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToDecimal(System.Object)">
      <summary>將物件轉換成 <see cref="T:System.Decimal" /> 值。</summary>
      <returns>物件的 Decimal 值。</returns>
      <param name="Value">要進行轉換的物件。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToDecimal(System.String)">
      <summary>將字串轉換成 <see cref="T:System.Decimal" /> 值。</summary>
      <returns>字串的 Decimal 值。</returns>
      <param name="Value">要轉換的字串。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToDouble(System.Object)">
      <summary>將物件轉換成 <see cref="T:System.Double" /> 值。</summary>
      <returns>物件的 Double 值。</returns>
      <param name="Value">要進行轉換的物件。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToDouble(System.String)">
      <summary>將字串轉換成 <see cref="T:System.Double" /> 值。</summary>
      <returns>字串的 Double 值。</returns>
      <param name="Value">要轉換的字串。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToGenericParameter``1(System.Object)">
      <summary>將物件轉換成泛型型別 <paramref name="T" />。</summary>
      <returns>泛型型別 <paramref name="T" /> 的結構或物件。</returns>
      <param name="Value">要進行轉換的物件。</param>
      <typeparam name="T">要將 <paramref name="Value" /> 轉換成哪一個型別。</typeparam>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToInteger(System.Object)">
      <summary>將物件轉換成整數值。</summary>
      <returns>物件的 int 值。</returns>
      <param name="Value">要進行轉換的物件。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToInteger(System.String)">
      <summary>將字串轉換成整數值。</summary>
      <returns>字串的 int 值。</returns>
      <param name="Value">要轉換的字串。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToLong(System.Object)">
      <summary>將物件轉換成 Long 值。</summary>
      <returns>物件的 Long 值。</returns>
      <param name="Value">要進行轉換的物件。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToLong(System.String)">
      <summary>將字串轉換成 Long 值。</summary>
      <returns>字串的 Long 值。</returns>
      <param name="Value">要轉換的字串。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToSByte(System.Object)">
      <summary>將物件轉換成 <see cref="T:System.SByte" /> 值。</summary>
      <returns>物件的 SByte 值。</returns>
      <param name="Value">要進行轉換的物件。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToSByte(System.String)">
      <summary>將字串轉換成 <see cref="T:System.SByte" /> 值。</summary>
      <returns>字串的 SByte 值。</returns>
      <param name="Value">要轉換的字串。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToShort(System.Object)">
      <summary>將物件轉換成 Short 值。</summary>
      <returns>物件的 Short 值。</returns>
      <param name="Value">要進行轉換的物件。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToShort(System.String)">
      <summary>將字串轉換成 Short 值。</summary>
      <returns>字串的 Short 值。</returns>
      <param name="Value">要轉換的字串。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToSingle(System.Object)">
      <summary>將物件轉換成 <see cref="T:System.Single" /> 值。</summary>
      <returns>物件的 Single 值。</returns>
      <param name="Value">要進行轉換的物件。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToSingle(System.String)">
      <summary>將 <see cref="T:System.String" /> 轉換成 <see cref="T:System.Single" /> 值。</summary>
      <returns>字串的 Single 值。</returns>
      <param name="Value">要轉換的字串。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToString(System.Boolean)">
      <summary>將 <see cref="T:System.Boolean" /> 值轉換成 <see cref="T:System.String" />。</summary>
      <returns>Boolean 值的 String 表示。</returns>
      <param name="Value">要轉換的 Boolean 值。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToString(System.Byte)">
      <summary>將 <see cref="T:System.Byte" /> 值轉換成 <see cref="T:System.String" />。</summary>
      <returns>Byte 值的 String 表示。</returns>
      <param name="Value">要轉換的 Byte 值。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToString(System.Char)">
      <summary>將 <see cref="T:System.Char" /> 值轉換成 <see cref="T:System.String" />。</summary>
      <returns>Char 值的 String 表示。</returns>
      <param name="Value">要轉換的 Char 值。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToString(System.DateTime)">
      <summary>將 <see cref="T:System.DateTime" /> 值轉換為 <see cref="T:System.String" /> 值。</summary>
      <returns>DateTime 值的 String 表示。</returns>
      <param name="Value">要轉換的 DateTime 值。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToString(System.Decimal)">
      <summary>將 <see cref="T:System.Decimal" /> 值轉換為 <see cref="T:System.String" /> 值。</summary>
      <returns>Decimal 值的 String 表示。</returns>
      <param name="Value">要轉換的 Decimal 值。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToString(System.Double)">
      <summary>將 <see cref="T:System.Double" /> 值轉換為 <see cref="T:System.String" /> 值。</summary>
      <returns>Double 值的 String 表示。</returns>
      <param name="Value">要轉換的 Double 值。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToString(System.Int16)">
      <summary>將 Short 值轉換為 <see cref="T:System.String" /> 值。</summary>
      <returns>Short 值的 String 表示。</returns>
      <param name="Value">要轉換的 Short 值。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToString(System.Int32)">
      <summary>將整數值轉換成 <see cref="T:System.String" /> 值。</summary>
      <returns>int 值的 String 表示。</returns>
      <param name="Value">要轉換的 int 值。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToString(System.Int64)">
      <summary>將 Long 值轉換為 <see cref="T:System.String" /> 值。</summary>
      <returns>Long 值的 String 表示。</returns>
      <param name="Value">要轉換的 Long 值。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToString(System.Object)">
      <summary>將物件轉換成 <see cref="T:System.String" /> 值。</summary>
      <returns>物件的 String 表示。</returns>
      <param name="Value">要進行轉換的物件。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToString(System.Single)">
      <summary>將 <see cref="T:System.Single" /> 值 (單精確度浮點數) 轉換成 <see cref="T:System.String" /> 值。</summary>
      <returns>Single 值的 String 表示。</returns>
      <param name="Value">要轉換的 Single 值。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToString(System.UInt32)">
      <summary>將 uint 值轉換為 <see cref="T:System.String" /> 值。</summary>
      <returns>Uint 值的 String 表示。</returns>
      <param name="Value">要轉換的 Uint 值。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToString(System.UInt64)">
      <summary>將 Ulong 值轉換為 <see cref="T:System.String" /> 值。</summary>
      <returns>Ulong 值的 String 表示。</returns>
      <param name="Value">要轉換的 Ulong 值。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToUInteger(System.Object)">
      <summary>將物件轉換成 Uint 值。</summary>
      <returns>物件的 Uint 值。</returns>
      <param name="Value">要進行轉換的物件。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToUInteger(System.String)">
      <summary>將字串轉換成 Uint 值。</summary>
      <returns>字串的 Uint 值。</returns>
      <param name="Value">要轉換的字串。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToULong(System.Object)">
      <summary>將物件轉換成 Ulong 值。</summary>
      <returns>物件的 Ulong 值。</returns>
      <param name="Value">要進行轉換的物件。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToULong(System.String)">
      <summary>將字串轉換成 Ulong 值。</summary>
      <returns>字串的 Ulong 值。</returns>
      <param name="Value">要轉換的字串。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToUShort(System.Object)">
      <summary>將物件轉換成 Ushort 值。</summary>
      <returns>物件的 Ushort 值。</returns>
      <param name="Value">要進行轉換的物件。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToUShort(System.String)">
      <summary>將字串轉換成 Ushort 值。</summary>
      <returns>字串的 Ushort 值。</returns>
      <param name="Value">要轉換的字串。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="T:Microsoft.VisualBasic.CompilerServices.DesignerGeneratedAttribute">
      <summary>當套用至類別 (Class) 時，編譯器會從預設虛構建構函式 (Constructor) 隱含地呼叫元件初始化方法。</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.DesignerGeneratedAttribute.#ctor">
      <summary>初始化 <see cref="T:Microsoft.VisualBasic.CompilerServices.DesignerGeneratedAttribute" /> 屬性的新執行個體。</summary>
    </member>
    <member name="T:Microsoft.VisualBasic.CompilerServices.IncompleteInitialization">
      <summary>Visual Basic 編譯器在靜態區域變數初始設定時會使用這個類別，並不適合從您的程式碼直接呼叫。如果靜態區域變數無法初始化，會擲回這個型別的例外狀況。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.IncompleteInitialization.#ctor">
      <summary>初始化 <see cref="T:Microsoft.VisualBasic.CompilerServices.IncompleteInitialization" /> 類別的新執行個體。</summary>
    </member>
    <member name="T:Microsoft.VisualBasic.CompilerServices.NewLateBinding">
      <summary>這個類別提供 Helper，讓 Visual Basic 編譯器用於晚期繫結呼叫，並不適合從您的程式碼直接呼叫。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.NewLateBinding.LateCall(System.Object,System.Type,System.String,System.Object[],System.String[],System.Type[],System.Boolean[],System.Boolean)">
      <summary>執行晚期繫結方法或函式呼叫。這個 Helper 方法並不適合從您的程式碼直接呼叫。</summary>
      <returns>呼叫物件的執行個體。</returns>
      <param name="Instance">公開屬性或方法的呼叫物件執行個體。</param>
      <param name="Type">呼叫物件的型別。</param>
      <param name="MemberName">在呼叫物件上的屬性或方法名稱。</param>
      <param name="Arguments">陣列，其中包含要傳遞給呼叫的屬性或方法之引數。</param>
      <param name="ArgumentNames">引數名稱的陣列。</param>
      <param name="TypeArguments">引數型別的陣列；只用於傳遞引數型別的泛型呼叫。</param>
      <param name="CopyBack">Boolean 值的陣列，晚期繫結器用來向呼叫站台傳達，哪些引數符合 ByRef 參數。每個 True 值表示符合的引數，並且在 LateCall 呼叫完成之後應複製出這些引數。</param>
      <param name="IgnoreReturn">Boolean 值，指出是否可以忽略傳回值。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.NewLateBinding.LateGet(System.Object,System.Type,System.String,System.Object[],System.String[],System.Type[],System.Boolean[])">
      <summary>執行晚期繫結屬性 Get 或欄位存取呼叫。這個 Helper 方法並不適合從您的程式碼直接呼叫。</summary>
      <returns>呼叫物件的執行個體。</returns>
      <param name="Instance">公開屬性或方法的呼叫物件執行個體。</param>
      <param name="Type">呼叫物件的型別。</param>
      <param name="MemberName">在呼叫物件上的屬性或方法名稱。</param>
      <param name="Arguments">陣列，其中包含要傳遞給呼叫的屬性或方法之引數。</param>
      <param name="ArgumentNames">引數名稱的陣列。</param>
      <param name="TypeArguments">引數型別的陣列；只用於傳遞引數型別的泛型呼叫。</param>
      <param name="CopyBack">Boolean 值的陣列，晚期繫結器用來向呼叫站台傳達，哪些引數符合 ByRef 參數。每個 True 值表示符合的引數，並且在 LateCall 呼叫完成之後應複製出這些引數。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.NewLateBinding.LateIndexGet(System.Object,System.Object[],System.String[])">
      <summary>執行晚期繫結屬性 Get 或欄位存取呼叫。這個 Helper 方法並不適合從您的程式碼直接呼叫。</summary>
      <returns>呼叫物件的執行個體。</returns>
      <param name="Instance">公開屬性或方法的呼叫物件執行個體。</param>
      <param name="Arguments">陣列，其中包含要傳遞給呼叫的屬性或方法之引數。</param>
      <param name="ArgumentNames">引數名稱的陣列。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.NewLateBinding.LateIndexSet(System.Object,System.Object[],System.String[])">
      <summary>執行晚期繫結屬性 Set 或欄位寫入呼叫。這個 Helper 方法並不適合從您的程式碼直接呼叫。</summary>
      <param name="Instance">公開屬性或方法的呼叫物件執行個體。</param>
      <param name="Arguments">陣列，其中包含要傳遞給呼叫的屬性或方法之引數。</param>
      <param name="ArgumentNames">引數名稱的陣列。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.NewLateBinding.LateIndexSetComplex(System.Object,System.Object[],System.String[],System.Boolean,System.Boolean)">
      <summary>執行晚期繫結屬性 Set 或欄位寫入呼叫。這個 Helper 方法並不適合從您的程式碼直接呼叫。</summary>
      <param name="Instance">公開屬性或方法的呼叫物件執行個體。</param>
      <param name="Arguments">陣列，其中包含要傳遞給呼叫的屬性或方法之引數。</param>
      <param name="ArgumentNames">引數名稱的陣列。</param>
      <param name="OptimisticSet">Boolean 值，用來決定設定作業是否會運作。當您相信屬性或欄位中已設定中繼值時，請設為 True，否則為 False。</param>
      <param name="RValueBase">Boolean 值，指定晚期參考的基底參考為 RValue。當晚期參考的基底參考為 RValue 時，若設為 True，對於實值型別之 RValues 欄位的晚期指派，您可以產生執行階段例外狀況。否則，請設為 False。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.NewLateBinding.LateSet(System.Object,System.Type,System.String,System.Object[],System.String[],System.Type[])">
      <summary>執行晚期繫結屬性 Set 或欄位寫入呼叫。這個 Helper 方法並不適合從您的程式碼直接呼叫。</summary>
      <param name="Instance">公開屬性或方法的呼叫物件執行個體。</param>
      <param name="Type">呼叫物件的型別。</param>
      <param name="MemberName">在呼叫物件上的屬性或方法名稱。</param>
      <param name="Arguments">陣列，其中包含要傳遞給呼叫的屬性或方法之引數。</param>
      <param name="ArgumentNames">引數名稱的陣列。</param>
      <param name="TypeArguments">引數型別的陣列；只用於傳遞引數型別的泛型呼叫。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.NewLateBinding.LateSet(System.Object,System.Type,System.String,System.Object[],System.String[],System.Type[],System.Boolean,System.Boolean,Microsoft.VisualBasic.CallType)">
      <summary>執行晚期繫結屬性 Set 或欄位寫入呼叫。這個 Helper 方法並不適合從您的程式碼直接呼叫。</summary>
      <param name="Instance">公開屬性或方法的呼叫物件執行個體。</param>
      <param name="Type">呼叫物件的型別。</param>
      <param name="MemberName">在呼叫物件上的屬性或方法名稱。</param>
      <param name="Arguments">陣列，其中包含要傳遞給呼叫的屬性或方法之引數。</param>
      <param name="ArgumentNames">引數名稱的陣列。</param>
      <param name="TypeArguments">引數型別的陣列；只用於傳遞引數型別的泛型呼叫。</param>
      <param name="OptimisticSet">Boolean 值，用來決定設定作業是否會運作。當您相信屬性或欄位中已設定中繼值時，請設為 True，否則為 False。</param>
      <param name="RValueBase">Boolean 值，指定晚期參考的基底參考為 RValue。當晚期參考的基底參考為 RValue 時，若設為 True，對於實值型別之 RValues 欄位的晚期指派，您可以產生執行階段例外狀況。否則，請設為 False。</param>
      <param name="CallType">型別 <see cref="T:Microsoft.VisualBasic.CallType" /> 的列舉型別成員，其表示正在呼叫的程序型別。CallType 的值可以是 Method、Get 或 Set。僅使用 Set。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.NewLateBinding.LateSetComplex(System.Object,System.Type,System.String,System.Object[],System.String[],System.Type[],System.Boolean,System.Boolean)">
      <summary>執行晚期繫結屬性 Set 或欄位寫入呼叫。這個 Helper 方法並不適合從您的程式碼直接呼叫。</summary>
      <param name="Instance">公開屬性或方法的呼叫物件執行個體。</param>
      <param name="Type">呼叫物件的型別。</param>
      <param name="MemberName">在呼叫物件上的屬性或方法名稱。</param>
      <param name="Arguments">陣列，其中包含要傳遞給呼叫的屬性或方法之引數。</param>
      <param name="ArgumentNames">引數名稱的陣列。</param>
      <param name="TypeArguments">引數型別的陣列；只用於傳遞引數型別的泛型呼叫。</param>
      <param name="OptimisticSet">Boolean 值，用來決定設定作業是否會運作。當您相信屬性或欄位中已設定中繼值時，請設為 True，否則為 False。</param>
      <param name="RValueBase">Boolean 值，指定晚期參考的基底參考為 RValue。當晚期參考的基底參考為 RValue 時，若設為 True，對於實值型別之 RValues 欄位的晚期指派，您可以產生執行階段例外狀況。否則，請設為 False。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="T:Microsoft.VisualBasic.CompilerServices.ObjectFlowControl">
      <summary>Visual Basic 編譯器將這個類別用於物件流量控制，並不適合從您的程式碼直接呼叫。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.ObjectFlowControl.CheckForSyncLockOnValueType(System.Object)">
      <summary>檢查指定的型別上是否有同步鎖定。</summary>
      <param name="Expression">要檢查同步鎖定的資料型別。</param>
    </member>
    <member name="T:Microsoft.VisualBasic.CompilerServices.ObjectFlowControl.ForLoopControl">
      <summary>提供服務給 Visual Basic 編譯器來編譯 For...Next 迴圈。</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.ObjectFlowControl.ForLoopControl.ForLoopInitObj(System.Object,System.Object,System.Object,System.Object,System.Object@,System.Object@)">
      <summary>初始化 For...Next 迴圈。</summary>
      <returns>如果迴圈已終止，則為 False，否則為 True。</returns>
      <param name="Counter">迴圈計數器變數。</param>
      <param name="Start">迴圈計數器的初始值。</param>
      <param name="Limit">To 選項的值。</param>
      <param name="StepValue">Step 選項的值。</param>
      <param name="LoopForResult">物件，其中包含迴圈值已驗證的值。</param>
      <param name="CounterResult">下一個迴圈反覆運算的計數器值。</param>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.ObjectFlowControl.ForLoopControl.ForNextCheckDec(System.Decimal,System.Decimal,System.Decimal)">
      <summary>檢查迴圈計數器、Step 和 To 值的有效值。</summary>
      <returns>如果 <paramref name="StepValue" /> 大於零且 <paramref name="count" /> 小於或等於 <paramref name="limit" />，或是 <paramref name="StepValue" /> 小於或等於零且 <paramref name="count" /> 大於或等於 <paramref name="limit" />，則為 True，否則為 False。</returns>
      <param name="count">必要項。Decimal 值，表示傳遞給迴圈計數器變數的初始值。</param>
      <param name="limit">必要項。Decimal 值，表示使用 To 關鍵字傳遞的值。</param>
      <param name="StepValue">必要項。Decimal 值，表示使用 Step 關鍵字傳遞的值。</param>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.ObjectFlowControl.ForLoopControl.ForNextCheckObj(System.Object,System.Object,System.Object@)">
      <summary>遞增 For...Next 迴圈。</summary>
      <returns>如果迴圈已終止，則為 False，否則為 True。</returns>
      <param name="Counter">迴圈計數器變數。</param>
      <param name="LoopObj">物件，其中包含迴圈值已驗證的值。</param>
      <param name="CounterResult">下一個迴圈反覆運算的計數器值。</param>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.ObjectFlowControl.ForLoopControl.ForNextCheckR4(System.Single,System.Single,System.Single)">
      <summary>檢查迴圈計數器、Step 和 To 值的有效值。</summary>
      <returns>如果 <paramref name="StepValue" /> 大於零且 <paramref name="count" /> 小於或等於 <paramref name="limit" />，或是 <paramref name="StepValue" /> 小於或等於零且 <paramref name="count" /> 大於或等於 <paramref name="limit" />，則為 True，否則為 False。</returns>
      <param name="count">必要項。Single 值，表示傳遞給迴圈計數器變數的初始值。</param>
      <param name="limit">必要項。Single 值，表示使用 To 關鍵字傳遞的值。</param>
      <param name="StepValue">必要項。Single 值，表示使用 Step 關鍵字傳遞的值。</param>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.ObjectFlowControl.ForLoopControl.ForNextCheckR8(System.Double,System.Double,System.Double)">
      <summary>檢查迴圈計數器、Step 和 To 值的有效值。</summary>
      <returns>如果 <paramref name="StepValue" /> 大於零且 <paramref name="count" /> 小於或等於 <paramref name="limit" />，或是 <paramref name="StepValue" /> 小於或等於零且 <paramref name="count" /> 大於或等於 <paramref name="limit" />，則為 True，否則為 False。</returns>
      <param name="count">必要項。Double 值，表示傳遞給迴圈計數器變數的初始值。</param>
      <param name="limit">必要項。Double 值，表示使用 To 關鍵字傳遞的值。</param>
      <param name="StepValue">必要項。Double 值，表示使用 Step 關鍵字傳遞的值。</param>
    </member>
    <member name="T:Microsoft.VisualBasic.CompilerServices.Operators">
      <summary>提供晚期繫結算術運算子，例如 <see cref="M:Microsoft.VisualBasic.CompilerServices.Operators.AddObject(System.Object,System.Object)" /> 和 <see cref="M:Microsoft.VisualBasic.CompilerServices.Operators.CompareObject(System.Object,System.Object,System.Boolean)" />，Visual Basic 編譯器在內部使用這些運算子。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.AddObject(System.Object,System.Object)">
      <summary>表示 Visual Basic 加法 (+) 運算子。</summary>
      <returns>
        <paramref name="Left" /> 和 <paramref name="Right" /> 的總和。</returns>
      <param name="Left">必要項。任何數值運算式。</param>
      <param name="Right">必要項。任何數值運算式。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.AndObject(System.Object,System.Object)">
      <summary>表示 Visual Basic And 運算子。</summary>
      <returns>對於 Boolean 運算，如果 <paramref name="Left" /> 和 <paramref name="Right" /> 都評估為 True，則為 True，否則為 False。對於位元運算，如果 <paramref name="Left" /> 和 <paramref name="Right" /> 都評估為 1，則為 1，否則為 0。</returns>
      <param name="Left">必要項。任何 Boolean 或數值運算式。</param>
      <param name="Right">必要項。任何 Boolean 或數值運算式。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.CompareObjectEqual(System.Object,System.Object,System.Boolean)">
      <summary>表示 Visual Basic 等號 (=) 運算子。</summary>
      <returns>如果 <paramref name="Left" /> 和 <paramref name="Right" /> 相等，則為 True，否則為 False。</returns>
      <param name="Left">必要項。任何運算式。</param>
      <param name="Right">必要項。任何運算式。</param>
      <param name="TextCompare">必要項。True 表示要執行不區分大小寫的字串比較，否則為 False。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.CompareObjectGreater(System.Object,System.Object,System.Boolean)">
      <summary>表示 Visual Basic 大於 (&gt;) 運算子。</summary>
      <returns>如果 <paramref name="Left" /> 大於 <paramref name="Right" />，則為 True，否則為 False。</returns>
      <param name="Left">必要項。任何運算式。</param>
      <param name="Right">必要項。任何運算式。</param>
      <param name="TextCompare">必要項。True 表示要執行不區分大小寫的字串比較，否則為 False。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.CompareObjectGreaterEqual(System.Object,System.Object,System.Boolean)">
      <summary>表示 Visual Basic 大於或等於 (&gt;=) 運算子。</summary>
      <returns>如果 <paramref name="Left" /> 大於或等於 <paramref name="Right" /> 則為 True，否則為 False。</returns>
      <param name="Left">必要項。任何運算式。</param>
      <param name="Right">必要項。任何運算式。</param>
      <param name="TextCompare">必要項。True 表示要執行不區分大小寫的字串比較，否則為 False。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.CompareObjectLess(System.Object,System.Object,System.Boolean)">
      <summary>表示 Visual Basic 小於 (&lt;) 運算子。</summary>
      <returns>如果 <paramref name="Left" /> 小於 <paramref name="Right" />，則為 True，否則為 False。</returns>
      <param name="Left">必要項。任何運算式。</param>
      <param name="Right">必要項。任何運算式。</param>
      <param name="TextCompare">必要項。True 表示要執行不區分大小寫的字串比較，否則為 False。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.CompareObjectLessEqual(System.Object,System.Object,System.Boolean)">
      <summary>表示 Visual Basic 小於或等於 (&lt;=) 運算子。</summary>
      <returns>如果 <paramref name="Left" /> 小於或等於 <paramref name="Right" /> 則為 True，否則為 False。</returns>
      <param name="Left">必要項。任何運算式。</param>
      <param name="Right">必要項。任何運算式。</param>
      <param name="TextCompare">必要項。True 表示要執行不區分大小寫的字串比較，否則為 False。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.CompareObjectNotEqual(System.Object,System.Object,System.Boolean)">
      <summary>表示 Visual Basic 不等 (&lt;&gt;) 運算子。</summary>
      <returns>如果 <paramref name="Left" /> 不等於 <paramref name="Right" /> 則為 True，否則為 False。</returns>
      <param name="Left">必要項。任何運算式。</param>
      <param name="Right">必要項。任何運算式。</param>
      <param name="TextCompare">必要項。True 表示要執行不區分大小寫的字串比較，否則為 False。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.CompareString(System.String,System.String,System.Boolean)">
      <summary>指定兩個字串時，執行二進位碼或文字字串比較。</summary>
      <returns>值條件-1<paramref name="Left" /> 小於 <paramref name="Right" />。0<paramref name="Left" /> 等於 <paramref name="Right" />。1<paramref name="Left" /> 大於 <paramref name="Right" />。</returns>
      <param name="Left">必要項。任何 String 運算式。</param>
      <param name="Right">必要項。任何 String 運算式。</param>
      <param name="TextCompare">必要項。True 表示要執行不區分大小寫的字串比較，否則為 False。</param>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.ConcatenateObject(System.Object,System.Object)">
      <summary>表示 Visual Basic 串連 (&amp;) 運算子。</summary>
      <returns>表示 <paramref name="Left" /> 和 <paramref name="Right" /> 串連的字串。</returns>
      <param name="Left">必要項。任何運算式。</param>
      <param name="Right">必要項。任何運算式。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.ConditionalCompareObjectEqual(System.Object,System.Object,System.Boolean)">
      <summary>表示多載 Visual Basic 等號 (=) 比較運算子。</summary>
      <returns>多載等號運算子的結果。如果不支援運算子多載，則為 False。</returns>
      <param name="Left">必要項。任何運算式。</param>
      <param name="Right">必要項。任何運算式。</param>
      <param name="TextCompare">必要項。True 表示要執行不區分大小寫的字串比較，否則為 False。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.ConditionalCompareObjectGreater(System.Object,System.Object,System.Boolean)">
      <summary>表示多載 Visual Basic 大於 (&gt;) 運算子。</summary>
      <returns>多載大於運算子的結果。如果不支援運算子多載，則為 False。</returns>
      <param name="Left">必要項。任何運算式。</param>
      <param name="Right">必要項。任何運算式。</param>
      <param name="TextCompare">必要項。True 表示要執行不區分大小寫的字串比較，否則為 False。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.ConditionalCompareObjectGreaterEqual(System.Object,System.Object,System.Boolean)">
      <summary>表示多載 Visual Basic 大於或等於 (&gt;=) 運算子。</summary>
      <returns>多載大於或等於運算子的結果。如果不支援運算子多載，則為 False。</returns>
      <param name="Left">必要項。任何運算式。</param>
      <param name="Right">必要項。任何運算式。</param>
      <param name="TextCompare">必要項。True 表示要執行不區分大小寫的字串比較，否則為 False。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.ConditionalCompareObjectLess(System.Object,System.Object,System.Boolean)">
      <summary>表示多載 Visual Basic 小於 (&lt;) 運算子。</summary>
      <returns>多載小於運算子的結果。如果不支援運算子多載，則為 False。</returns>
      <param name="Left">必要項。任何運算式。</param>
      <param name="Right">必要項。任何運算式。</param>
      <param name="TextCompare">必要項。True 表示要執行不區分大小寫的字串比較，否則為 False。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.ConditionalCompareObjectLessEqual(System.Object,System.Object,System.Boolean)">
      <summary>表示多載 Visual Basic 小於或等於 (&lt;=) 運算子。</summary>
      <returns>多載小於或等於運算子的結果。如果不支援運算子多載，則為 False。</returns>
      <param name="Left">必要項。任何運算式。</param>
      <param name="Right">必要項。任何運算式。</param>
      <param name="TextCompare">必要項。True 表示要執行不區分大小寫的字串比較，否則為 False。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.ConditionalCompareObjectNotEqual(System.Object,System.Object,System.Boolean)">
      <summary>表示多載 Visual Basic 不等 (&lt;&gt;) 運算子。</summary>
      <returns>多載不等於運算子的結果。如果不支援運算子多載，則為 False。</returns>
      <param name="Left">必要項。任何運算式。</param>
      <param name="Right">必要項。任何運算式。</param>
      <param name="TextCompare">必要項。True 表示要執行不區分大小寫的字串比較，否則為 False。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.DivideObject(System.Object,System.Object)">
      <summary>表示 Visual Basic 除法 (/) 運算子。</summary>
      <returns>
        <paramref name="Left" /> 除以 <paramref name="Right" /> 的完整商數，包括任何餘數。</returns>
      <param name="Left">必要項。任何數值運算式。</param>
      <param name="Right">必要項。任何數值運算式。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.ExponentObject(System.Object,System.Object)">
      <summary>表示 Visual Basic 指數 (^) 運算子。</summary>
      <returns>
        <paramref name="Left" /> 自乘至 <paramref name="Right" /> 之乘冪的結果。</returns>
      <param name="Left">必要項。任何數值運算式。</param>
      <param name="Right">必要項。任何數值運算式。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.IntDivideObject(System.Object,System.Object)">
      <summary>表示 Visual Basic 整數除法 (\) 運算子。</summary>
      <returns>
        <paramref name="Left" /> 除以 <paramref name="Right" /> 的整數商數，這會捨棄任何餘數而只保留整數部分。</returns>
      <param name="Left">必要項。任何數值運算式。</param>
      <param name="Right">必要項。任何數值運算式。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.LeftShiftObject(System.Object,System.Object)">
      <summary>表示 Visual Basic 算術左移位 (&lt;&lt;) 運算子。</summary>
      <returns>整數值。位元模式移位的結果。資料型別與 <paramref name="Operand" /> 的型別相同。</returns>
      <param name="Operand">必要項。整數值運算式。要移位的位元模式。資料型別必須是整數類資料型別 (Integral Type) (SByte、Byte、Short、UShort、Integer、UInteger、Long 或 ULong)。</param>
      <param name="Amount">必要項。數值運算式。位元模式移位的位元數。資料型別必須是 Integer 或擴展至 Integer。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.ModObject(System.Object,System.Object)">
      <summary>表示 Visual Basic Mod 運算子。</summary>
      <returns>
        <paramref name="Left" /> 除以 <paramref name="Right" /> 後所留的餘數。</returns>
      <param name="Left">必要項。任何數值運算式。</param>
      <param name="Right">必要項。任何數值運算式。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.MultiplyObject(System.Object,System.Object)">
      <summary>表示 Visual Basic 乘法 (*) 運算子。</summary>
      <returns>
        <paramref name="Left" /> 與 <paramref name="Right" /> 的乘積。</returns>
      <param name="Left">必要項。任何數值運算式。</param>
      <param name="Right">必要項。任何數值運算式。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.NegateObject(System.Object)">
      <summary>表示 Visual Basic 一元減號 (–) 運算子。</summary>
      <returns>
        <paramref name="Operand" /> 的負值。</returns>
      <param name="Operand">必要項。任何數值運算式。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.NotObject(System.Object)">
      <summary>表示 Visual Basic Not 運算子。</summary>
      <returns>對於 Boolean 運算，如果 <paramref name="Operand" /> 為 True，則為 False，否則為 True。對於位元運算，如果 <paramref name="Operand" /> 為 0，則為 1，否則為 0。</returns>
      <param name="Operand">必要項。任何 Boolean 或數值運算式。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.OrObject(System.Object,System.Object)">
      <summary>表示 Visual Basic Or 運算子。</summary>
      <returns>對於 Boolean 運算，如果 <paramref name="Left" /> 和 <paramref name="Right" /> 都評估為 False，則為 False，否則為 True。對於位元運算，如果 <paramref name="Left" /> 和 <paramref name="Right" /> 都評估為 0，則為 0，否則為 1。</returns>
      <param name="Left">必要項。任何 Boolean 或數值運算式。</param>
      <param name="Right">必要項。任何 Boolean 或數值運算式。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.PlusObject(System.Object)">
      <summary>表示 Visual Basic 一元加號 (+) 運算子。</summary>
      <returns>
        <paramref name="Operand" /> 的值 (<paramref name="Operand" /> 的正負號不會變更)。</returns>
      <param name="Operand">必要項。任何數值運算式。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.RightShiftObject(System.Object,System.Object)">
      <summary>表示 Visual Basic 算術右移位 (&gt;&gt;) 運算子。</summary>
      <returns>整數值。位元模式移位的結果。資料型別與 <paramref name="Operand" /> 的型別相同。</returns>
      <param name="Operand">必要項。整數值運算式。要移位的位元模式。資料型別必須是整數類資料型別 (Integral Type) (SByte、Byte、Short、UShort、Integer、UInteger、Long 或 ULong)。</param>
      <param name="Amount">必要項。數值運算式。位元模式移位的位元數。資料型別必須是 Integer 或擴展至 Integer。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.SubtractObject(System.Object,System.Object)">
      <summary>表示 Visual Basic 減法 (–) 運算子。</summary>
      <returns>
        <paramref name="Left" /> 與 <paramref name="Right" /> 之間的差數。</returns>
      <param name="Left">必要項。任何數值運算式。</param>
      <param name="Right">必要項。任何數值運算式。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.XorObject(System.Object,System.Object)">
      <summary>表示 Visual Basic Xor 運算子。</summary>
      <returns>Boolean 或數值。如果是 Boolean 比較，傳回值為兩個 Boolean 值的邏輯互斥 (獨佔邏輯分離)。如果是位元 (數值) 運算，傳回值為數值，表示兩個數值位元模式的位元互斥 (獨佔位元分離)。如需詳細資訊，請參閱Xor 運算子 (Visual Basic)。</returns>
      <param name="Left">必要項。任何 Boolean 或數值運算式。</param>
      <param name="Right">必要項。任何 Boolean 或數值運算式。</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="T:Microsoft.VisualBasic.CompilerServices.OptionCompareAttribute">
      <summary>指定應傳遞目前的 Option Compare 設定，做為引數的預設值。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.OptionCompareAttribute.#ctor">
      <summary>初始化 <see cref="T:Microsoft.VisualBasic.CompilerServices.OptionCompareAttribute" /> 類別的新執行個體。</summary>
    </member>
    <member name="T:Microsoft.VisualBasic.CompilerServices.OptionTextAttribute">
      <summary>Visual Basic 編譯器會發出這個 Helper 類別，以指出 (Visual Basic 偵錯之用) 哪個比較選項 (二進位碼或文字) 正在使用中。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.OptionTextAttribute.#ctor">
      <summary>初始化 <see cref="T:Microsoft.VisualBasic.CompilerServices.OptionTextAttribute" /> 類別的新執行個體。這是 Helper 方法。</summary>
    </member>
    <member name="T:Microsoft.VisualBasic.CompilerServices.ProjectData">
      <summary>提供 Visual Basic Err 物件的 Helper。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.ProjectData.ClearProjectError">
      <summary>為 Err 物件的 Clear 方法執行工作。Helper 方法。</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.ProjectData.SetProjectError(System.Exception)">
      <summary>Visual Basic 編譯器會使用這個 Helper 方法來擷取 Err 物件中的例外狀況。</summary>
      <param name="ex">要攔截的 <see cref="T:System.Exception" /> 物件。</param>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.ProjectData.SetProjectError(System.Exception,System.Int32)">
      <summary>Visual Basic 編譯器會使用這個 Helper 方法來擷取 Err 物件中的例外狀況。</summary>
      <param name="ex">要攔截的 <see cref="T:System.Exception" /> 物件。</param>
      <param name="lErl">例外狀況的行號。</param>
    </member>
    <member name="T:Microsoft.VisualBasic.CompilerServices.StandardModuleAttribute">
      <summary>這個類別提供屬性，當標準模組建構發出至中繼語言 (IL) 時，對標準模組建構套用這些屬性。它並不適合直接從您的程式碼呼叫。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.StandardModuleAttribute.#ctor">
      <summary>初始化 <see cref="T:Microsoft.VisualBasic.CompilerServices.StandardModuleAttribute" /> 類別的新執行個體。</summary>
    </member>
    <member name="T:Microsoft.VisualBasic.CompilerServices.StaticLocalInitFlag">
      <summary>Visual Basic 編譯器初始化靜態本機成員時，會在內部使用這個類別，並不適合從您的程式碼直接呼叫。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.StaticLocalInitFlag.#ctor">
      <summary>初始化 <see cref="T:Microsoft.VisualBasic.CompilerServices.StaticLocalInitFlag" /> 類別的新執行個體。</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.CompilerServices.StaticLocalInitFlag.State">
      <summary>傳回靜態本機成員的初始設定旗標狀態 (已初始化或未初始化)。</summary>
    </member>
    <member name="T:Microsoft.VisualBasic.CompilerServices.Utils">
      <summary>包含 Visual Basic 編譯器使用的公用程式。</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Utils.CopyArray(System.Array,System.Array)">
      <summary>由 Visual Basic 編譯器用做 Redim 的 Helper。</summary>
      <returns>複製的陣列。</returns>
      <param name="arySrc">要複製的陣列。</param>
      <param name="aryDest">目的陣列。</param>
    </member>
  </members>
</doc>