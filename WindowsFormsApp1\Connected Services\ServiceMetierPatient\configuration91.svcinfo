<?xml version="1.0" encoding="utf-8"?>
<SavedWcfConfigurationInformation xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" Version="9.1" CheckSum="YxSUk91KAG/4xw7xtftISyMLxRVkmM86JPd1PwAMXNk=">
  <bindingConfigurations>
    <bindingConfiguration bindingType="basicHttpBinding" name="BasicHttpBinding_IPatientService">
      <properties>
        <property path="/name" isComplexType="false" isExplicitlyDefined="true" clrType="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <serializedValue>BasicHttpBinding_IPatientService</serializedValue>
        </property>
        <property path="/closeTimeout" isComplexType="false" isExplicitlyDefined="true" clrType="System.TimeSpan, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <serializedValue />
        </property>
        <property path="/openTimeout" isComplexType="false" isExplicitlyDefined="true" clrType="System.TimeSpan, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <serializedValue />
        </property>
        <property path="/receiveTimeout" isComplexType="false" isExplicitlyDefined="true" clrType="System.TimeSpan, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <serializedValue />
        </property>
        <property path="/sendTimeout" isComplexType="false" isExplicitlyDefined="true" clrType="System.TimeSpan, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <serializedValue />
        </property>
        <property path="/allowCookies" isComplexType="false" isExplicitlyDefined="true" clrType="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <serializedValue />
        </property>
        <property path="/bypassProxyOnLocal" isComplexType="false" isExplicitlyDefined="true" clrType="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <serializedValue />
        </property>
        <property path="/hostNameComparisonMode" isComplexType="false" isExplicitlyDefined="false" clrType="System.ServiceModel.HostNameComparisonMode, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <serializedValue>StrongWildcard</serializedValue>
        </property>
        <property path="/maxBufferPoolSize" isComplexType="false" isExplicitlyDefined="true" clrType="System.Int64, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <serializedValue />
        </property>
        <property path="/maxBufferSize" isComplexType="false" isExplicitlyDefined="false" clrType="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <serializedValue>65536</serializedValue>
        </property>
        <property path="/maxReceivedMessageSize" isComplexType="false" isExplicitlyDefined="true" clrType="System.Int64, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <serializedValue />
        </property>
        <property path="/proxyAddress" isComplexType="false" isExplicitlyDefined="false" clrType="System.Uri, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <serializedValue />
        </property>
        <property path="/readerQuotas" isComplexType="true" isExplicitlyDefined="false" clrType="System.ServiceModel.Configuration.XmlDictionaryReaderQuotasElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <serializedValue>System.ServiceModel.Configuration.XmlDictionaryReaderQuotasElement</serializedValue>
        </property>
        <property path="/readerQuotas/maxDepth" isComplexType="false" isExplicitlyDefined="false" clrType="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <serializedValue>0</serializedValue>
        </property>
        <property path="/readerQuotas/maxStringContentLength" isComplexType="false" isExplicitlyDefined="false" clrType="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <serializedValue>0</serializedValue>
        </property>
        <property path="/readerQuotas/maxArrayLength" isComplexType="false" isExplicitlyDefined="false" clrType="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <serializedValue>0</serializedValue>
        </property>
        <property path="/readerQuotas/maxBytesPerRead" isComplexType="false" isExplicitlyDefined="false" clrType="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <serializedValue>0</serializedValue>
        </property>
        <property path="/readerQuotas/maxNameTableCharCount" isComplexType="false" isExplicitlyDefined="false" clrType="System.Int32, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <serializedValue>0</serializedValue>
        </property>
        <property path="/textEncoding" isComplexType="false" isExplicitlyDefined="false" clrType="System.Text.Encoding, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <serializedValue>System.Text.UTF8Encoding</serializedValue>
        </property>
        <property path="/transferMode" isComplexType="false" isExplicitlyDefined="false" clrType="System.ServiceModel.TransferMode, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <serializedValue>Buffered</serializedValue>
        </property>
        <property path="/useDefaultWebProxy" isComplexType="false" isExplicitlyDefined="true" clrType="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <serializedValue />
        </property>
        <property path="/messageEncoding" isComplexType="false" isExplicitlyDefined="false" clrType="System.ServiceModel.WSMessageEncoding, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <serializedValue>Text</serializedValue>
        </property>
        <property path="/security" isComplexType="true" isExplicitlyDefined="false" clrType="System.ServiceModel.Configuration.BasicHttpSecurityElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <serializedValue>System.ServiceModel.Configuration.BasicHttpSecurityElement</serializedValue>
        </property>
        <property path="/security/mode" isComplexType="false" isExplicitlyDefined="false" clrType="System.ServiceModel.BasicHttpSecurityMode, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <serializedValue>None</serializedValue>
        </property>
        <property path="/security/transport" isComplexType="true" isExplicitlyDefined="false" clrType="System.ServiceModel.Configuration.HttpTransportSecurityElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <serializedValue>System.ServiceModel.Configuration.HttpTransportSecurityElement</serializedValue>
        </property>
        <property path="/security/transport/clientCredentialType" isComplexType="false" isExplicitlyDefined="false" clrType="System.ServiceModel.HttpClientCredentialType, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <serializedValue>None</serializedValue>
        </property>
        <property path="/security/transport/proxyCredentialType" isComplexType="false" isExplicitlyDefined="false" clrType="System.ServiceModel.HttpProxyCredentialType, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <serializedValue>None</serializedValue>
        </property>
        <property path="/security/transport/extendedProtectionPolicy" isComplexType="true" isExplicitlyDefined="false" clrType="System.Security.Authentication.ExtendedProtection.Configuration.ExtendedProtectionPolicyElement, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <serializedValue>System.Security.Authentication.ExtendedProtection.Configuration.ExtendedProtectionPolicyElement</serializedValue>
        </property>
        <property path="/security/transport/extendedProtectionPolicy/policyEnforcement" isComplexType="false" isExplicitlyDefined="false" clrType="System.Security.Authentication.ExtendedProtection.PolicyEnforcement, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <serializedValue>Never</serializedValue>
        </property>
        <property path="/security/transport/extendedProtectionPolicy/protectionScenario" isComplexType="false" isExplicitlyDefined="false" clrType="System.Security.Authentication.ExtendedProtection.ProtectionScenario, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <serializedValue>TransportSelected</serializedValue>
        </property>
        <property path="/security/transport/extendedProtectionPolicy/customServiceNames" isComplexType="true" isExplicitlyDefined="false" clrType="System.Security.Authentication.ExtendedProtection.Configuration.ServiceNameElementCollection, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <serializedValue>(Collection)</serializedValue>
        </property>
        <property path="/security/transport/realm" isComplexType="false" isExplicitlyDefined="false" clrType="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <serializedValue />
        </property>
        <property path="/security/message" isComplexType="true" isExplicitlyDefined="false" clrType="System.ServiceModel.Configuration.BasicHttpMessageSecurityElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <serializedValue>System.ServiceModel.Configuration.BasicHttpMessageSecurityElement</serializedValue>
        </property>
        <property path="/security/message/clientCredentialType" isComplexType="false" isExplicitlyDefined="false" clrType="System.ServiceModel.BasicHttpMessageCredentialType, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <serializedValue>UserName</serializedValue>
        </property>
        <property path="/security/message/algorithmSuite" isComplexType="false" isExplicitlyDefined="false" clrType="System.ServiceModel.Security.SecurityAlgorithmSuite, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <serializedValue>Default</serializedValue>
        </property>
      </properties>
    </bindingConfiguration>
  </bindingConfigurations>
  <endpoints>
    <endpoint name="BasicHttpBinding_IPatientService" contract="ServiceMetierPatient.IPatientService" bindingType="basicHttpBinding" address="http://localhost:60827/Wcf/PatientService.svc" bindingConfiguration="BasicHttpBinding_IPatientService">
      <properties>
        <property path="/address" isComplexType="false" isExplicitlyDefined="true" clrType="System.Uri, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <serializedValue>http://localhost:60827/Wcf/PatientService.svc</serializedValue>
        </property>
        <property path="/behaviorConfiguration" isComplexType="false" isExplicitlyDefined="false" clrType="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <serializedValue />
        </property>
        <property path="/binding" isComplexType="false" isExplicitlyDefined="true" clrType="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <serializedValue>basicHttpBinding</serializedValue>
        </property>
        <property path="/bindingConfiguration" isComplexType="false" isExplicitlyDefined="true" clrType="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <serializedValue>BasicHttpBinding_IPatientService</serializedValue>
        </property>
        <property path="/contract" isComplexType="false" isExplicitlyDefined="true" clrType="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <serializedValue>ServiceMetierPatient.IPatientService</serializedValue>
        </property>
        <property path="/headers" isComplexType="true" isExplicitlyDefined="false" clrType="System.ServiceModel.Configuration.AddressHeaderCollectionElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <serializedValue>System.ServiceModel.Configuration.AddressHeaderCollectionElement</serializedValue>
        </property>
        <property path="/headers/headers" isComplexType="false" isExplicitlyDefined="true" clrType="System.ServiceModel.Channels.AddressHeaderCollection, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <serializedValue>&lt;Header /&gt;</serializedValue>
        </property>
        <property path="/identity" isComplexType="true" isExplicitlyDefined="false" clrType="System.ServiceModel.Configuration.IdentityElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <serializedValue>System.ServiceModel.Configuration.IdentityElement</serializedValue>
        </property>
        <property path="/identity/userPrincipalName" isComplexType="true" isExplicitlyDefined="false" clrType="System.ServiceModel.Configuration.UserPrincipalNameElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <serializedValue>System.ServiceModel.Configuration.UserPrincipalNameElement</serializedValue>
        </property>
        <property path="/identity/userPrincipalName/value" isComplexType="false" isExplicitlyDefined="false" clrType="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <serializedValue />
        </property>
        <property path="/identity/servicePrincipalName" isComplexType="true" isExplicitlyDefined="false" clrType="System.ServiceModel.Configuration.ServicePrincipalNameElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <serializedValue>System.ServiceModel.Configuration.ServicePrincipalNameElement</serializedValue>
        </property>
        <property path="/identity/servicePrincipalName/value" isComplexType="false" isExplicitlyDefined="false" clrType="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <serializedValue />
        </property>
        <property path="/identity/dns" isComplexType="true" isExplicitlyDefined="false" clrType="System.ServiceModel.Configuration.DnsElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <serializedValue>System.ServiceModel.Configuration.DnsElement</serializedValue>
        </property>
        <property path="/identity/dns/value" isComplexType="false" isExplicitlyDefined="false" clrType="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <serializedValue />
        </property>
        <property path="/identity/rsa" isComplexType="true" isExplicitlyDefined="false" clrType="System.ServiceModel.Configuration.RsaElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <serializedValue>System.ServiceModel.Configuration.RsaElement</serializedValue>
        </property>
        <property path="/identity/rsa/value" isComplexType="false" isExplicitlyDefined="false" clrType="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <serializedValue />
        </property>
        <property path="/identity/certificate" isComplexType="true" isExplicitlyDefined="false" clrType="System.ServiceModel.Configuration.CertificateElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <serializedValue>System.ServiceModel.Configuration.CertificateElement</serializedValue>
        </property>
        <property path="/identity/certificate/encodedValue" isComplexType="false" isExplicitlyDefined="false" clrType="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <serializedValue />
        </property>
        <property path="/identity/certificateReference" isComplexType="true" isExplicitlyDefined="false" clrType="System.ServiceModel.Configuration.CertificateReferenceElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <serializedValue>System.ServiceModel.Configuration.CertificateReferenceElement</serializedValue>
        </property>
        <property path="/identity/certificateReference/storeName" isComplexType="false" isExplicitlyDefined="false" clrType="System.Security.Cryptography.X509Certificates.StoreName, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <serializedValue>My</serializedValue>
        </property>
        <property path="/identity/certificateReference/storeLocation" isComplexType="false" isExplicitlyDefined="false" clrType="System.Security.Cryptography.X509Certificates.StoreLocation, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <serializedValue>LocalMachine</serializedValue>
        </property>
        <property path="/identity/certificateReference/x509FindType" isComplexType="false" isExplicitlyDefined="false" clrType="System.Security.Cryptography.X509Certificates.X509FindType, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <serializedValue>FindBySubjectDistinguishedName</serializedValue>
        </property>
        <property path="/identity/certificateReference/findValue" isComplexType="false" isExplicitlyDefined="false" clrType="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <serializedValue />
        </property>
        <property path="/identity/certificateReference/isChainIncluded" isComplexType="false" isExplicitlyDefined="false" clrType="System.Boolean, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <serializedValue>False</serializedValue>
        </property>
        <property path="/name" isComplexType="false" isExplicitlyDefined="true" clrType="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <serializedValue>BasicHttpBinding_IPatientService</serializedValue>
        </property>
        <property path="/kind" isComplexType="false" isExplicitlyDefined="false" clrType="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <serializedValue />
        </property>
        <property path="/endpointConfiguration" isComplexType="false" isExplicitlyDefined="false" clrType="System.String, mscorlib, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
          <serializedValue />
        </property>
      </properties>
    </endpoint>
  </endpoints>
</SavedWcfConfigurationInformation>