﻿<?xml version="1.0" encoding="utf-8"?>
<configurationSnapshot xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="urn:schemas-microsoft-com:xml-wcfconfigurationsnapshot">
  <behaviors />
  <bindings>
    <binding digest="System.ServiceModel.Configuration.BasicHttpBindingElement, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089:&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data name=&quot;BasicHttpBinding_IAgendaService&quot; /&gt;" bindingType="basicHttpBinding" name="BasicHttpBinding_IAgendaService" />
  </bindings>
  <endpoints>
    <endpoint normalizedDigest="&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data address=&quot;http://localhost:60827/Wcf/AgendaService.svc&quot; binding=&quot;basicHttpBinding&quot; bindingConfiguration=&quot;BasicHttpBinding_IAgendaService&quot; contract=&quot;ServiceMetierAgenda.IAgendaService&quot; name=&quot;BasicHttpBinding_IAgendaService&quot; /&gt;" digest="&lt;?xml version=&quot;1.0&quot; encoding=&quot;utf-16&quot;?&gt;&lt;Data address=&quot;http://localhost:60827/Wcf/AgendaService.svc&quot; binding=&quot;basicHttpBinding&quot; bindingConfiguration=&quot;BasicHttpBinding_IAgendaService&quot; contract=&quot;ServiceMetierAgenda.IAgendaService&quot; name=&quot;BasicHttpBinding_IAgendaService&quot; /&gt;" contractName="ServiceMetierAgenda.IAgendaService" name="BasicHttpBinding_IAgendaService" />
  </endpoints>
</configurationSnapshot>