﻿<?xml version="1.0" encoding="utf-8"?>
<doc>
  <assembly>
    <name>Microsoft.VisualBasic</name>
  </assembly>
  <members>
    <member name="T:Microsoft.VisualBasic.CallType">
      <summary>Indicates the type of procedure being invoked when calling the CallByName function.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:Microsoft.VisualBasic.CallType.Get">
      <summary>A property value is being retrieved.  This member is equivalent to the Visual Basic constant vbGet.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.CallType.Let">
      <summary>An Object property value is being determined. This member is equivalent to the Visual Basic constant vbLet.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.CallType.Method">
      <summary>A method is being invoked.  This member is equivalent to the Visual Basic constant vbMethod.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.CallType.Set">
      <summary>A property value is being determined.  This member is equivalent to the Visual Basic constant vbSet.</summary>
    </member>
    <member name="T:Microsoft.VisualBasic.Constants">
      <summary>The Constants module contains miscellaneous constants. These constants can be used anywhere in your code.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbBack">
      <summary>Represents a backspace character for print and display functions.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbCr">
      <summary>Represents a carriage-return character for print and display functions.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbCrLf">
      <summary>Represents a carriage-return character combined with a linefeed character for print and display functions.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbFormFeed">
      <summary>Represents a form-feed character for print functions.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbLf">
      <summary>Represents a linefeed character for print and display functions.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbNewLine">
      <summary>Represents a newline character for print and display functions.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbNullChar">
      <summary>Represents a null character for print and display functions.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbNullString">
      <summary>Represents a zero-length string for print and display functions, and for calling external procedures.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbTab">
      <summary>Represents a tab character for print and display functions.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="F:Microsoft.VisualBasic.Constants.vbVerticalTab">
      <summary>Represents a carriage-return character for print functions.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:Microsoft.VisualBasic.HideModuleNameAttribute">
      <summary>The HideModuleNameAttribute attribute, when applied to a module, allows the module members to be accessed using only the qualification needed for the module.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:Microsoft.VisualBasic.HideModuleNameAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.HideModuleNameAttribute" /> attribute. </summary>
    </member>
    <member name="T:Microsoft.VisualBasic.Strings">
      <summary>The Strings module contains procedures used to perform string operations. </summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:Microsoft.VisualBasic.Strings.AscW(System.Char)">
      <summary>Returns an Integer value representing the character code corresponding to a character.</summary>
      <returns>Returns an Integer value representing the character code corresponding to a character.</returns>
      <param name="String">Required. Any valid Char or String expression. If <paramref name="String" /> is a String expression, only the first character of the string is used for input. If <paramref name="String" /> is Nothing or contains no characters, an <see cref="T:System.ArgumentException" /> error occurs.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:Microsoft.VisualBasic.Strings.AscW(System.String)">
      <summary>Returns an Integer value representing the character code corresponding to a character.</summary>
      <returns>Returns an Integer value representing the character code corresponding to a character.</returns>
      <param name="String">Required. Any valid Char or String expression. If <paramref name="String" /> is a String expression, only the first character of the string is used for input. If <paramref name="String" /> is Nothing or contains no characters, an <see cref="T:System.ArgumentException" /> error occurs.</param>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:Microsoft.VisualBasic.Strings.ChrW(System.Int32)">
      <summary>Returns the character associated with the specified character code.</summary>
      <returns>Returns the character associated with the specified character code.</returns>
      <param name="CharCode">Required. An Integer expression representing the <paramref name="code point" />, or character code, for the character. </param>
      <exception cref="T:System.ArgumentException">
        <paramref name="CharCode" /> &lt; -32768 or &gt; 65535 for ChrW.</exception>
      <filterpriority>1</filterpriority>
    </member>
    <member name="T:Microsoft.VisualBasic.CompilerServices.Conversions">
      <summary>Provides methods that perform various type conversions.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ChangeType(System.Object,System.Type)">
      <summary>Converts an object to the specified type.</summary>
      <returns>An object of the specified target type.</returns>
      <param name="Expression">The object to convert.</param>
      <param name="TargetType">The type to which to convert the object.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToBoolean(System.Object)">
      <summary>Converts an object to a <see cref="T:System.Boolean" /> value.</summary>
      <returns>A Boolean value. Returns False if the object is null; otherwise, True.</returns>
      <param name="Value">The object to convert.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToBoolean(System.String)">
      <summary>Converts a string to a <see cref="T:System.Boolean" /> value.</summary>
      <returns>A Boolean value. Returns False if the string is null; otherwise, True.</returns>
      <param name="Value">The string to convert.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToByte(System.Object)">
      <summary>Converts an object to a <see cref="T:System.Byte" /> value.</summary>
      <returns>The Byte value of the object.</returns>
      <param name="Value">The object to convert.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToByte(System.String)">
      <summary>Converts a string to a <see cref="T:System.Byte" /> value.</summary>
      <returns>The Byte value of the string.</returns>
      <param name="Value">The string to convert.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToChar(System.Object)">
      <summary>Converts an object to a <see cref="T:System.Char" /> value.</summary>
      <returns>The Char value of the object.</returns>
      <param name="Value">The object to convert.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToChar(System.String)">
      <summary>Converts a string to a <see cref="T:System.Char" /> value.</summary>
      <returns>The Char value of the string.</returns>
      <param name="Value">The string to convert.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToCharArrayRankOne(System.Object)">
      <summary>Converts an object to a one-dimensional <see cref="T:System.Char" /> array.</summary>
      <returns>A one-dimensional Char array.</returns>
      <param name="Value">The object to convert.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToCharArrayRankOne(System.String)">
      <summary>Converts a string to a one-dimensional <see cref="T:System.Char" /> array.</summary>
      <returns>A one-dimensional Char array.</returns>
      <param name="Value">The string to convert.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToDate(System.Object)">
      <summary>Converts an object to a <see cref="T:System.DateTime" /> value.</summary>
      <returns>The DateTime value of the object.</returns>
      <param name="Value">The object to convert.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToDate(System.String)">
      <summary>Converts a string to a <see cref="T:System.DateTime" /> value.</summary>
      <returns>The DateTime value of the string.</returns>
      <param name="Value">The string to convert.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToDecimal(System.Boolean)">
      <summary>Converts a <see cref="T:System.Boolean" /> value to a <see cref="T:System.Decimal" /> value.</summary>
      <returns>The Decimal value of the Boolean value.</returns>
      <param name="Value">A Boolean value to convert.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToDecimal(System.Object)">
      <summary>Converts an object to a <see cref="T:System.Decimal" /> value.</summary>
      <returns>The Decimal value of the object.</returns>
      <param name="Value">The object to convert.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToDecimal(System.String)">
      <summary>Converts a string to a <see cref="T:System.Decimal" /> value.</summary>
      <returns>The Decimal value of the string.</returns>
      <param name="Value">The string to convert.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToDouble(System.Object)">
      <summary>Converts an object to a <see cref="T:System.Double" /> value.</summary>
      <returns>The Double value of the object.</returns>
      <param name="Value">The object to convert.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToDouble(System.String)">
      <summary>Converts a string to a <see cref="T:System.Double" /> value.</summary>
      <returns>The Double value of the string.</returns>
      <param name="Value">The string to convert.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToGenericParameter``1(System.Object)">
      <summary>Converts an object to a generic type <paramref name="T" />.</summary>
      <returns>A structure or object of generic type <paramref name="T" />.</returns>
      <param name="Value">The object to convert.</param>
      <typeparam name="T">The type to convert <paramref name="Value" /> to.</typeparam>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToInteger(System.Object)">
      <summary>Converts an object to an integer value.</summary>
      <returns>The int value of the object.</returns>
      <param name="Value">The object to convert.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToInteger(System.String)">
      <summary>Converts a string to an integer value.</summary>
      <returns>The int value of the string.</returns>
      <param name="Value">The string to convert.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToLong(System.Object)">
      <summary>Converts an object to a Long value.</summary>
      <returns>The Long value of the object.</returns>
      <param name="Value">The object to convert.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToLong(System.String)">
      <summary>Converts a string to a Long value.</summary>
      <returns>The Long value of the string.</returns>
      <param name="Value">The string to convert.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToSByte(System.Object)">
      <summary>Converts an object to an <see cref="T:System.SByte" /> value.</summary>
      <returns>The SByte value of the object.</returns>
      <param name="Value">The object to convert.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToSByte(System.String)">
      <summary>Converts a string to an <see cref="T:System.SByte" /> value.</summary>
      <returns>The SByte value of the string.</returns>
      <param name="Value">The string to convert.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToShort(System.Object)">
      <summary>Converts an object to a Short value.</summary>
      <returns>The Short value of the object.</returns>
      <param name="Value">The object to convert.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToShort(System.String)">
      <summary>Converts a string to a Short value.</summary>
      <returns>The Short value of the string.</returns>
      <param name="Value">The string to convert.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToSingle(System.Object)">
      <summary>Converts an object to a <see cref="T:System.Single" /> value.</summary>
      <returns>The Single value of the object.</returns>
      <param name="Value">The object to convert.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToSingle(System.String)">
      <summary>Converts a <see cref="T:System.String" /> to a <see cref="T:System.Single" /> value.</summary>
      <returns>The Single value of the string.</returns>
      <param name="Value">The string to convert.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToString(System.Boolean)">
      <summary>Converts a <see cref="T:System.Boolean" /> value to a <see cref="T:System.String" />.</summary>
      <returns>The String representation of the Boolean value.</returns>
      <param name="Value">The Boolean value to convert.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToString(System.Byte)">
      <summary>Converts a <see cref="T:System.Byte" /> value to a <see cref="T:System.String" />.</summary>
      <returns>The String representation of the Byte value.</returns>
      <param name="Value">The Byte value to convert.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToString(System.Char)">
      <summary>Converts a <see cref="T:System.Char" /> value to a <see cref="T:System.String" />.</summary>
      <returns>The String representation of the Char value.</returns>
      <param name="Value">The Char value to convert.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToString(System.DateTime)">
      <summary>Converts a <see cref="T:System.DateTime" /> value to a <see cref="T:System.String" /> value.</summary>
      <returns>The String representation of the DateTime value.</returns>
      <param name="Value">The DateTime value to convert.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToString(System.Decimal)">
      <summary>Converts a <see cref="T:System.Decimal" /> value to a <see cref="T:System.String" /> value.</summary>
      <returns>The String representation of the Decimal value.</returns>
      <param name="Value">The Decimal value to convert.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToString(System.Double)">
      <summary>Converts a <see cref="T:System.Double" /> value to a <see cref="T:System.String" /> value.</summary>
      <returns>The String representation of the Double value.</returns>
      <param name="Value">The Double value to convert.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToString(System.Int16)">
      <summary>Converts a Short value to a <see cref="T:System.String" /> value.</summary>
      <returns>The String representation of the Short value.</returns>
      <param name="Value">The Short value to convert.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToString(System.Int32)">
      <summary>Converts an integer value to a <see cref="T:System.String" /> value.</summary>
      <returns>The String representation of the int value.</returns>
      <param name="Value">The int value to convert.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToString(System.Int64)">
      <summary>Converts a Long value to a <see cref="T:System.String" /> value.</summary>
      <returns>The String representation of the Long value.</returns>
      <param name="Value">The Long value to convert.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToString(System.Object)">
      <summary>Converts an object to a <see cref="T:System.String" /> value.</summary>
      <returns>The String representation of the object.</returns>
      <param name="Value">The object to convert.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToString(System.Single)">
      <summary>Converts a <see cref="T:System.Single" /> value (a single-precision floating point number) to a <see cref="T:System.String" /> value.</summary>
      <returns>The String representation of the Single value.</returns>
      <param name="Value">The Single value to convert.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToString(System.UInt32)">
      <summary>Converts a uint value to a <see cref="T:System.String" /> value.</summary>
      <returns>The String representation of the Uint value.</returns>
      <param name="Value">The Uint value to convert.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToString(System.UInt64)">
      <summary>Converts a Ulong value to a <see cref="T:System.String" /> value.</summary>
      <returns>The String representation of the Ulong value.</returns>
      <param name="Value">The Ulong value to convert.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToUInteger(System.Object)">
      <summary>Converts an object to a Uint value.</summary>
      <returns>The Uint value of the object.</returns>
      <param name="Value">The object to convert.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToUInteger(System.String)">
      <summary>Converts a string to a Uint value.</summary>
      <returns>The Uint value of the string.</returns>
      <param name="Value">The string to convert.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToULong(System.Object)">
      <summary>Converts an object to a Ulong value.</summary>
      <returns>The Ulong value of the object.</returns>
      <param name="Value">The object to convert.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToULong(System.String)">
      <summary>Converts a string to a Ulong value.</summary>
      <returns>The Ulong value of the string.</returns>
      <param name="Value">The string to convert.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToUShort(System.Object)">
      <summary>Converts an object to a Ushort value.</summary>
      <returns>The Ushort value of the object.</returns>
      <param name="Value">The object to convert.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Conversions.ToUShort(System.String)">
      <summary>Converts a string to a Ushort value.</summary>
      <returns>The Ushort value of the string.</returns>
      <param name="Value">The string to convert.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="T:Microsoft.VisualBasic.CompilerServices.DesignerGeneratedAttribute">
      <summary>When applied to a class, the compiler implicitly calls a component-initializing method from the default synthetic constructor.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.DesignerGeneratedAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.CompilerServices.DesignerGeneratedAttribute" /> attribute.</summary>
    </member>
    <member name="T:Microsoft.VisualBasic.CompilerServices.IncompleteInitialization">
      <summary>The Visual Basic compiler uses this class during static local initialization; it is not meant to be called directly from your code. An exception of this type is thrown if a static local variable fails to initialize.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.IncompleteInitialization.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.CompilerServices.IncompleteInitialization" /> class.</summary>
    </member>
    <member name="T:Microsoft.VisualBasic.CompilerServices.NewLateBinding">
      <summary>This class provides helpers that the Visual Basic compiler uses for late binding calls; it is not meant to be called directly from your code.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.NewLateBinding.LateCall(System.Object,System.Type,System.String,System.Object[],System.String[],System.Type[],System.Boolean[],System.Boolean)">
      <summary>Executes a late-bound method or function call. This helper method is not meant to be called directly from your code.</summary>
      <returns>An instance of the call object.</returns>
      <param name="Instance">An instance of the call object exposing the property or method.</param>
      <param name="Type">The type of the call object.</param>
      <param name="MemberName">The name of the property or method on the call object.</param>
      <param name="Arguments">An array containing the arguments to be passed to the property or method being called.</param>
      <param name="ArgumentNames">An array of argument names.</param>
      <param name="TypeArguments">An array of argument types; used only for generic calls to pass argument types.</param>
      <param name="CopyBack">An array of Boolean values that the late binder uses to communicate back to the call site which arguments match ByRef parameters. Each True value indicates that the arguments matched and should be copied out after the call to LateCall is complete.</param>
      <param name="IgnoreReturn">A Boolean value indicating whether or not the return value can be ignored.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.NewLateBinding.LateGet(System.Object,System.Type,System.String,System.Object[],System.String[],System.Type[],System.Boolean[])">
      <summary>Executes a late-bound property get or field access call. This helper method is not meant to be called directly from your code.</summary>
      <returns>An instance of the call object.</returns>
      <param name="Instance">An instance of the call object exposing the property or method.</param>
      <param name="Type">The type of the call object.</param>
      <param name="MemberName">The name of the property or method on the call object.</param>
      <param name="Arguments">An array containing the arguments to be passed to the property or method being called.</param>
      <param name="ArgumentNames">An array of argument names.</param>
      <param name="TypeArguments">An array of argument types; used only for generic calls to pass argument types.</param>
      <param name="CopyBack">An array of Boolean values that the late binder uses to communicate back to the call site which arguments match ByRef parameters. Each True value indicates that the arguments matched and should be copied out after the call to LateCall is complete.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.NewLateBinding.LateIndexGet(System.Object,System.Object[],System.String[])">
      <summary>Executes a late-bound property get or field access call. This helper method is not meant to be called directly from your code.</summary>
      <returns>An instance of the call object.</returns>
      <param name="Instance">An instance of the call object exposing the property or method.</param>
      <param name="Arguments">An array containing the arguments to be passed to the property or method being called.</param>
      <param name="ArgumentNames">An array of argument names.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.NewLateBinding.LateIndexSet(System.Object,System.Object[],System.String[])">
      <summary>Executes a late-bound property set or field write call. This helper method is not meant to be called directly from your code.</summary>
      <param name="Instance">An instance of the call object exposing the property or method.</param>
      <param name="Arguments">An array containing the arguments to be passed to the property or method being called.</param>
      <param name="ArgumentNames">An array of argument names.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.NewLateBinding.LateIndexSetComplex(System.Object,System.Object[],System.String[],System.Boolean,System.Boolean)">
      <summary>Executes a late-bound property set or field write call. This helper method is not meant to be called directly from your code.</summary>
      <param name="Instance">An instance of the call object exposing the property or method.</param>
      <param name="Arguments">An array containing the arguments to be passed to the property or method being called.</param>
      <param name="ArgumentNames">An array of argument names.</param>
      <param name="OptimisticSet">A Boolean value used to determine whether the set operation will work. Set to True when you believe that an intermediate value has been set in the property or field; otherwise False.</param>
      <param name="RValueBase">A Boolean value that specifies when the base reference of the late reference is an RValue. Set to True when the base reference of the late reference is an RValue; this allows you to generate a run-time exception for late assignments to fields of RValues of value types. Otherwise, set to False.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.NewLateBinding.LateSet(System.Object,System.Type,System.String,System.Object[],System.String[],System.Type[])">
      <summary>Executes a late-bound property set or field write call. This helper method is not meant to be called directly from your code.</summary>
      <param name="Instance">An instance of the call object exposing the property or method.</param>
      <param name="Type">The type of the call object.</param>
      <param name="MemberName">The name of the property or method on the call object.</param>
      <param name="Arguments">An array containing the arguments to be passed to the property or method being called.</param>
      <param name="ArgumentNames">An array of argument names.</param>
      <param name="TypeArguments">An array of argument types; used only for generic calls to pass argument types.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.NewLateBinding.LateSet(System.Object,System.Type,System.String,System.Object[],System.String[],System.Type[],System.Boolean,System.Boolean,Microsoft.VisualBasic.CallType)">
      <summary>Executes a late-bound property set or field write call. This helper method is not meant to be called directly from your code.</summary>
      <param name="Instance">An instance of the call object exposing the property or method.</param>
      <param name="Type">The type of the call object.</param>
      <param name="MemberName">The name of the property or method on the call object.</param>
      <param name="Arguments">An array containing the arguments to be passed to the property or method being called.</param>
      <param name="ArgumentNames">An array of argument names.</param>
      <param name="TypeArguments">An array of argument types; used only for generic calls to pass argument types.</param>
      <param name="OptimisticSet">A Boolean value used to determine whether the set operation will work. Set to True when you believe that an intermediate value has been set in the property or field; otherwise False.</param>
      <param name="RValueBase">A Boolean value that specifies when the base reference of the late reference is an RValue. Set to True when the base reference of the late reference is an RValue; this allows you to generate a run-time exception for late assignments to fields of RValues of value types. Otherwise, set to False.</param>
      <param name="CallType">An enumeration member of type <see cref="T:Microsoft.VisualBasic.CallType" /> representing the type of procedure being called. The value of CallType can be Method, Get, or Set. Only Set is used.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.NewLateBinding.LateSetComplex(System.Object,System.Type,System.String,System.Object[],System.String[],System.Type[],System.Boolean,System.Boolean)">
      <summary>Executes a late-bound property set or field write call. This helper method is not meant to be called directly from your code.</summary>
      <param name="Instance">An instance of the call object exposing the property or method.</param>
      <param name="Type">The type of the call object.</param>
      <param name="MemberName">The name of the property or method on the call object.</param>
      <param name="Arguments">An array containing the arguments to be passed to the property or method being called.</param>
      <param name="ArgumentNames">An array of argument names.</param>
      <param name="TypeArguments">An array of argument types; used only for generic calls to pass argument types.</param>
      <param name="OptimisticSet">A Boolean value used to determine whether the set operation will work. Set to True when you believe that an intermediate value has been set in the property or field; otherwise False.</param>
      <param name="RValueBase">A Boolean value that specifies when the base reference of the late reference is an RValue. Set to True when the base reference of the late reference is an RValue; this allows you to generate a run-time exception for late assignments to fields of RValues of value types. Otherwise, set to False.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true" />
      </PermissionSet>
    </member>
    <member name="T:Microsoft.VisualBasic.CompilerServices.ObjectFlowControl">
      <summary>The Visual Basic compiler uses this class for object flow control; it is not meant to be called directly from your code.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.ObjectFlowControl.CheckForSyncLockOnValueType(System.Object)">
      <summary>Checks for a synchronization lock on the specified type.</summary>
      <param name="Expression">The data type for which to check for synchronization lock.</param>
    </member>
    <member name="T:Microsoft.VisualBasic.CompilerServices.ObjectFlowControl.ForLoopControl">
      <summary>Provides services to the Visual Basic compiler for compiling For...Next loops.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.ObjectFlowControl.ForLoopControl.ForLoopInitObj(System.Object,System.Object,System.Object,System.Object,System.Object@,System.Object@)">
      <summary>Initializes a For...Next loop.</summary>
      <returns>False if the loop has terminated; otherwise, True.</returns>
      <param name="Counter">The loop counter variable.</param>
      <param name="Start">The initial value of the loop counter.</param>
      <param name="Limit">The value of the To option.</param>
      <param name="StepValue">The value of the Step option.</param>
      <param name="LoopForResult">An object that contains verified values for loop values.</param>
      <param name="CounterResult">The counter value for the next loop iteration.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.ObjectFlowControl.ForLoopControl.ForNextCheckDec(System.Decimal,System.Decimal,System.Decimal)">
      <summary>Checks for valid values for the loop counter, Step, and To values.</summary>
      <returns>True if <paramref name="StepValue" /> is greater than zero and <paramref name="count" /> is less than or equal to <paramref name="limit" /> or <paramref name="StepValue" /> is less than or equal to zero and <paramref name="count" /> is greater than or equal to <paramref name="limit" />; otherwise, False.</returns>
      <param name="count">Required. A Decimal value that represents the initial value passed for the loop counter variable.</param>
      <param name="limit">Required. A Decimal value that represents the value passed by using the To keyword.</param>
      <param name="StepValue">Required. A Decimal value that represents the value passed by using the Step keyword.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.ObjectFlowControl.ForLoopControl.ForNextCheckObj(System.Object,System.Object,System.Object@)">
      <summary>Increments a For...Next loop.</summary>
      <returns>False if the loop has terminated; otherwise, True.</returns>
      <param name="Counter">The loop counter variable.</param>
      <param name="LoopObj">An object that contains verified values for loop values.</param>
      <param name="CounterResult">The counter value for the next loop iteration.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.ObjectFlowControl.ForLoopControl.ForNextCheckR4(System.Single,System.Single,System.Single)">
      <summary>Checks for valid values for the loop counter, Step, and To values.</summary>
      <returns>True if <paramref name="StepValue" /> is greater than zero and <paramref name="count" /> is less than or equal to <paramref name="limit" />, or if <paramref name="StepValue" /> is less than or equal to zero and <paramref name="count" /> is greater than or equal to <paramref name="limit" />; otherwise, False.</returns>
      <param name="count">Required. A Single value that represents the initial value passed for the loop counter variable.</param>
      <param name="limit">Required. A Single value that represents the value passed by using the To keyword.</param>
      <param name="StepValue">Required. A Single value that represents the value passed by using the Step keyword.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.ObjectFlowControl.ForLoopControl.ForNextCheckR8(System.Double,System.Double,System.Double)">
      <summary>Checks for valid values for the loop counter, Step, and To values.</summary>
      <returns>True if <paramref name="StepValue" /> is greater than zero and <paramref name="count" /> is less than or equal to <paramref name="limit" />, or if <paramref name="StepValue" /> is less than or equal to zero and <paramref name="count" /> is greater than or equal to <paramref name="limit" />; otherwise, False.</returns>
      <param name="count">Required. A Double value that represents the initial value passed for the loop counter variable.</param>
      <param name="limit">Required. A Double value that represents the value passed by using the To keyword.</param>
      <param name="StepValue">Required. A Double value that represents the value passed by using the Step keyword.</param>
    </member>
    <member name="T:Microsoft.VisualBasic.CompilerServices.Operators">
      <summary>Provides late-bound math operators, such as <see cref="M:Microsoft.VisualBasic.CompilerServices.Operators.AddObject(System.Object,System.Object)" /> and <see cref="M:Microsoft.VisualBasic.CompilerServices.Operators.CompareObject(System.Object,System.Object,System.Boolean)" />, which the Visual Basic compiler uses internally. </summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.AddObject(System.Object,System.Object)">
      <summary>Represents the Visual Basic addition (+) operator.</summary>
      <returns>The sum of <paramref name="Left" /> and <paramref name="Right" />.</returns>
      <param name="Left">Required. Any numeric expression.</param>
      <param name="Right">Required. Any numeric expression.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.AndObject(System.Object,System.Object)">
      <summary>Represents the Visual Basic And operator.</summary>
      <returns>For Boolean operations, True if both <paramref name="Left" /> and <paramref name="Right" /> evaluate to True; otherwise, False. For bitwise operations, 1 if both <paramref name="Left" /> and <paramref name="Right" /> evaluate to 1; otherwise, 0.</returns>
      <param name="Left">Required. Any Boolean or numeric expression.</param>
      <param name="Right">Required. Any Boolean or numeric expression.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.CompareObjectEqual(System.Object,System.Object,System.Boolean)">
      <summary>Represents the Visual Basic equal (=) operator.</summary>
      <returns>True if <paramref name="Left" /> and <paramref name="Right" /> are equal; otherwise, False.</returns>
      <param name="Left">Required. Any expression.</param>
      <param name="Right">Required. Any expression.</param>
      <param name="TextCompare">Required. True to perform a case-insensitive string comparison; otherwise, False.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.CompareObjectGreater(System.Object,System.Object,System.Boolean)">
      <summary>Represents the Visual Basic greater-than (&gt;) operator.</summary>
      <returns>True if <paramref name="Left" /> is greater than <paramref name="Right" />; otherwise, False.</returns>
      <param name="Left">Required. Any expression.</param>
      <param name="Right">Required. Any expression.</param>
      <param name="TextCompare">Required. True to perform a case-insensitive string comparison; otherwise, False.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.CompareObjectGreaterEqual(System.Object,System.Object,System.Boolean)">
      <summary>Represents the Visual Basic greater-than or equal-to (&gt;=) operator.</summary>
      <returns>True if <paramref name="Left" /> is greater than or equal to <paramref name="Right" />; otherwise, False.</returns>
      <param name="Left">Required. Any expression.</param>
      <param name="Right">Required. Any expression.</param>
      <param name="TextCompare">Required. True to perform a case-insensitive string comparison; otherwise, False.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.CompareObjectLess(System.Object,System.Object,System.Boolean)">
      <summary>Represents the Visual Basic less-than (&lt;) operator.</summary>
      <returns>True if <paramref name="Left" /> is less than <paramref name="Right" />; otherwise, False.</returns>
      <param name="Left">Required. Any expression.</param>
      <param name="Right">Required. Any expression.</param>
      <param name="TextCompare">Required. True to perform a case-insensitive string comparison; otherwise, False.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.CompareObjectLessEqual(System.Object,System.Object,System.Boolean)">
      <summary>Represents the Visual Basic less-than or equal-to (&lt;=) operator.</summary>
      <returns>True if <paramref name="Left" /> is less than or equal to <paramref name="Right" />; otherwise, False.</returns>
      <param name="Left">Required. Any expression.</param>
      <param name="Right">Required. Any expression.</param>
      <param name="TextCompare">Required. True to perform a case-insensitive string comparison; otherwise, False.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.CompareObjectNotEqual(System.Object,System.Object,System.Boolean)">
      <summary>Represents the Visual Basic not-equal (&lt;&gt;) operator.</summary>
      <returns>True if <paramref name="Left" /> is not equal to <paramref name="Right" />; otherwise, False.</returns>
      <param name="Left">Required. Any expression.</param>
      <param name="Right">Required. Any expression.</param>
      <param name="TextCompare">Required. True to perform a case-insensitive string comparison; otherwise, False.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.CompareString(System.String,System.String,System.Boolean)">
      <summary>Performs binary or text string comparison when given two strings.</summary>
      <returns>Value Condition -1 <paramref name="Left" /> is less than <paramref name="Right" />. 0<paramref name="Left" /> is equal to <paramref name="Right" />. 1 <paramref name="Left" /> is greater than <paramref name="Right" />. </returns>
      <param name="Left">Required. Any String expression.</param>
      <param name="Right">Required. Any String expression.</param>
      <param name="TextCompare">Required. True to perform a case-insensitive string comparison; otherwise, False.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.ConcatenateObject(System.Object,System.Object)">
      <summary>Represents the Visual Basic concatenation (&amp;) operator.</summary>
      <returns>A string representing the concatenation of <paramref name="Left" /> and <paramref name="Right" />.</returns>
      <param name="Left">Required. Any expression.</param>
      <param name="Right">Required. Any expression.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.ConditionalCompareObjectEqual(System.Object,System.Object,System.Boolean)">
      <summary>Represents the overloaded Visual Basic equals (=) operator.</summary>
      <returns>The result of the overloaded equals operator. False if operator overloading is not supported.</returns>
      <param name="Left">Required. Any expression.</param>
      <param name="Right">Required. Any expression.</param>
      <param name="TextCompare">Required. True to perform a case-insensitive string comparison; otherwise, False.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.ConditionalCompareObjectGreater(System.Object,System.Object,System.Boolean)">
      <summary>Represents the overloaded Visual Basic greater-than (&gt;) operator.</summary>
      <returns>The result of the overloaded greater-than operator. False if operator overloading is not supported.</returns>
      <param name="Left">Required. Any expression.</param>
      <param name="Right">Required. Any expression.</param>
      <param name="TextCompare">Required. True to perform a case-insensitive string comparison; otherwise, False.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.ConditionalCompareObjectGreaterEqual(System.Object,System.Object,System.Boolean)">
      <summary>Represents the overloaded Visual Basic greater-than or equal-to (&gt;=) operator.</summary>
      <returns>The result of the overloaded greater-than or equal-to operator. False if operator overloading is not supported.</returns>
      <param name="Left">Required. Any expression.</param>
      <param name="Right">Required. Any expression.</param>
      <param name="TextCompare">Required. True to perform a case-insensitive string comparison; otherwise, False.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.ConditionalCompareObjectLess(System.Object,System.Object,System.Boolean)">
      <summary>Represents the overloaded Visual Basic less-than (&lt;) operator.</summary>
      <returns>The result of the overloaded less-than operator. False if operator overloading is not supported.</returns>
      <param name="Left">Required. Any expression.</param>
      <param name="Right">Required. Any expression.</param>
      <param name="TextCompare">Required. True to perform a case-insensitive string comparison; otherwise, False.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.ConditionalCompareObjectLessEqual(System.Object,System.Object,System.Boolean)">
      <summary>Represents the overloaded Visual Basic less-than or equal-to (&lt;=) operator.</summary>
      <returns>The result of the overloaded less-than or equal-to operator. False if operator overloading is not supported.</returns>
      <param name="Left">Required. Any expression.</param>
      <param name="Right">Required. Any expression.</param>
      <param name="TextCompare">Required. True to perform a case-insensitive string comparison; otherwise, False.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.ConditionalCompareObjectNotEqual(System.Object,System.Object,System.Boolean)">
      <summary>Represents the overloaded Visual Basic not-equal (&lt;&gt;) operator.</summary>
      <returns>The result of the overloaded not-equal operator. False if operator overloading is not supported.</returns>
      <param name="Left">Required. Any expression.</param>
      <param name="Right">Required. Any expression.</param>
      <param name="TextCompare">Required. True to perform a case-insensitive string comparison; otherwise, False.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.DivideObject(System.Object,System.Object)">
      <summary>Represents the Visual Basic division (/) operator.</summary>
      <returns>The full quotient of <paramref name="Left" /> divided by <paramref name="Right" />, including any remainder.</returns>
      <param name="Left">Required. Any numeric expression.</param>
      <param name="Right">Required. Any numeric expression.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.ExponentObject(System.Object,System.Object)">
      <summary>Represents the Visual Basic exponent (^) operator.</summary>
      <returns>The result of <paramref name="Left" /> raised to the power of <paramref name="Right" />.</returns>
      <param name="Left">Required. Any numeric expression.</param>
      <param name="Right">Required. Any numeric expression.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.IntDivideObject(System.Object,System.Object)">
      <summary>Represents the Visual Basic integer division (\) operator.</summary>
      <returns>The integer quotient of <paramref name="Left" /> divided by <paramref name="Right" />, which discards any remainder and retains only the integer portion.</returns>
      <param name="Left">Required. Any numeric expression.</param>
      <param name="Right">Required. Any numeric expression.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.LeftShiftObject(System.Object,System.Object)">
      <summary>Represents the Visual Basic arithmetic left shift (&lt;&lt;) operator.</summary>
      <returns>An integral numeric value. The result of shifting the bit pattern. The data type is the same as that of <paramref name="Operand" />.</returns>
      <param name="Operand">Required. Integral numeric expression. The bit pattern to be shifted. The data type must be an integral type (SByte, Byte, Short, UShort, Integer, UInteger, Long, or ULong).</param>
      <param name="Amount">Required. Numeric expression. The number of bits to shift the bit pattern. The data type must be Integer or widen to Integer.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.ModObject(System.Object,System.Object)">
      <summary>Represents the Visual Basic Mod operator.</summary>
      <returns>The remainder after <paramref name="Left" /> is divided by <paramref name="Right" />. </returns>
      <param name="Left">Required. Any numeric expression.</param>
      <param name="Right">Required. Any numeric expression.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.MultiplyObject(System.Object,System.Object)">
      <summary>Represents the Visual Basic multiply (*) operator.</summary>
      <returns>The product of <paramref name="Left" /> and <paramref name="Right" />.</returns>
      <param name="Left">Required. Any numeric expression.</param>
      <param name="Right">Required. Any numeric expression.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.NegateObject(System.Object)">
      <summary>Represents the Visual Basic unary minus (–) operator.</summary>
      <returns>The negative value of <paramref name="Operand" />.</returns>
      <param name="Operand">Required. Any numeric expression.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.NotObject(System.Object)">
      <summary>Represents the Visual Basic Not operator.</summary>
      <returns>For Boolean operations, False if <paramref name="Operand" /> is True; otherwise, True. For bitwise operations, 1 if <paramref name="Operand" /> is 0; otherwise, 0.</returns>
      <param name="Operand">Required. Any Boolean or numeric expression.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.OrObject(System.Object,System.Object)">
      <summary>Represents the Visual Basic Or operator.</summary>
      <returns>For Boolean operations, False if both <paramref name="Left" /> and <paramref name="Right" /> evaluate to False; otherwise, True. For bitwise operations, 0 if both <paramref name="Left" /> and <paramref name="Right" /> evaluate to 0; otherwise, 1.</returns>
      <param name="Left">Required. Any Boolean or numeric expression.</param>
      <param name="Right">Required. Any Boolean or numeric expression.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.PlusObject(System.Object)">
      <summary>Represents the Visual Basic unary plus (+) operator.</summary>
      <returns>The value of <paramref name="Operand" />. (The sign of the <paramref name="Operand" /> is unchanged.)</returns>
      <param name="Operand">Required. Any numeric expression.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.RightShiftObject(System.Object,System.Object)">
      <summary>Represents the Visual Basic arithmetic right shift (&gt;&gt;) operator.</summary>
      <returns>An integral numeric value. The result of shifting the bit pattern. The data type is the same as that of <paramref name="Operand" />.</returns>
      <param name="Operand">Required. Integral numeric expression. The bit pattern to be shifted. The data type must be an integral type (SByte, Byte, Short, UShort, Integer, UInteger, Long, or ULong).</param>
      <param name="Amount">Required. Numeric expression. The number of bits to shift the bit pattern. The data type must be Integer or widen to Integer.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.SubtractObject(System.Object,System.Object)">
      <summary>Represents the Visual Basic subtraction (–) operator.</summary>
      <returns>The difference between <paramref name="Left" /> and <paramref name="Right" />.</returns>
      <param name="Left">Required. Any numeric expression. </param>
      <param name="Right">Required. Any numeric expression.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Operators.XorObject(System.Object,System.Object)">
      <summary>Represents the Visual Basic Xor operator.</summary>
      <returns>A Boolean or numeric value. For a Boolean comparison, the return value is the logical exclusion (exclusive logical disjunction) of two Boolean values. For bitwise (numeric) operations, the return value is a numeric value that represents the bitwise exclusion (exclusive bitwise disjunction) of two numeric bit patterns. For more information, see Xor Operator (Visual Basic).</returns>
      <param name="Left">Required. Any Boolean or numeric expression.</param>
      <param name="Right">Required. Any Boolean or numeric expression.</param>
      <PermissionSet>
        <IPermission class="System.Security.Permissions.SecurityPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Flags="UnmanagedCode" />
      </PermissionSet>
    </member>
    <member name="T:Microsoft.VisualBasic.CompilerServices.OptionCompareAttribute">
      <summary>Specifies that the current Option Compare setting should be passed as the default value for an argument. </summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.OptionCompareAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.CompilerServices.OptionCompareAttribute" /> class.</summary>
    </member>
    <member name="T:Microsoft.VisualBasic.CompilerServices.OptionTextAttribute">
      <summary>The Visual Basic compiler emits this helper class to indicate (for Visual Basic debugging) which comparison option, binary or text, is being used</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.OptionTextAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.CompilerServices.OptionTextAttribute" /> class. This is a helper method.</summary>
    </member>
    <member name="T:Microsoft.VisualBasic.CompilerServices.ProjectData">
      <summary>Provides helpers for the Visual Basic Err object. </summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.ProjectData.ClearProjectError">
      <summary>Performs the work for the Clear method of the Err object. A helper method.</summary>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.ProjectData.SetProjectError(System.Exception)">
      <summary>The Visual Basic compiler uses this helper method to capture exceptions in the Err object.</summary>
      <param name="ex">The <see cref="T:System.Exception" /> object to be caught.</param>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.ProjectData.SetProjectError(System.Exception,System.Int32)">
      <summary>The Visual Basic compiler uses this helper method to capture exceptions in the Err object.</summary>
      <param name="ex">The <see cref="T:System.Exception" /> object to be caught.</param>
      <param name="lErl">The line number of the exception.</param>
    </member>
    <member name="T:Microsoft.VisualBasic.CompilerServices.StandardModuleAttribute">
      <summary>This class provides attributes that are applied to the standard module construct when it is emitted to Intermediate Language (IL). It is not intended to be called directly from your code.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.StandardModuleAttribute.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.CompilerServices.StandardModuleAttribute" /> class.</summary>
    </member>
    <member name="T:Microsoft.VisualBasic.CompilerServices.StaticLocalInitFlag">
      <summary>The Visual Basic compiler uses this class internally when initializing static local members; it is not meant to be called directly from your code.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.StaticLocalInitFlag.#ctor">
      <summary>Initializes a new instance of the <see cref="T:Microsoft.VisualBasic.CompilerServices.StaticLocalInitFlag" /> class.</summary>
    </member>
    <member name="F:Microsoft.VisualBasic.CompilerServices.StaticLocalInitFlag.State">
      <summary>Returns the state of the static local member's initialization flag (initialized or not).</summary>
    </member>
    <member name="T:Microsoft.VisualBasic.CompilerServices.Utils">
      <summary>Contains utilities that the Visual Basic compiler uses.</summary>
      <filterpriority>1</filterpriority>
    </member>
    <member name="M:Microsoft.VisualBasic.CompilerServices.Utils.CopyArray(System.Array,System.Array)">
      <summary>Used by the Visual Basic compiler as a helper for Redim.</summary>
      <returns>The copied array.</returns>
      <param name="arySrc">The array to be copied.</param>
      <param name="aryDest">The destination array.</param>
    </member>
  </members>
</doc>