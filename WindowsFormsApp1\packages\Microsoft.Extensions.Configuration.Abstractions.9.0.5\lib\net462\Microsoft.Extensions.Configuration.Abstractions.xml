<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Microsoft.Extensions.Configuration.Abstractions</name>
    </assembly>
    <members>
        <member name="T:Microsoft.Extensions.Configuration.ConfigurationDebugViewContext">
            <summary>
            Provides data about the current item of the configuration.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.ConfigurationDebugViewContext.#ctor(System.String,System.String,System.String,Microsoft.Extensions.Configuration.IConfigurationProvider)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.Extensions.Configuration.ConfigurationDebugViewContext"/>.
            </summary>
            <param name="path">The path of the current item of the configuration.</param>
            <param name="key">The key of the current item of the configuration.</param>
            <param name="value">The value of the current item of the configuration.</param>
            <param name="configurationProvider">The <see cref="T:Microsoft.Extensions.Configuration.IConfigurationProvider" /> to use to get the value of the current item.</param>
        </member>
        <member name="P:Microsoft.Extensions.Configuration.ConfigurationDebugViewContext.Path">
            <summary>
            Gets the path of the current item.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Configuration.ConfigurationDebugViewContext.Key">
            <summary>
            Gets the key of the current item.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Configuration.ConfigurationDebugViewContext.Value">
            <summary>
            Gets the value of the current item.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Configuration.ConfigurationDebugViewContext.ConfigurationProvider">
            <summary>
            Gets the <see cref="T:Microsoft.Extensions.Configuration.IConfigurationProvider" /> that was used to get the value of the current item.
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.Configuration.ConfigurationExtensions">
            <summary>
            Provides extension methods for configuration classes.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.ConfigurationExtensions.Add``1(Microsoft.Extensions.Configuration.IConfigurationBuilder,System.Action{``0})">
            <summary>
            Adds a new configuration source.
            </summary>
            <param name="builder">The builder to add to.</param>
            <param name="configureSource">Configures the source secrets.</param>
            <returns>The <see cref="T:Microsoft.Extensions.Configuration.IConfigurationBuilder"/>.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.ConfigurationExtensions.GetConnectionString(Microsoft.Extensions.Configuration.IConfiguration,System.String)">
            <summary>
            Gets the specified connection string from the specified configuration.
            Shorthand for <c>GetSection("ConnectionStrings")[name]</c>.
            </summary>
            <param name="configuration">The configuration to enumerate.</param>
            <param name="name">The connection string key.</param>
            <returns>The connection string.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.ConfigurationExtensions.AsEnumerable(Microsoft.Extensions.Configuration.IConfiguration)">
            <summary>
            Get the enumeration of key value pairs within the <see cref="T:Microsoft.Extensions.Configuration.IConfiguration" />
            </summary>
            <param name="configuration">The configuration to enumerate.</param>
            <returns>An enumeration of key value pairs.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.ConfigurationExtensions.AsEnumerable(Microsoft.Extensions.Configuration.IConfiguration,System.Boolean)">
            <summary>
            Get the enumeration of key value pairs within the <see cref="T:Microsoft.Extensions.Configuration.IConfiguration" />
            </summary>
            <param name="configuration">The configuration to enumerate.</param>
            <param name="makePathsRelative"><see langword="true" /> to trim the current configuration's path from the front of the returned child keys.</param>
            <returns>An enumeration of key value pairs.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.ConfigurationExtensions.Exists(Microsoft.Extensions.Configuration.IConfigurationSection)">
            <summary>
            Determines whether the section has a <see cref="P:Microsoft.Extensions.Configuration.IConfigurationSection.Value"/> or has children.
            </summary>
            <param name="section">The section to enumerate.</param>
            <returns><see langword="true" /> if the section has values or children; otherwise, <see langword="false" />.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.ConfigurationExtensions.GetRequiredSection(Microsoft.Extensions.Configuration.IConfiguration,System.String)">
            <summary>
            Gets a configuration subsection with the specified key.
            </summary>
            <param name="configuration">The configuration to enumerate.</param>
            <param name="key">The key of the configuration section.</param>
            <returns>The <see cref="T:Microsoft.Extensions.Configuration.IConfigurationSection"/>.</returns>
            <remarks>
                If no matching sub-section is found with the specified key, an exception is raised.
            </remarks>
            <exception cref="T:System.InvalidOperationException">There is no section with key <paramref name="key"/>.</exception>
        </member>
        <member name="T:Microsoft.Extensions.Configuration.ConfigurationKeyNameAttribute">
            <summary>
            Specifies the key name for a configuration property.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.ConfigurationKeyNameAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of <see cref="T:Microsoft.Extensions.Configuration.ConfigurationKeyNameAttribute"/>.
            </summary>
            <param name="name">The key name.</param>
        </member>
        <member name="P:Microsoft.Extensions.Configuration.ConfigurationKeyNameAttribute.Name">
            <summary>
            Gets the key name for a configuration property.
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.Configuration.ConfigurationPath">
            <summary>
            Provides utility methods and constants for manipulating Configuration paths.
            </summary>
        </member>
        <member name="F:Microsoft.Extensions.Configuration.ConfigurationPath.KeyDelimiter">
            <summary>
            The delimiter ":" used to separate individual keys in a path.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.ConfigurationPath.Combine(System.String[])">
            <summary>
            Combines path segments into one path.
            </summary>
            <param name="pathSegments">The path segments to combine.</param>
            <returns>The combined path.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.ConfigurationPath.Combine(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Combines path segments into one path.
            </summary>
            <param name="pathSegments">The path segments to combine.</param>
            <returns>The combined path.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.ConfigurationPath.GetSectionKey(System.String)">
            <summary>
            Extracts the last path segment from the path.
            </summary>
            <param name="path">The path.</param>
            <returns>The last path segment of the path.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.ConfigurationPath.GetParentPath(System.String)">
            <summary>
            Extracts the path corresponding to the parent node for a given path.
            </summary>
            <param name="path">The path.</param>
            <returns>The original path minus the last individual segment found in it. Null if the original path corresponds to a top level node.</returns>
        </member>
        <member name="T:Microsoft.Extensions.Configuration.ConfigurationRootExtensions">
            <summary>
            Provides extension methods for <see cref="T:Microsoft.Extensions.Configuration.IConfigurationRoot"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.ConfigurationRootExtensions.GetDebugView(Microsoft.Extensions.Configuration.IConfigurationRoot)">
            <summary>
            Generates a human-readable view of the configuration showing where each value came from.
            </summary>
            <returns>The debug view.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.ConfigurationRootExtensions.GetDebugView(Microsoft.Extensions.Configuration.IConfigurationRoot,System.Func{Microsoft.Extensions.Configuration.ConfigurationDebugViewContext,System.String})">
            <summary>
            Generates a human-readable view of the configuration showing where each value came from.
            </summary>
            <param name="root">The configuration root.</param>
            <param name="processValue">
            The function for processing the value, for example, hiding secrets.
            Parameters:
              ConfigurationDebugViewContext: Context of the current configuration item.
              returns: A string value is used to assign as the Value of the configuration section.
            </param>
            <returns>The debug view.</returns>
        </member>
        <member name="T:Microsoft.Extensions.Configuration.IConfiguration">
            <summary>
            Represents a set of key/value application configuration properties.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Configuration.IConfiguration.Item(System.String)">
            <summary>
            Gets or sets a configuration value.
            </summary>
            <param name="key">The configuration key.</param>
            <returns>The configuration value.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.IConfiguration.GetSection(System.String)">
            <summary>
            Gets a configuration sub-section with the specified key.
            </summary>
            <param name="key">The key of the configuration section.</param>
            <returns>The <see cref="T:Microsoft.Extensions.Configuration.IConfigurationSection"/>.</returns>
            <remarks>
                This method will never return <c>null</c>. If no matching sub-section is found with the specified key,
                an empty <see cref="T:Microsoft.Extensions.Configuration.IConfigurationSection"/> will be returned.
            </remarks>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.IConfiguration.GetChildren">
            <summary>
            Gets the immediate descendant configuration sub-sections.
            </summary>
            <returns>The configuration sub-sections.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.IConfiguration.GetReloadToken">
            <summary>
            Returns a <see cref="T:Microsoft.Extensions.Primitives.IChangeToken"/> that can be used to observe when this configuration is reloaded.
            </summary>
            <returns>A <see cref="T:Microsoft.Extensions.Primitives.IChangeToken"/>.</returns>
        </member>
        <member name="T:Microsoft.Extensions.Configuration.IConfigurationBuilder">
            <summary>
            Represents a type used to build application configuration.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Configuration.IConfigurationBuilder.Properties">
            <summary>
            Gets a key/value collection that can be used to share data between the <see cref="T:Microsoft.Extensions.Configuration.IConfigurationBuilder"/>
            and the registered <see cref="T:Microsoft.Extensions.Configuration.IConfigurationSource"/>s.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Configuration.IConfigurationBuilder.Sources">
            <summary>
            Gets the sources used to obtain configuration values
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.IConfigurationBuilder.Add(Microsoft.Extensions.Configuration.IConfigurationSource)">
            <summary>
            Adds a new configuration source.
            </summary>
            <param name="source">The configuration source to add.</param>
            <returns>The same <see cref="T:Microsoft.Extensions.Configuration.IConfigurationBuilder"/>.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.IConfigurationBuilder.Build">
            <summary>
            Builds an <see cref="T:Microsoft.Extensions.Configuration.IConfiguration"/> with keys and values from the set of sources registered in
            <see cref="P:Microsoft.Extensions.Configuration.IConfigurationBuilder.Sources"/>.
            </summary>
            <returns>An <see cref="T:Microsoft.Extensions.Configuration.IConfigurationRoot"/> with keys and values from the registered sources.</returns>
        </member>
        <member name="T:Microsoft.Extensions.Configuration.IConfigurationManager">
            <summary>
            Represents a mutable configuration object.
            </summary>
            <remarks>
            It is both an <see cref="T:Microsoft.Extensions.Configuration.IConfigurationBuilder"/> and an <see cref="T:Microsoft.Extensions.Configuration.IConfiguration"/>.
            As sources are added, it updates its current view of configuration.
            </remarks>
        </member>
        <member name="T:Microsoft.Extensions.Configuration.IConfigurationProvider">
            <summary>
            Provides configuration key/values for an application.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.IConfigurationProvider.TryGet(System.String,System.String@)">
            <summary>
            Tries to get a configuration value for the specified key.
            </summary>
            <param name="key">The key.</param>
            <param name="value">When this method returns, contains the value for the specified key.</param>
            <returns><see langword="true" /> if a value for the specified key was found, otherwise <see langword="false" />.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.IConfigurationProvider.Set(System.String,System.String)">
            <summary>
            Sets a configuration value for the specified key.
            </summary>
            <param name="key">The key.</param>
            <param name="value">The value.</param>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.IConfigurationProvider.GetReloadToken">
            <summary>
            Attempts to get an <see cref="T:Microsoft.Extensions.Primitives.IChangeToken"/> for change tracking.
            </summary>
            <returns>An <see cref="T:Microsoft.Extensions.Primitives.IChangeToken"/> token if this provider supports change tracking, <see langword="null"/> otherwise.</returns>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.IConfigurationProvider.Load">
            <summary>
            Loads configuration values from the source represented by this <see cref="T:Microsoft.Extensions.Configuration.IConfigurationProvider"/>.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.IConfigurationProvider.GetChildKeys(System.Collections.Generic.IEnumerable{System.String},System.String)">
            <summary>
            Returns the immediate descendant configuration keys for a given parent path based on the data of this
            <see cref="T:Microsoft.Extensions.Configuration.IConfigurationProvider"/> and the set of keys returned by all the preceding
            <see cref="T:Microsoft.Extensions.Configuration.IConfigurationProvider"/> providers.
            </summary>
            <param name="earlierKeys">The child keys returned by the preceding providers for the same parent path.</param>
            <param name="parentPath">The parent path.</param>
            <returns>The child keys.</returns>
        </member>
        <member name="T:Microsoft.Extensions.Configuration.IConfigurationRoot">
            <summary>
            Represents the root of an <see cref="T:Microsoft.Extensions.Configuration.IConfiguration"/> hierarchy.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.IConfigurationRoot.Reload">
            <summary>
            Forces the configuration values to be reloaded from the underlying <see cref="T:Microsoft.Extensions.Configuration.IConfigurationProvider"/> providers.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Configuration.IConfigurationRoot.Providers">
            <summary>
            Gets the <see cref="T:Microsoft.Extensions.Configuration.IConfigurationProvider"/> providers for this configuration.
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.Configuration.IConfigurationSection">
            <summary>
            Represents a section of application configuration values.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Configuration.IConfigurationSection.Key">
            <summary>
            Gets the key this section occupies in its parent.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Configuration.IConfigurationSection.Path">
            <summary>
            Gets the full path to this section within the <see cref="T:Microsoft.Extensions.Configuration.IConfiguration"/>.
            </summary>
        </member>
        <member name="P:Microsoft.Extensions.Configuration.IConfigurationSection.Value">
            <summary>
            Gets or sets the section value.
            </summary>
        </member>
        <member name="T:Microsoft.Extensions.Configuration.IConfigurationSource">
            <summary>
            Represents a source of configuration key/values for an application.
            </summary>
        </member>
        <member name="M:Microsoft.Extensions.Configuration.IConfigurationSource.Build(Microsoft.Extensions.Configuration.IConfigurationBuilder)">
            <summary>
            Builds the <see cref="T:Microsoft.Extensions.Configuration.IConfigurationProvider"/> for this source.
            </summary>
            <param name="builder">The <see cref="T:Microsoft.Extensions.Configuration.IConfigurationBuilder"/>.</param>
            <returns>An <see cref="T:Microsoft.Extensions.Configuration.IConfigurationProvider"/></returns>
        </member>
        <member name="M:System.ThrowHelper.ThrowIfNull(System.Object,System.String)">
            <summary>Throws an <see cref="T:System.ArgumentNullException"/> if <paramref name="argument"/> is null.</summary>
            <param name="argument">The reference type argument to validate as non-null.</param>
            <param name="paramName">The name of the parameter with which <paramref name="argument"/> corresponds.</param>
        </member>
        <member name="M:System.ThrowHelper.IfNullOrWhitespace(System.String,System.String)">
            <summary>
            Throws either an <see cref="T:System.ArgumentNullException"/> or an <see cref="T:System.ArgumentException"/>
            if the specified string is <see langword="null"/> or whitespace respectively.
            </summary>
            <param name="argument">String to be checked for <see langword="null"/> or whitespace.</param>
            <param name="paramName">The name of the parameter being checked.</param>
            <returns>The original value of <paramref name="argument"/>.</returns>
        </member>
        <member name="T:System.Runtime.InteropServices.LibraryImportAttribute">
            <summary>
            Attribute used to indicate a source generator should create a function for marshalling
            arguments instead of relying on the runtime to generate an equivalent marshalling function at run-time.
            </summary>
            <remarks>
            This attribute is meaningless if the source generator associated with it is not enabled.
            The current built-in source generator only supports C# and only supplies an implementation when
            applied to static, partial, non-generic methods.
            </remarks>
        </member>
        <member name="M:System.Runtime.InteropServices.LibraryImportAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:System.Runtime.InteropServices.LibraryImportAttribute"/>.
            </summary>
            <param name="libraryName">Name of the library containing the import.</param>
        </member>
        <member name="P:System.Runtime.InteropServices.LibraryImportAttribute.LibraryName">
            <summary>
            Gets the name of the library containing the import.
            </summary>
        </member>
        <member name="P:System.Runtime.InteropServices.LibraryImportAttribute.EntryPoint">
            <summary>
            Gets or sets the name of the entry point to be called.
            </summary>
        </member>
        <member name="P:System.Runtime.InteropServices.LibraryImportAttribute.StringMarshalling">
            <summary>
            Gets or sets how to marshal string arguments to the method.
            </summary>
            <remarks>
            If this field is set to a value other than <see cref="F:System.Runtime.InteropServices.StringMarshalling.Custom" />,
            <see cref="P:System.Runtime.InteropServices.LibraryImportAttribute.StringMarshallingCustomType" /> must not be specified.
            </remarks>
        </member>
        <member name="P:System.Runtime.InteropServices.LibraryImportAttribute.StringMarshallingCustomType">
            <summary>
            Gets or sets the <see cref="T:System.Type"/> used to control how string arguments to the method are marshalled.
            </summary>
            <remarks>
            If this field is specified, <see cref="P:System.Runtime.InteropServices.LibraryImportAttribute.StringMarshalling" /> must not be specified
            or must be set to <see cref="F:System.Runtime.InteropServices.StringMarshalling.Custom" />.
            </remarks>
        </member>
        <member name="P:System.Runtime.InteropServices.LibraryImportAttribute.SetLastError">
            <summary>
            Gets or sets whether the callee sets an error (SetLastError on Windows or errno
            on other platforms) before returning from the attributed method.
            </summary>
        </member>
        <member name="T:System.Runtime.InteropServices.StringMarshalling">
            <summary>
            Specifies how strings should be marshalled for generated p/invokes
            </summary>
        </member>
        <member name="F:System.Runtime.InteropServices.StringMarshalling.Custom">
            <summary>
            Indicates the user is supplying a specific marshaller in <see cref="P:System.Runtime.InteropServices.LibraryImportAttribute.StringMarshallingCustomType"/>.
            </summary>
        </member>
        <member name="F:System.Runtime.InteropServices.StringMarshalling.Utf8">
            <summary>
            Use the platform-provided UTF-8 marshaller.
            </summary>
        </member>
        <member name="F:System.Runtime.InteropServices.StringMarshalling.Utf16">
            <summary>
            Use the platform-provided UTF-16 marshaller.
            </summary>
        </member>
        <member name="P:System.SR.InvalidSectionName">
            <summary>Section '{0}' not found in configuration.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.AllowNullAttribute">
            <summary>Specifies that null is allowed as an input even if the corresponding type disallows it.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DisallowNullAttribute">
            <summary>Specifies that null is disallowed as an input even if the corresponding type allows it.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MaybeNullAttribute">
            <summary>Specifies that an output may be null even if the corresponding type disallows it.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullAttribute">
            <summary>Specifies that an output will not be null even if the corresponding type allows it. Specifies that an input argument was not null when the call returns.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute">
            <summary>Specifies that when a method returns <see cref="P:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute.ReturnValue"/>, the parameter may be null even if the corresponding type disallows it.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute.#ctor(System.Boolean)">
            <summary>Initializes the attribute with the specified return value condition.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated parameter may be null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute.ReturnValue">
            <summary>Gets the return value condition.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute">
            <summary>Specifies that when a method returns <see cref="P:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.ReturnValue"/>, the parameter will not be null even if the corresponding type allows it.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.#ctor(System.Boolean)">
            <summary>Initializes the attribute with the specified return value condition.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated parameter will not be null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.ReturnValue">
            <summary>Gets the return value condition.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute">
            <summary>Specifies that the output will be non-null if the named parameter is non-null.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute.#ctor(System.String)">
            <summary>Initializes the attribute with the associated parameter name.</summary>
            <param name="parameterName">
            The associated parameter name.  The output will be non-null if the argument to the parameter specified is non-null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute.ParameterName">
            <summary>Gets the associated parameter name.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DoesNotReturnAttribute">
            <summary>Applied to a method that will never return under any circumstance.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DoesNotReturnIfAttribute">
            <summary>Specifies that the method will not return if the associated Boolean parameter is passed the specified value.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.DoesNotReturnIfAttribute.#ctor(System.Boolean)">
            <summary>Initializes the attribute with the specified parameter value.</summary>
            <param name="parameterValue">
            The condition parameter value. Code after the method will be considered unreachable by diagnostics if the argument to
            the associated parameter matches this value.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.DoesNotReturnIfAttribute.ParameterValue">
            <summary>Gets the condition parameter value.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute">
            <summary>Specifies that the method or property will ensure that the listed field and property members have not-null values.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute.#ctor(System.String)">
            <summary>Initializes the attribute with a field or property member.</summary>
            <param name="member">
            The field or property member that is promised to be not-null.
            </param>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute.#ctor(System.String[])">
            <summary>Initializes the attribute with the list of field and property members.</summary>
            <param name="members">
            The list of field and property members that are promised to be not-null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute.Members">
            <summary>Gets field or property member names.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute">
            <summary>Specifies that the method or property will ensure that the listed field and property members have not-null values when returning with the specified return value condition.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.#ctor(System.Boolean,System.String)">
            <summary>Initializes the attribute with the specified return value condition and a field or property member.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated field or property member will not be null.
            </param>
            <param name="member">
            The field or property member that is promised to be not-null.
            </param>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.#ctor(System.Boolean,System.String[])">
            <summary>Initializes the attribute with the specified return value condition and list of field and property members.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated field and property members will not be null.
            </param>
            <param name="members">
            The list of field and property members that are promised to be not-null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.ReturnValue">
            <summary>Gets the return value condition.</summary>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.Members">
            <summary>Gets field or property member names.</summary>
        </member>
    </members>
</doc>
