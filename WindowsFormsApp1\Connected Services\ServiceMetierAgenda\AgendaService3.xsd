<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:tns="http://tempuri.org/" elementFormDefault="qualified" targetNamespace="http://tempuri.org/" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:import schemaLocation="http://localhost:60827/Wcf/AgendaService.svc?xsd=xsd2" namespace="http://schemas.datacontract.org/2004/07/MetierRvMedical.Model" />
  <xs:import schemaLocation="http://localhost:60827/Wcf/AgendaService.svc?xsd=xsd3" namespace="http://schemas.microsoft.com/2003/10/Serialization/Arrays" />
  <xs:element name="DoWork">
    <xs:complexType>
      <xs:sequence />
    </xs:complexType>
  </xs:element>
  <xs:element name="DoWorkResponse">
    <xs:complexType>
      <xs:sequence />
    </xs:complexType>
  </xs:element>
  <xs:element name="LoadAgenda">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="datetoday" type="xs:dateTime" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="LoadAgendaResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q1="http://schemas.datacontract.org/2004/07/MetierRvMedical.Model" minOccurs="0" name="LoadAgendaResult" nillable="true" type="q1:ArrayOfAgenda" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="LoadCreneauxByDate">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="dateRecherche" type="xs:dateTime" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="LoadCreneauxByDateResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q2="http://schemas.microsoft.com/2003/10/Serialization/Arrays" minOccurs="0" name="LoadCreneauxByDateResult" nillable="true" type="q2:ArrayOfArrayOfKeyValueOfstringanyType" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="CreneauxByHoraire">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="dateRecherche" type="xs:dateTime" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="CreneauxByHoraireResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q3="http://schemas.microsoft.com/2003/10/Serialization/Arrays" minOccurs="0" name="CreneauxByHoraireResult" nillable="true" type="q3:ArrayOfArrayOfKeyValueOfstringanyType" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="ListeTimeCreneau">
    <xs:complexType>
      <xs:sequence>
        <xs:element minOccurs="0" name="dateRecherche" type="xs:dateTime" />
        <xs:element minOccurs="0" name="idMedecin" nillable="true" type="xs:int" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
  <xs:element name="ListeTimeCreneauResponse">
    <xs:complexType>
      <xs:sequence>
        <xs:element xmlns:q4="http://schemas.microsoft.com/2003/10/Serialization/Arrays" minOccurs="0" name="ListeTimeCreneauResult" nillable="true" type="q4:ArrayOfint" />
      </xs:sequence>
    </xs:complexType>
  </xs:element>
</xs:schema>