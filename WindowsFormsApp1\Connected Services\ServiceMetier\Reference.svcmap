<?xml version="1.0" encoding="utf-8"?>
<ReferenceGroup xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" ID="8b4c55f8-fe2e-41dd-bd6e-b236eeae6f07" xmlns="urn:schemas-microsoft-com:xml-wcfservicemap">
  <ClientOptions>
    <GenerateAsynchronousMethods>false</GenerateAsynchronousMethods>
    <GenerateTaskBasedAsynchronousMethod>true</GenerateTaskBasedAsynchronousMethod>
    <EnableDataBinding>true</EnableDataBinding>
    <ExcludedTypes />
    <ImportXmlTypes>false</ImportXmlTypes>
    <GenerateInternalTypes>false</GenerateInternalTypes>
    <GenerateMessageContracts>false</GenerateMessageContracts>
    <NamespaceMappings />
    <CollectionMappings />
    <GenerateSerializableTypes>true</GenerateSerializableTypes>
    <Serializer>Auto</Serializer>
    <UseSerializerForFaults>true</UseSerializerForFaults>
    <ReferenceAllAssemblies>true</ReferenceAllAssemblies>
    <ReferencedAssemblies />
    <ReferencedDataContractTypes />
    <ServiceContractMappings />
  </ClientOptions>
  <MetadataSources>
    <MetadataSource Address="http://localhost:60827/Service1.svc" Protocol="http" SourceId="1" />
  </MetadataSources>
  <Metadata>
    <MetadataFile FileName="Service1.xsd" MetadataType="Schema" ID="db003c2b-7705-4323-bca4-a2a4ee9cce7c" SourceId="1" SourceUrl="http://localhost:60827/Service1.svc?xsd=xsd2" />
    <MetadataFile FileName="Service11.xsd" MetadataType="Schema" ID="39309511-9b43-4f7c-9a80-357151cb08b5" SourceId="1" SourceUrl="http://localhost:60827/Service1.svc?xsd=xsd1" />
    <MetadataFile FileName="Service12.xsd" MetadataType="Schema" ID="05466549-4807-4aed-8550-b6d3a598a619" SourceId="1" SourceUrl="http://localhost:60827/Service1.svc?xsd=xsd0" />
    <MetadataFile FileName="Service1.wsdl" MetadataType="Wsdl" ID="515a50c5-20bd-4d3a-84ae-f3299d308ec6" SourceId="1" SourceUrl="http://localhost:60827/Service1.svc?wsdl" />
    <MetadataFile FileName="Service1.disco" MetadataType="Disco" ID="4c01e50b-30b3-449e-914c-2cefa748cc35" SourceId="1" SourceUrl="http://localhost:60827/Service1.svc?disco" />
    <MetadataFile FileName="Service13.xsd" MetadataType="Schema" ID="a1503030-cc44-468e-a4fd-795ebdd81eb4" SourceId="1" SourceUrl="http://localhost:60827/Service1.svc?xsd=xsd3" />
  </Metadata>
  <Extensions>
    <ExtensionFile FileName="configuration91.svcinfo" Name="configuration91.svcinfo" />
    <ExtensionFile FileName="configuration.svcinfo" Name="configuration.svcinfo" />
  </Extensions>
</ReferenceGroup>