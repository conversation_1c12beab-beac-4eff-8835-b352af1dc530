﻿//------------------------------------------------------------------------------
// <auto-generated>
//     Ce code a été généré par un outil.
//
//     Les changements apportés à ce fichier peuvent provoquer un comportement incorrect et seront perdus si
//     le code est regénéré.
// </auto-generated>
//------------------------------------------------------------------------------

namespace ServiceMetierAgenda
{
    using System.Runtime.Serialization;
    
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Agenda", Namespace="http://schemas.datacontract.org/2004/07/MetierRvMedical.Model")]
    public partial class Agenda : object
    {
        
        private int CreneauField;
        
        private ServiceMetierAgenda.Creneau[] CreneauxField;
        
        private System.DateTime DatePlanifieField;
        
        private string HeureDebutField;
        
        private string HeureFinField;
        
        private int IdAgendaField;
        
        private int IdMedecinField;
        
        private string LieuField;
        
        private ServiceMetierAgenda.Medecin MedecinField;
        
        private ServiceMetierAgenda.RendezVous[] RendezVousField;
        
        private string TitreField;
        
        private string statutField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Creneau
        {
            get
            {
                return this.CreneauField;
            }
            set
            {
                this.CreneauField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public ServiceMetierAgenda.Creneau[] Creneaux
        {
            get
            {
                return this.CreneauxField;
            }
            set
            {
                this.CreneauxField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime DatePlanifie
        {
            get
            {
                return this.DatePlanifieField;
            }
            set
            {
                this.DatePlanifieField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string HeureDebut
        {
            get
            {
                return this.HeureDebutField;
            }
            set
            {
                this.HeureDebutField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string HeureFin
        {
            get
            {
                return this.HeureFinField;
            }
            set
            {
                this.HeureFinField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int IdAgenda
        {
            get
            {
                return this.IdAgendaField;
            }
            set
            {
                this.IdAgendaField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int IdMedecin
        {
            get
            {
                return this.IdMedecinField;
            }
            set
            {
                this.IdMedecinField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Lieu
        {
            get
            {
                return this.LieuField;
            }
            set
            {
                this.LieuField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public ServiceMetierAgenda.Medecin Medecin
        {
            get
            {
                return this.MedecinField;
            }
            set
            {
                this.MedecinField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public ServiceMetierAgenda.RendezVous[] RendezVous
        {
            get
            {
                return this.RendezVousField;
            }
            set
            {
                this.RendezVousField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Titre
        {
            get
            {
                return this.TitreField;
            }
            set
            {
                this.TitreField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string statut
        {
            get
            {
                return this.statutField;
            }
            set
            {
                this.statutField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Medecin", Namespace="http://schemas.datacontract.org/2004/07/MetierRvMedical.Model")]
    public partial class Medecin : ServiceMetierAgenda.Utilisateur
    {
        
        private ServiceMetierAgenda.Agenda[] AgendasField;
        
        private System.Nullable<int> IdSpecialiteField;
        
        private string NumeroOrdreField;
        
        private ServiceMetierAgenda.Specialite SpecialiteField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public ServiceMetierAgenda.Agenda[] Agendas
        {
            get
            {
                return this.AgendasField;
            }
            set
            {
                this.AgendasField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> IdSpecialite
        {
            get
            {
                return this.IdSpecialiteField;
            }
            set
            {
                this.IdSpecialiteField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string NumeroOrdre
        {
            get
            {
                return this.NumeroOrdreField;
            }
            set
            {
                this.NumeroOrdreField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public ServiceMetierAgenda.Specialite Specialite
        {
            get
            {
                return this.SpecialiteField;
            }
            set
            {
                this.SpecialiteField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Creneau", Namespace="http://schemas.datacontract.org/2004/07/MetierRvMedical.Model")]
    public partial class Creneau : object
    {
        
        private ServiceMetierAgenda.Agenda AgendaField;
        
        private System.DateTime DateField;
        
        private bool DisponibleField;
        
        private string HeureDebutField;
        
        private string HeureFinField;
        
        private int IdAgendaField;
        
        private int IdCreneauField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public ServiceMetierAgenda.Agenda Agenda
        {
            get
            {
                return this.AgendaField;
            }
            set
            {
                this.AgendaField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime Date
        {
            get
            {
                return this.DateField;
            }
            set
            {
                this.DateField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public bool Disponible
        {
            get
            {
                return this.DisponibleField;
            }
            set
            {
                this.DisponibleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string HeureDebut
        {
            get
            {
                return this.HeureDebutField;
            }
            set
            {
                this.HeureDebutField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string HeureFin
        {
            get
            {
                return this.HeureFinField;
            }
            set
            {
                this.HeureFinField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int IdAgenda
        {
            get
            {
                return this.IdAgendaField;
            }
            set
            {
                this.IdAgendaField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int IdCreneau
        {
            get
            {
                return this.IdCreneauField;
            }
            set
            {
                this.IdCreneauField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="RendezVous", Namespace="http://schemas.datacontract.org/2004/07/MetierRvMedical.Model")]
    public partial class RendezVous : object
    {
        
        private ServiceMetierAgenda.Creneau CreneauField;
        
        private System.DateTime DateRvField;
        
        private System.Nullable<int> IdCreneauField;
        
        private System.Nullable<int> IdMedecinField;
        
        private System.Nullable<int> IdPatientField;
        
        private int IdRvField;
        
        private System.Nullable<int> IdSoinField;
        
        private ServiceMetierAgenda.Medecin MedecinField;
        
        private ServiceMetierAgenda.Patient PatientField;
        
        private ServiceMetierAgenda.Soin SoinField;
        
        private string StatutField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public ServiceMetierAgenda.Creneau Creneau
        {
            get
            {
                return this.CreneauField;
            }
            set
            {
                this.CreneauField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.DateTime DateRv
        {
            get
            {
                return this.DateRvField;
            }
            set
            {
                this.DateRvField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> IdCreneau
        {
            get
            {
                return this.IdCreneauField;
            }
            set
            {
                this.IdCreneauField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> IdMedecin
        {
            get
            {
                return this.IdMedecinField;
            }
            set
            {
                this.IdMedecinField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> IdPatient
        {
            get
            {
                return this.IdPatientField;
            }
            set
            {
                this.IdPatientField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int IdRv
        {
            get
            {
                return this.IdRvField;
            }
            set
            {
                this.IdRvField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> IdSoin
        {
            get
            {
                return this.IdSoinField;
            }
            set
            {
                this.IdSoinField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public ServiceMetierAgenda.Medecin Medecin
        {
            get
            {
                return this.MedecinField;
            }
            set
            {
                this.MedecinField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public ServiceMetierAgenda.Patient Patient
        {
            get
            {
                return this.PatientField;
            }
            set
            {
                this.PatientField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public ServiceMetierAgenda.Soin Soin
        {
            get
            {
                return this.SoinField;
            }
            set
            {
                this.SoinField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Statut
        {
            get
            {
                return this.StatutField;
            }
            set
            {
                this.StatutField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Personne", Namespace="http://schemas.datacontract.org/2004/07/MetierRvMedical.Model")]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(ServiceMetierAgenda.Patient))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(ServiceMetierAgenda.Utilisateur))]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(ServiceMetierAgenda.Medecin))]
    public partial class Personne : object
    {
        
        private string AdresseField;
        
        private string EmailField;
        
        private int IdUField;
        
        private string NomPrenomField;
        
        private string TELField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Adresse
        {
            get
            {
                return this.AdresseField;
            }
            set
            {
                this.AdresseField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Email
        {
            get
            {
                return this.EmailField;
            }
            set
            {
                this.EmailField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int IdU
        {
            get
            {
                return this.IdUField;
            }
            set
            {
                this.IdUField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string NomPrenom
        {
            get
            {
                return this.NomPrenomField;
            }
            set
            {
                this.NomPrenomField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string TEL
        {
            get
            {
                return this.TELField;
            }
            set
            {
                this.TELField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Patient", Namespace="http://schemas.datacontract.org/2004/07/MetierRvMedical.Model")]
    public partial class Patient : ServiceMetierAgenda.Personne
    {
        
        private System.Nullable<System.DateTime> DateNaissanceField;
        
        private ServiceMetierAgenda.GroupeSanguin GroupeSanguinField;
        
        private int IdGroupeSanguinField;
        
        private System.Nullable<float> PoidsField;
        
        private System.Nullable<float> TailleField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> DateNaissance
        {
            get
            {
                return this.DateNaissanceField;
            }
            set
            {
                this.DateNaissanceField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public ServiceMetierAgenda.GroupeSanguin GroupeSanguin
        {
            get
            {
                return this.GroupeSanguinField;
            }
            set
            {
                this.GroupeSanguinField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int IdGroupeSanguin
        {
            get
            {
                return this.IdGroupeSanguinField;
            }
            set
            {
                this.IdGroupeSanguinField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<float> Poids
        {
            get
            {
                return this.PoidsField;
            }
            set
            {
                this.PoidsField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<float> Taille
        {
            get
            {
                return this.TailleField;
            }
            set
            {
                this.TailleField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Utilisateur", Namespace="http://schemas.datacontract.org/2004/07/MetierRvMedical.Model")]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(ServiceMetierAgenda.Medecin))]
    public partial class Utilisateur : ServiceMetierAgenda.Personne
    {
        
        private System.Nullable<int> IdRoleField;
        
        private string MotDePasseField;
        
        private ServiceMetierAgenda.Role RoleField;
        
        private string identifiantField;
        
        private System.Nullable<bool> statutField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<int> IdRole
        {
            get
            {
                return this.IdRoleField;
            }
            set
            {
                this.IdRoleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string MotDePasse
        {
            get
            {
                return this.MotDePasseField;
            }
            set
            {
                this.MotDePasseField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public ServiceMetierAgenda.Role Role
        {
            get
            {
                return this.RoleField;
            }
            set
            {
                this.RoleField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string identifiant
        {
            get
            {
                return this.identifiantField;
            }
            set
            {
                this.identifiantField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<bool> statut
        {
            get
            {
                return this.statutField;
            }
            set
            {
                this.statutField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Role", Namespace="http://schemas.datacontract.org/2004/07/MetierRvMedical.Model")]
    public partial class Role : object
    {
        
        private string CodeField;
        
        private string DescriptionField;
        
        private int IdField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Code
        {
            get
            {
                return this.CodeField;
            }
            set
            {
                this.CodeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Description
        {
            get
            {
                return this.DescriptionField;
            }
            set
            {
                this.DescriptionField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Id
        {
            get
            {
                return this.IdField;
            }
            set
            {
                this.IdField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Specialite", Namespace="http://schemas.datacontract.org/2004/07/MetierRvMedical.Model")]
    public partial class Specialite : object
    {
        
        private string CodeSpecialiteField;
        
        private int IdField;
        
        private string NomSpecialiteField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string CodeSpecialite
        {
            get
            {
                return this.CodeSpecialiteField;
            }
            set
            {
                this.CodeSpecialiteField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Id
        {
            get
            {
                return this.IdField;
            }
            set
            {
                this.IdField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string NomSpecialite
        {
            get
            {
                return this.NomSpecialiteField;
            }
            set
            {
                this.NomSpecialiteField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="GroupeSanguin", Namespace="http://schemas.datacontract.org/2004/07/MetierRvMedical.Model")]
    public partial class GroupeSanguin : object
    {
        
        private string CodeGroupeSanguinField;
        
        private int IdGroupeSanguinField;
        
        private string NomGroupeSanguinField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string CodeGroupeSanguin
        {
            get
            {
                return this.CodeGroupeSanguinField;
            }
            set
            {
                this.CodeGroupeSanguinField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int IdGroupeSanguin
        {
            get
            {
                return this.IdGroupeSanguinField;
            }
            set
            {
                this.IdGroupeSanguinField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string NomGroupeSanguin
        {
            get
            {
                return this.NomGroupeSanguinField;
            }
            set
            {
                this.NomGroupeSanguinField = value;
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Soin", Namespace="http://schemas.datacontract.org/2004/07/MetierRvMedical.Model")]
    public partial class Soin : object
    {
        
        private string CategoryField;
        
        private string DurationField;
        
        private int IdSoinField;
        
        private string NameSoinField;
        
        private int PriceField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Category
        {
            get
            {
                return this.CategoryField;
            }
            set
            {
                this.CategoryField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Duration
        {
            get
            {
                return this.DurationField;
            }
            set
            {
                this.DurationField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int IdSoin
        {
            get
            {
                return this.IdSoinField;
            }
            set
            {
                this.IdSoinField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string NameSoin
        {
            get
            {
                return this.NameSoinField;
            }
            set
            {
                this.NameSoinField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Price
        {
            get
            {
                return this.PriceField;
            }
            set
            {
                this.PriceField = value;
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    [System.ServiceModel.ServiceContractAttribute(ConfigurationName="ServiceMetierAgenda.IAgendaService")]
    public interface IAgendaService
    {
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IAgendaService/DoWork", ReplyAction="http://tempuri.org/IAgendaService/DoWorkResponse")]
        System.Threading.Tasks.Task DoWorkAsync();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IAgendaService/LoadAgenda", ReplyAction="http://tempuri.org/IAgendaService/LoadAgendaResponse")]
        System.Threading.Tasks.Task<ServiceMetierAgenda.Agenda[]> LoadAgendaAsync(System.DateTime datetoday);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IAgendaService/LoadCreneauxByDate", ReplyAction="http://tempuri.org/IAgendaService/LoadCreneauxByDateResponse")]
        System.Threading.Tasks.Task<System.Collections.Generic.Dictionary<string, object>[]> LoadCreneauxByDateAsync(System.DateTime dateRecherche);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IAgendaService/CreneauxByHoraire", ReplyAction="http://tempuri.org/IAgendaService/CreneauxByHoraireResponse")]
        System.Threading.Tasks.Task<System.Collections.Generic.Dictionary<string, object>[]> CreneauxByHoraireAsync(System.DateTime dateRecherche);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IAgendaService/ListeTimeCreneau", ReplyAction="http://tempuri.org/IAgendaService/ListeTimeCreneauResponse")]
        System.Threading.Tasks.Task<int[]> ListeTimeCreneauAsync(System.DateTime dateRecherche, System.Nullable<int> idMedecin);
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    public interface IAgendaServiceChannel : ServiceMetierAgenda.IAgendaService, System.ServiceModel.IClientChannel
    {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.Tools.ServiceModel.Svcutil", "8.0.0")]
    public partial class AgendaServiceClient : System.ServiceModel.ClientBase<ServiceMetierAgenda.IAgendaService>, ServiceMetierAgenda.IAgendaService
    {
        
        /// <summary>
        /// Implémentez cette méthode partielle pour configurer le point de terminaison de service.
        /// </summary>
        /// <param name="serviceEndpoint">Point de terminaison à configurer</param>
        /// <param name="clientCredentials">Informations d'identification du client</param>
        static partial void ConfigureEndpoint(System.ServiceModel.Description.ServiceEndpoint serviceEndpoint, System.ServiceModel.Description.ClientCredentials clientCredentials);
        
        public AgendaServiceClient() : 
                base(AgendaServiceClient.GetDefaultBinding(), AgendaServiceClient.GetDefaultEndpointAddress())
        {
            this.Endpoint.Name = EndpointConfiguration.BasicHttpBinding_IAgendaService.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public AgendaServiceClient(EndpointConfiguration endpointConfiguration) : 
                base(AgendaServiceClient.GetBindingForEndpoint(endpointConfiguration), AgendaServiceClient.GetEndpointAddress(endpointConfiguration))
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public AgendaServiceClient(EndpointConfiguration endpointConfiguration, string remoteAddress) : 
                base(AgendaServiceClient.GetBindingForEndpoint(endpointConfiguration), new System.ServiceModel.EndpointAddress(remoteAddress))
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public AgendaServiceClient(EndpointConfiguration endpointConfiguration, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(AgendaServiceClient.GetBindingForEndpoint(endpointConfiguration), remoteAddress)
        {
            this.Endpoint.Name = endpointConfiguration.ToString();
            ConfigureEndpoint(this.Endpoint, this.ClientCredentials);
        }
        
        public AgendaServiceClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(binding, remoteAddress)
        {
        }
        
        public System.Threading.Tasks.Task DoWorkAsync()
        {
            return base.Channel.DoWorkAsync();
        }
        
        public System.Threading.Tasks.Task<ServiceMetierAgenda.Agenda[]> LoadAgendaAsync(System.DateTime datetoday)
        {
            return base.Channel.LoadAgendaAsync(datetoday);
        }
        
        public System.Threading.Tasks.Task<System.Collections.Generic.Dictionary<string, object>[]> LoadCreneauxByDateAsync(System.DateTime dateRecherche)
        {
            return base.Channel.LoadCreneauxByDateAsync(dateRecherche);
        }
        
        public System.Threading.Tasks.Task<System.Collections.Generic.Dictionary<string, object>[]> CreneauxByHoraireAsync(System.DateTime dateRecherche)
        {
            return base.Channel.CreneauxByHoraireAsync(dateRecherche);
        }
        
        public System.Threading.Tasks.Task<int[]> ListeTimeCreneauAsync(System.DateTime dateRecherche, System.Nullable<int> idMedecin)
        {
            return base.Channel.ListeTimeCreneauAsync(dateRecherche, idMedecin);
        }
        
        public virtual System.Threading.Tasks.Task OpenAsync()
        {
            return System.Threading.Tasks.Task.Factory.FromAsync(((System.ServiceModel.ICommunicationObject)(this)).BeginOpen(null, null), new System.Action<System.IAsyncResult>(((System.ServiceModel.ICommunicationObject)(this)).EndOpen));
        }
        
        #if !NET6_0_OR_GREATER
        public virtual System.Threading.Tasks.Task CloseAsync()
        {
            return System.Threading.Tasks.Task.Factory.FromAsync(((System.ServiceModel.ICommunicationObject)(this)).BeginClose(null, null), new System.Action<System.IAsyncResult>(((System.ServiceModel.ICommunicationObject)(this)).EndClose));
        }
        #endif
        
        private static System.ServiceModel.Channels.Binding GetBindingForEndpoint(EndpointConfiguration endpointConfiguration)
        {
            if ((endpointConfiguration == EndpointConfiguration.BasicHttpBinding_IAgendaService))
            {
                System.ServiceModel.BasicHttpBinding result = new System.ServiceModel.BasicHttpBinding();
                result.MaxBufferSize = int.MaxValue;
                result.ReaderQuotas = System.Xml.XmlDictionaryReaderQuotas.Max;
                result.MaxReceivedMessageSize = int.MaxValue;
                result.AllowCookies = true;
                return result;
            }
            throw new System.InvalidOperationException(string.Format("Le point de terminaison nommé \'{0}\' est introuvable.", endpointConfiguration));
        }
        
        private static System.ServiceModel.EndpointAddress GetEndpointAddress(EndpointConfiguration endpointConfiguration)
        {
            if ((endpointConfiguration == EndpointConfiguration.BasicHttpBinding_IAgendaService))
            {
                return new System.ServiceModel.EndpointAddress("http://localhost:60827/Wcf/AgendaService.svc");
            }
            throw new System.InvalidOperationException(string.Format("Le point de terminaison nommé \'{0}\' est introuvable.", endpointConfiguration));
        }
        
        private static System.ServiceModel.Channels.Binding GetDefaultBinding()
        {
            return AgendaServiceClient.GetBindingForEndpoint(EndpointConfiguration.BasicHttpBinding_IAgendaService);
        }
        
        private static System.ServiceModel.EndpointAddress GetDefaultEndpointAddress()
        {
            return AgendaServiceClient.GetEndpointAddress(EndpointConfiguration.BasicHttpBinding_IAgendaService);
        }
        
        public enum EndpointConfiguration
        {
            
            BasicHttpBinding_IAgendaService,
        }
    }
}
