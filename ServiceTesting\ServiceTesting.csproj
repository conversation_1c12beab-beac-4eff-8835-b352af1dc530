<?xml version="1.0" encoding="utf-8"?>
<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>
  <ItemGroup>
    <PackageReference Include="MySql.Data.EntityFramework" Version="9.1.0" />
    <PackageReference Include="System.Configuration.ConfigurationManager" Version="9.0.5" />
    <PackageReference Include="System.Data.SqlClient" Version="4.9.0" />
    <PackageReference Include="System.Security.Permissions" Version="9.0.5" />
    <PackageReference Include="System.ServiceModel.Http" Version="8.*" />
    <PackageReference Include="System.ServiceModel.NetTcp" Version="8.*" />
    <PackageReference Include="System.ServiceModel.Primitives" Version="8.*" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\MetierRvMedical\MetierRvMedical.csproj" />
  </ItemGroup>
  <ItemGroup>
    <None Update="menu.tt">
      <LastGenOutput>menu.txt</LastGenOutput>
      <Generator>TextTemplatingFileGenerator</Generator>
    </None>
    <None Update="menu.txt">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      <DependentUpon>menu.tt</DependentUpon>
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
    </None>
  </ItemGroup>
  <ItemGroup>
    <Service Include="{508349b6-6b84-4df5-91f0-309beebad82d}" />
  </ItemGroup>
</Project>