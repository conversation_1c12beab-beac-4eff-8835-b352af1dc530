<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions xmlns:wsam="http://www.w3.org/2007/05/addressing/metadata" xmlns:wsx="http://schemas.xmlsoap.org/ws/2004/09/mex" xmlns:wsap="http://schemas.xmlsoap.org/ws/2004/08/addressing/policy" xmlns:msc="http://schemas.microsoft.com/ws/2005/12/wsdl/contract" xmlns:wsp="http://schemas.xmlsoap.org/ws/2004/09/policy" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:tns="http://tempuri.org/" xmlns:wsa10="http://www.w3.org/2005/08/addressing" xmlns:wsaw="http://www.w3.org/2006/05/addressing/wsdl" xmlns:wsa="http://schemas.xmlsoap.org/ws/2004/08/addressing" name="AgendaService" targetNamespace="http://tempuri.org/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/">
  <wsdl:types>
    <xsd:schema targetNamespace="http://tempuri.org/Imports">
      <xsd:import schemaLocation="http://localhost:60827/Wcf/AgendaService.svc?xsd=xsd0" namespace="http://tempuri.org/" />
      <xsd:import schemaLocation="http://localhost:60827/Wcf/AgendaService.svc?xsd=xsd1" namespace="http://schemas.microsoft.com/2003/10/Serialization/" />
      <xsd:import schemaLocation="http://localhost:60827/Wcf/AgendaService.svc?xsd=xsd2" namespace="http://schemas.datacontract.org/2004/07/MetierRvMedical.Model" />
      <xsd:import schemaLocation="http://localhost:60827/Wcf/AgendaService.svc?xsd=xsd3" namespace="http://schemas.microsoft.com/2003/10/Serialization/Arrays" />
    </xsd:schema>
  </wsdl:types>
  <wsdl:message name="IAgendaService_DoWork_InputMessage">
    <wsdl:part name="parameters" element="tns:DoWork" />
  </wsdl:message>
  <wsdl:message name="IAgendaService_DoWork_OutputMessage">
    <wsdl:part name="parameters" element="tns:DoWorkResponse" />
  </wsdl:message>
  <wsdl:message name="IAgendaService_LoadAgenda_InputMessage">
    <wsdl:part name="parameters" element="tns:LoadAgenda" />
  </wsdl:message>
  <wsdl:message name="IAgendaService_LoadAgenda_OutputMessage">
    <wsdl:part name="parameters" element="tns:LoadAgendaResponse" />
  </wsdl:message>
  <wsdl:message name="IAgendaService_LoadCreneauxByDate_InputMessage">
    <wsdl:part name="parameters" element="tns:LoadCreneauxByDate" />
  </wsdl:message>
  <wsdl:message name="IAgendaService_LoadCreneauxByDate_OutputMessage">
    <wsdl:part name="parameters" element="tns:LoadCreneauxByDateResponse" />
  </wsdl:message>
  <wsdl:message name="IAgendaService_CreneauxByHoraire_InputMessage">
    <wsdl:part name="parameters" element="tns:CreneauxByHoraire" />
  </wsdl:message>
  <wsdl:message name="IAgendaService_CreneauxByHoraire_OutputMessage">
    <wsdl:part name="parameters" element="tns:CreneauxByHoraireResponse" />
  </wsdl:message>
  <wsdl:message name="IAgendaService_ListeTimeCreneau_InputMessage">
    <wsdl:part name="parameters" element="tns:ListeTimeCreneau" />
  </wsdl:message>
  <wsdl:message name="IAgendaService_ListeTimeCreneau_OutputMessage">
    <wsdl:part name="parameters" element="tns:ListeTimeCreneauResponse" />
  </wsdl:message>
  <wsdl:portType name="IAgendaService">
    <wsdl:operation name="DoWork">
      <wsdl:input wsaw:Action="http://tempuri.org/IAgendaService/DoWork" message="tns:IAgendaService_DoWork_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IAgendaService/DoWorkResponse" message="tns:IAgendaService_DoWork_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="LoadAgenda">
      <wsdl:input wsaw:Action="http://tempuri.org/IAgendaService/LoadAgenda" message="tns:IAgendaService_LoadAgenda_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IAgendaService/LoadAgendaResponse" message="tns:IAgendaService_LoadAgenda_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="LoadCreneauxByDate">
      <wsdl:input wsaw:Action="http://tempuri.org/IAgendaService/LoadCreneauxByDate" message="tns:IAgendaService_LoadCreneauxByDate_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IAgendaService/LoadCreneauxByDateResponse" message="tns:IAgendaService_LoadCreneauxByDate_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="CreneauxByHoraire">
      <wsdl:input wsaw:Action="http://tempuri.org/IAgendaService/CreneauxByHoraire" message="tns:IAgendaService_CreneauxByHoraire_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IAgendaService/CreneauxByHoraireResponse" message="tns:IAgendaService_CreneauxByHoraire_OutputMessage" />
    </wsdl:operation>
    <wsdl:operation name="ListeTimeCreneau">
      <wsdl:input wsaw:Action="http://tempuri.org/IAgendaService/ListeTimeCreneau" message="tns:IAgendaService_ListeTimeCreneau_InputMessage" />
      <wsdl:output wsaw:Action="http://tempuri.org/IAgendaService/ListeTimeCreneauResponse" message="tns:IAgendaService_ListeTimeCreneau_OutputMessage" />
    </wsdl:operation>
  </wsdl:portType>
  <wsdl:binding name="BasicHttpBinding_IAgendaService" type="tns:IAgendaService">
    <soap:binding transport="http://schemas.xmlsoap.org/soap/http" />
    <wsdl:operation name="DoWork">
      <soap:operation soapAction="http://tempuri.org/IAgendaService/DoWork" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LoadAgenda">
      <soap:operation soapAction="http://tempuri.org/IAgendaService/LoadAgenda" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="LoadCreneauxByDate">
      <soap:operation soapAction="http://tempuri.org/IAgendaService/LoadCreneauxByDate" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="CreneauxByHoraire">
      <soap:operation soapAction="http://tempuri.org/IAgendaService/CreneauxByHoraire" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
    <wsdl:operation name="ListeTimeCreneau">
      <soap:operation soapAction="http://tempuri.org/IAgendaService/ListeTimeCreneau" style="document" />
      <wsdl:input>
        <soap:body use="literal" />
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal" />
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="AgendaService">
    <wsdl:port name="BasicHttpBinding_IAgendaService" binding="tns:BasicHttpBinding_IAgendaService">
      <soap:address location="http://localhost:60827/Wcf/AgendaService.svc" />
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>