﻿//------------------------------------------------------------------------------
// <auto-generated>
//     Ce code a été généré par un outil.
//     Version du runtime :4.0.30319.42000
//
//     Les modifications apportées à ce fichier peuvent provoquer un comportement incorrect et seront perdues si
//     le code est régénéré.
// </auto-generated>
//------------------------------------------------------------------------------

namespace WindowsFormsApp1.ServiceMetierPatient {
    using System.Runtime.Serialization;
    using System;
    
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Patient", Namespace="http://schemas.datacontract.org/2004/07/MetierRvMedical.Model")]
    [System.SerializableAttribute()]
    public partial class Patient : WindowsFormsApp1.ServiceMetierPatient.Personne {
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private System.Nullable<System.DateTime> DateNaissanceField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private WindowsFormsApp1.ServiceMetierPatient.GroupeSanguin GroupeSanguinField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private int IdGroupeSanguinField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private System.Nullable<float> PoidsField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private System.Nullable<float> TailleField;
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<System.DateTime> DateNaissance {
            get {
                return this.DateNaissanceField;
            }
            set {
                if ((this.DateNaissanceField.Equals(value) != true)) {
                    this.DateNaissanceField = value;
                    this.RaisePropertyChanged("DateNaissance");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public WindowsFormsApp1.ServiceMetierPatient.GroupeSanguin GroupeSanguin {
            get {
                return this.GroupeSanguinField;
            }
            set {
                if ((object.ReferenceEquals(this.GroupeSanguinField, value) != true)) {
                    this.GroupeSanguinField = value;
                    this.RaisePropertyChanged("GroupeSanguin");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int IdGroupeSanguin {
            get {
                return this.IdGroupeSanguinField;
            }
            set {
                if ((this.IdGroupeSanguinField.Equals(value) != true)) {
                    this.IdGroupeSanguinField = value;
                    this.RaisePropertyChanged("IdGroupeSanguin");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<float> Poids {
            get {
                return this.PoidsField;
            }
            set {
                if ((this.PoidsField.Equals(value) != true)) {
                    this.PoidsField = value;
                    this.RaisePropertyChanged("Poids");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public System.Nullable<float> Taille {
            get {
                return this.TailleField;
            }
            set {
                if ((this.TailleField.Equals(value) != true)) {
                    this.TailleField = value;
                    this.RaisePropertyChanged("Taille");
                }
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Personne", Namespace="http://schemas.datacontract.org/2004/07/MetierRvMedical.Model")]
    [System.SerializableAttribute()]
    [System.Runtime.Serialization.KnownTypeAttribute(typeof(WindowsFormsApp1.ServiceMetierPatient.Patient))]
    public partial class Personne : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string AdresseField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string EmailField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private int IdUField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string NomPrenomField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string TELField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Adresse {
            get {
                return this.AdresseField;
            }
            set {
                if ((object.ReferenceEquals(this.AdresseField, value) != true)) {
                    this.AdresseField = value;
                    this.RaisePropertyChanged("Adresse");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Email {
            get {
                return this.EmailField;
            }
            set {
                if ((object.ReferenceEquals(this.EmailField, value) != true)) {
                    this.EmailField = value;
                    this.RaisePropertyChanged("Email");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int IdU {
            get {
                return this.IdUField;
            }
            set {
                if ((this.IdUField.Equals(value) != true)) {
                    this.IdUField = value;
                    this.RaisePropertyChanged("IdU");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string NomPrenom {
            get {
                return this.NomPrenomField;
            }
            set {
                if ((object.ReferenceEquals(this.NomPrenomField, value) != true)) {
                    this.NomPrenomField = value;
                    this.RaisePropertyChanged("NomPrenom");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string TEL {
            get {
                return this.TELField;
            }
            set {
                if ((object.ReferenceEquals(this.TELField, value) != true)) {
                    this.TELField = value;
                    this.RaisePropertyChanged("TEL");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="GroupeSanguin", Namespace="http://schemas.datacontract.org/2004/07/MetierRvMedical.Model")]
    [System.SerializableAttribute()]
    public partial class GroupeSanguin : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string CodeGroupeSanguinField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private int IdGroupeSanguinField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string NomGroupeSanguinField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string CodeGroupeSanguin {
            get {
                return this.CodeGroupeSanguinField;
            }
            set {
                if ((object.ReferenceEquals(this.CodeGroupeSanguinField, value) != true)) {
                    this.CodeGroupeSanguinField = value;
                    this.RaisePropertyChanged("CodeGroupeSanguin");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int IdGroupeSanguin {
            get {
                return this.IdGroupeSanguinField;
            }
            set {
                if ((this.IdGroupeSanguinField.Equals(value) != true)) {
                    this.IdGroupeSanguinField = value;
                    this.RaisePropertyChanged("IdGroupeSanguin");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string NomGroupeSanguin {
            get {
                return this.NomGroupeSanguinField;
            }
            set {
                if ((object.ReferenceEquals(this.NomGroupeSanguinField, value) != true)) {
                    this.NomGroupeSanguinField = value;
                    this.RaisePropertyChanged("NomGroupeSanguin");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ServiceModel.ServiceContractAttribute(ConfigurationName="ServiceMetierPatient.IPatientService")]
    public interface IPatientService {
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IPatientService/GetListePatients", ReplyAction="http://tempuri.org/IPatientService/GetListePatientsResponse")]
        WindowsFormsApp1.ServiceMetierPatient.Patient[] GetListePatients();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IPatientService/GetListePatients", ReplyAction="http://tempuri.org/IPatientService/GetListePatientsResponse")]
        System.Threading.Tasks.Task<WindowsFormsApp1.ServiceMetierPatient.Patient[]> GetListePatientsAsync();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IPatientService/AddPatient", ReplyAction="http://tempuri.org/IPatientService/AddPatientResponse")]
        bool AddPatient(WindowsFormsApp1.ServiceMetierPatient.Patient patient);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IPatientService/AddPatient", ReplyAction="http://tempuri.org/IPatientService/AddPatientResponse")]
        System.Threading.Tasks.Task<bool> AddPatientAsync(WindowsFormsApp1.ServiceMetierPatient.Patient patient);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IPatientService/UpdatePatient", ReplyAction="http://tempuri.org/IPatientService/UpdatePatientResponse")]
        bool UpdatePatient(WindowsFormsApp1.ServiceMetierPatient.Patient patient);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IPatientService/UpdatePatient", ReplyAction="http://tempuri.org/IPatientService/UpdatePatientResponse")]
        System.Threading.Tasks.Task<bool> UpdatePatientAsync(WindowsFormsApp1.ServiceMetierPatient.Patient patient);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IPatientService/RemovePatient", ReplyAction="http://tempuri.org/IPatientService/RemovePatientResponse")]
        bool RemovePatient(WindowsFormsApp1.ServiceMetierPatient.Patient patient);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IPatientService/RemovePatient", ReplyAction="http://tempuri.org/IPatientService/RemovePatientResponse")]
        System.Threading.Tasks.Task<bool> RemovePatientAsync(WindowsFormsApp1.ServiceMetierPatient.Patient patient);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IPatientService/GetListeGroupesSanguins", ReplyAction="http://tempuri.org/IPatientService/GetListeGroupesSanguinsResponse")]
        WindowsFormsApp1.ServiceMetierPatient.GroupeSanguin[] GetListeGroupesSanguins();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IPatientService/GetListeGroupesSanguins", ReplyAction="http://tempuri.org/IPatientService/GetListeGroupesSanguinsResponse")]
        System.Threading.Tasks.Task<WindowsFormsApp1.ServiceMetierPatient.GroupeSanguin[]> GetListeGroupesSanguinsAsync();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IPatientService/ResearchPatient", ReplyAction="http://tempuri.org/IPatientService/ResearchPatientResponse")]
        WindowsFormsApp1.ServiceMetierPatient.Patient ResearchPatient(string phoneNumberInput);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IPatientService/ResearchPatient", ReplyAction="http://tempuri.org/IPatientService/ResearchPatientResponse")]
        System.Threading.Tasks.Task<WindowsFormsApp1.ServiceMetierPatient.Patient> ResearchPatientAsync(string phoneNumberInput);
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    public interface IPatientServiceChannel : WindowsFormsApp1.ServiceMetierPatient.IPatientService, System.ServiceModel.IClientChannel {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    public partial class PatientServiceClient : System.ServiceModel.ClientBase<WindowsFormsApp1.ServiceMetierPatient.IPatientService>, WindowsFormsApp1.ServiceMetierPatient.IPatientService {
        
        public PatientServiceClient() {
        }
        
        public PatientServiceClient(string endpointConfigurationName) : 
                base(endpointConfigurationName) {
        }
        
        public PatientServiceClient(string endpointConfigurationName, string remoteAddress) : 
                base(endpointConfigurationName, remoteAddress) {
        }
        
        public PatientServiceClient(string endpointConfigurationName, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(endpointConfigurationName, remoteAddress) {
        }
        
        public PatientServiceClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(binding, remoteAddress) {
        }
        
        public WindowsFormsApp1.ServiceMetierPatient.Patient[] GetListePatients() {
            return base.Channel.GetListePatients();
        }
        
        public System.Threading.Tasks.Task<WindowsFormsApp1.ServiceMetierPatient.Patient[]> GetListePatientsAsync() {
            return base.Channel.GetListePatientsAsync();
        }
        
        public bool AddPatient(WindowsFormsApp1.ServiceMetierPatient.Patient patient) {
            return base.Channel.AddPatient(patient);
        }
        
        public System.Threading.Tasks.Task<bool> AddPatientAsync(WindowsFormsApp1.ServiceMetierPatient.Patient patient) {
            return base.Channel.AddPatientAsync(patient);
        }
        
        public bool UpdatePatient(WindowsFormsApp1.ServiceMetierPatient.Patient patient) {
            return base.Channel.UpdatePatient(patient);
        }
        
        public System.Threading.Tasks.Task<bool> UpdatePatientAsync(WindowsFormsApp1.ServiceMetierPatient.Patient patient) {
            return base.Channel.UpdatePatientAsync(patient);
        }
        
        public bool RemovePatient(WindowsFormsApp1.ServiceMetierPatient.Patient patient) {
            return base.Channel.RemovePatient(patient);
        }
        
        public System.Threading.Tasks.Task<bool> RemovePatientAsync(WindowsFormsApp1.ServiceMetierPatient.Patient patient) {
            return base.Channel.RemovePatientAsync(patient);
        }
        
        public WindowsFormsApp1.ServiceMetierPatient.GroupeSanguin[] GetListeGroupesSanguins() {
            return base.Channel.GetListeGroupesSanguins();
        }
        
        public System.Threading.Tasks.Task<WindowsFormsApp1.ServiceMetierPatient.GroupeSanguin[]> GetListeGroupesSanguinsAsync() {
            return base.Channel.GetListeGroupesSanguinsAsync();
        }
        
        public WindowsFormsApp1.ServiceMetierPatient.Patient ResearchPatient(string phoneNumberInput) {
            return base.Channel.ResearchPatient(phoneNumberInput);
        }
        
        public System.Threading.Tasks.Task<WindowsFormsApp1.ServiceMetierPatient.Patient> ResearchPatientAsync(string phoneNumberInput) {
            return base.Channel.ResearchPatientAsync(phoneNumberInput);
        }
    }
}
