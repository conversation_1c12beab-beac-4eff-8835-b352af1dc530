## About

Provides the System.Runtime.CompilerServices.Unsafe class, which provides generic, low-level functionality for manipulating pointers and managed byrefs.

## Main Types

The main types provided by this library are:

- System.Runtime.CompilerServices.Unsafe

## Additional Documentation

- API reference can be found in: https://learn.microsoft.com/en-us/dotnet/api/system.runtime.compilerservices.unsafe

## License

System.Runtime.CompilerServices.Unsafe is released as open source under the [MIT license](https://licenses.nuget.org/MIT).
