<?xml version="1.0"?>
<doc>
    <assembly>
        <name>System.Data.SqlClient</name>
    </assembly>
    <members>
        <member name="T:System.Data.SqlClient.ApplicationIntent">
            <summary>
            represents the application workload type when connecting to a server
            </summary>
        </member>
        <member name="T:System.Data.SqlClient.SqlClientDiagnosticListenerExtensions">
            <summary>
            Extension methods on the DiagnosticListener class to log SqlCommand data
            </summary>
        </member>
        <member name="P:System.Data.SqlClient.SqlCommandBuilder.CatalogLocation">
            <devnote>SqlServer only supports CatalogLocation.Start</devnote>
        </member>
        <member name="P:System.Data.SqlClient.SqlCommandBuilder.CatalogSeparator">
            <devnote>SqlServer only supports '.'</devnote>
        </member>
        <member name="P:System.Data.SqlClient.SqlCommandBuilder.QuotePrefix">
            <devnote>SqlServer only supports '.'</devnote>
        </member>
        <member name="M:System.Data.SqlClient.MetaType.AssertIsUserDefinedTypeInstance(System.Object,System.String)">
             <summary>
             Assert that the supplied object is an instance of a SQL User-Defined Type (UDT).
             </summary>
             <param name="sqlValue">Object instance to be tested.</param>
             <param name="failedAssertMessage">Failed assert message</param>
             <remarks>
             This method is only compiled with debug builds, and it a helper method for the GetComValueFromSqlVariant method defined in this class.
            
             The presence of the SqlUserDefinedTypeAttribute on the object's type
             is used to determine if the object is a UDT instance (if present it is a UDT, else it is not).
             </remarks>
             <exception cref="T:System.NullReferenceException">
             If sqlValue is null.  Callers must ensure the object is non-null.
             </exception>
        </member>
        <member name="T:System.Data.SqlClient.SqlInfoMessageEventHandler">
            <devdoc>
               <para>
                  Represents the method that will handle the <see cref='E:System.Data.SqlClient.SqlConnection.InfoMessage'/> event of a <see cref='T:System.Data.SqlClient.SqlConnection'/>.
               </para>
            </devdoc>
        </member>
        <member name="M:System.Data.SqlClient.SqlInternalConnectionTds.CheckEnlistedTransactionBinding">
            <summary>
            Validate the enlisted transaction state, taking into consideration the ambient transaction and transaction unbinding mode.
            If there is no enlisted transaction, this method is a nop.
            </summary>
            <remarks>
            <para>
            This method must be called while holding a lock on the SqlInternalConnection instance,
            to ensure we don't accidentally execute after the transaction has completed on a different thread, 
            causing us to unwittingly execute in auto-commit mode.
            </para>
            
            <para>
            When using Explicit transaction unbinding, 
            verify that the enlisted transaction is active and equal to the current ambient transaction.
            </para>
            
            <para>
            When using Implicit transaction unbinding,
            verify that the enlisted transaction is active.
            If it is not active, and the transaction object has been disposed, unbind from the transaction.
            If it is not active and not disposed, throw an exception.
            </para>
            </remarks>
        </member>
        <member name="M:System.Data.SqlClient.SqlSequentialStream.SetClosed">
            <summary>
            Forces the stream to act as if it was closed (i.e. CanRead=false and Read() throws)
            This does not actually close the stream, read off the rest of the data or dispose this
            </summary>
        </member>
        <member name="M:System.Data.SqlClient.SqlSequentialStream.ValidateReadParameters(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Checks the parameters passed into a Read() method are valid
            </summary>
            <param name="buffer"></param>
            <param name="offset"></param>
            <param name="count"></param>
        </member>
        <member name="M:System.Data.SqlClient.SqlSequentialTextReader.SetClosed">
            <summary>
            Forces the TextReader to act as if it was closed
            This does not actually close the stream, read off the rest of the data or dispose this
            </summary>
        </member>
        <member name="M:System.Data.SqlClient.SqlSequentialTextReader.InternalRead(System.Char[],System.Int32,System.Int32)">
            <summary>
            Performs the actual reading and converting
            NOTE: This assumes that buffer, index and count are all valid, we're not closed (!IsClosed) and that there is data left (IsDataLeft())
            </summary>
            <param name="buffer"></param>
            <param name="index"></param>
            <param name="count"></param>
            <returns></returns>
        </member>
        <member name="M:System.Data.SqlClient.SqlSequentialTextReader.PrepareByteBuffer(System.Int32,System.Int32@)">
            <summary>
            Creates a byte array large enough to store all bytes for the characters in the current encoding, then fills it with any leftover bytes
            </summary>
            <param name="numberOfChars">Number of characters that are to be read</param>
            <param name="byteBufferUsed">Number of bytes pre-filled by the leftover bytes</param>
            <returns>A byte array of the correct size, pre-filled with leftover bytes</returns>
        </member>
        <member name="M:System.Data.SqlClient.SqlSequentialTextReader.DecodeBytesToChars(System.Byte[],System.Int32,System.Char[],System.Int32,System.Int32)">
            <summary>
            Decodes the given bytes into characters, and stores the leftover bytes for later use
            </summary>
            <param name="inBuffer">Buffer of bytes to decode</param>
            <param name="inBufferCount">Number of bytes to decode from the inBuffer</param>
            <param name="outBuffer">Buffer to write the characters to</param>
            <param name="outBufferOffset">Offset to start writing to outBuffer at</param>
            <param name="outBufferCount">Maximum number of characters to decode</param>
            <returns>The actual number of characters decoded</returns>
        </member>
        <member name="P:System.Data.SqlClient.SqlSequentialTextReader.IsClosed">
            <summary>
            True if this TextReader is supposed to be closed
            </summary>
        </member>
        <member name="P:System.Data.SqlClient.SqlSequentialTextReader.HasPeekedChar">
            <summary>
            True if there is a peeked character available
            </summary>
        </member>
        <member name="M:System.Data.SqlClient.SqlSequentialTextReader.ValidateReadParameters(System.Char[],System.Int32,System.Int32)">
            <summary>
            Checks the parameters passed into a Read() method are valid
            </summary>
            <param name="buffer"></param>
            <param name="index"></param>
            <param name="count"></param>
        </member>
        <member name="M:System.Data.SqlClient.SQL.MultiSubnetFailoverWithFailoverPartner(System.Boolean,System.Data.SqlClient.SqlInternalConnectionTds)">
            <summary>
            used to block two scenarios if MultiSubnetFailover is true: 
            * server-provided failover partner - raising SqlException in this case
            * connection string with failover partner and MultiSubnetFailover=true - raising argument one in this case with the same message
            </summary>
        </member>
        <member name="M:System.Data.SqlClient.SQL.GetSNIErrorMessage(System.Int32)">
            <summary>
            gets a message for SNI error (sniError must be valid, non-zero error code)
            </summary>
        </member>
        <member name="T:System.Data.SqlClient.SqlServerEscapeHelper">
            <summary>
            This class holds helper methods to escape Microsoft SQL Server identifiers, such as table, schema, database or other names
            </summary>
        </member>
        <member name="M:System.Data.SqlClient.SqlServerEscapeHelper.EscapeIdentifier(System.String)">
            <summary>
            Escapes the identifier with square brackets. The input has to be in unescaped form, like the parts received from MultipartIdentifier.ParseMultipartIdentifier.
            </summary>
            <param name="name">name of the identifier, in unescaped form</param>
            <returns>escapes the name with [], also escapes the last close bracket with double-bracket</returns>
        </member>
        <member name="M:System.Data.SqlClient.SqlServerEscapeHelper.EscapeIdentifier(System.Text.StringBuilder,System.String)">
            <summary>
            Same as above EscapeIdentifier, except that output is written into StringBuilder
            </summary>
        </member>
        <member name="M:System.Data.SqlClient.SqlServerEscapeHelper.EscapeStringAsLiteral(System.String)">
            <summary>
             Escape a string to be used inside TSQL literal, such as N'somename' or 'somename'
            </summary>
        </member>
        <member name="M:System.Data.SqlClient.SqlServerEscapeHelper.MakeStringLiteral(System.String)">
            <summary>
            Escape a string as a TSQL literal, wrapping it around with single quotes.
            Use this method to escape input strings to prevent SQL injection 
            and to get correct behavior for embedded quotes.
            </summary>
            <param name="input">unescaped string</param>
            <returns>escaped and quoted literal string</returns>
        </member>
        <member name="T:System.Data.SqlClient.SysTxForGlobalTransactions">
            <summary>
            This class holds methods invoked on System.Transactions through reflection for Global Transactions
            </summary>
        </member>
        <member name="P:System.Data.SqlClient.SysTxForGlobalTransactions.EnlistPromotableSinglePhase">
            <summary>
            Enlists the given IPromotableSinglePhaseNotification and Non-MSDTC Promoter type into a transaction
            </summary>
            <returns>The MethodInfo instance to be invoked. Null if the method doesn't exist</returns>
        </member>
        <member name="P:System.Data.SqlClient.SysTxForGlobalTransactions.SetDistributedTransactionIdentifier">
            <summary>
            Sets the given DistributedTransactionIdentifier for a Transaction instance.
            Needs to be invoked when using a Non-MSDTC Promoter type
            </summary>
            <returns>The MethodInfo instance to be invoked. Null if the method doesn't exist</returns>
        </member>
        <member name="P:System.Data.SqlClient.SysTxForGlobalTransactions.GetPromotedToken">
            <summary>
            Gets the Promoted Token for a Transaction
            </summary>
            <returns>The MethodInfo instance to be invoked. Null if the method doesn't exist</returns>
        </member>
        <member name="T:System.Data.SqlClient.TdsEnums">
            <devdoc> Class of variables for the Tds connection.
            </devdoc>
        </member>
        <member name="M:System.Data.SqlClient.TdsParser.IsValidTdsToken(System.Byte)">
            <summary>
            Checks if the given token is a valid TDS token
            </summary>
            <param name="token">Token to check</param>
            <returns>True if the token is a valid TDS token, otherwise false</returns>
        </member>
        <member name="M:System.Data.SqlClient.TdsParser.TrySkipValue(System.Data.SqlClient.SqlMetaDataPriv,System.Int32,System.Data.SqlClient.TdsParserStateObject)">
            <summary>
            This method skips bytes of a single column value from the media. It supports NBCROW and handles all types of values, including PLP and long
            </summary>
        </member>
        <member name="T:System.Data.SqlClient.FederatedAuthenticationFeatureExtensionData">
            <summary>
            Struct encapsulating the data to be sent to the server as part of Federated Authentication Feature Extension.
            </summary>
        </member>
        <member name="F:System.Data.SqlClient.SqlEnvChange.newBinValue">
            <summary>
            contains binary data, before using this field check newBinRented to see if you can take the field array or whether you should allocate and copy
            </summary>
        </member>
        <member name="F:System.Data.SqlClient.SqlEnvChange.oldBinValue">
            <summary>
            contains binary data, before using this field check newBinRented to see if you can take the field array or whether you should allocate and copy
            </summary>
        </member>
        <member name="M:System.Data.SqlClient.TdsParserStateObject.NullBitmap.IsGuaranteedNull(System.Int32)">
            <summary>
            If this method returns true, the value is guaranteed to be null. This is not true vice versa: 
            if the bitmap value is false (if this method returns false), the value can be either null or non-null - no guarantee in this case.
            To determine whether it is null or not, read it from the TDS (per NBCROW design spec, for IMAGE/TEXT/NTEXT columns server might send
            bitmap = 0, when the actual value is null).
            </summary>
        </member>
        <member name="M:System.Data.SqlClient.TdsParserStateObject.OnTimeoutCore(System.Int32,System.Int32,System.Boolean)">
            <summary>
            attempts to change the timout state from the expected state to the target state and if it succeeds
            will setup the the stateobject into the timeout expired state
            </summary>
            <param name="expectedState">the state that is the expected current state, state will change only if this is correct</param>
            <param name="targetState">the state that will be changed to if the expected state is correct</param>
            <param name="asyncClose">any close action to be taken by an async task to avoid deadlock.</param>
            <returns>boolean value indicating whether the call changed the timeout state</returns>
        </member>
        <member name="M:System.Data.SqlClient.TdsParserStateObject.IsConnectionAlive(System.Boolean)">
            <summary>
            Checks to see if the underlying connection is still alive (used by connection pool resiliency)
            NOTE: This is not safe to do on a connection that is currently in use
            NOTE: This will mark the connection as broken if it is found to be dead
            </summary>
            <param name="throwOnException">If true then an exception will be thrown if the connection is found to be dead, otherwise no exception will be thrown</param>
            <returns>True if the connection is still alive, otherwise false</returns>
        </member>
        <member name="M:System.Data.SqlClient.TdsParserStateObject.ValidateSNIConnection">
            <summary>
            Checks to see if the underlying connection is still valid (used by idle connection resiliency - for active connections) 
            NOTE: This is not safe to do on a connection that is currently in use
            NOTE: This will mark the connection as broken if it is found to be dead
            </summary>
            <returns>True if the connection is still alive, otherwise false</returns>
        </member>
        <member name="P:System.Data.SqlClient.TdsParserStateObject.HasErrorOrWarning">
            <summary>
            True if there is at least one error or warning (not counting the pre-attention errors\warnings)
            </summary>
        </member>
        <member name="M:System.Data.SqlClient.TdsParserStateObject.AddError(System.Data.SqlClient.SqlError)">
            <summary>
            Adds an error to the error collection
            </summary>
            <param name="error"></param>
        </member>
        <member name="P:System.Data.SqlClient.TdsParserStateObject.ErrorCount">
            <summary>
            Gets the number of errors currently in the error collection
            </summary>
        </member>
        <member name="M:System.Data.SqlClient.TdsParserStateObject.AddWarning(System.Data.SqlClient.SqlError)">
            <summary>
            Adds an warning to the warning collection
            </summary>
            <param name="error"></param>
        </member>
        <member name="P:System.Data.SqlClient.TdsParserStateObject.WarningCount">
            <summary>
            Gets the number of warnings currently in the warning collection
            </summary>
        </member>
        <member name="M:System.Data.SqlClient.TdsParserStateObject.GetFullErrorAndWarningCollection(System.Boolean@)">
            <summary>
            Gets the full list of errors and warnings (including the pre-attention ones), then wipes all error and warning lists
            </summary>
            <param name="broken">If true, the connection should be broken</param>
            <returns>An array containing all of the errors and warnings</returns>
        </member>
        <member name="M:System.Data.SqlClient.TdsParserStateObject.StoreErrorAndWarningForAttention">
            <summary>
            Stores away current errors and warnings so that an attention can be processed
            </summary>
        </member>
        <member name="M:System.Data.SqlClient.TdsParserStateObject.RestoreErrorAndWarningAfterAttention">
            <summary>
            Restores errors and warnings that were stored in order to process an attention
            </summary>
        </member>
        <member name="M:System.Data.SqlClient.TdsParserStateObject.CheckThrowSNIException">
            <summary>
            Checks if an error is stored in _error and, if so, throws an error
            </summary>
        </member>
        <member name="M:System.Data.SqlClient.TdsParserStateObject.AssertStateIsClean">
            <summary>
            Debug Only: Ensures that the TdsParserStateObject has no lingering state and can safely be re-used
            </summary>
        </member>
        <member name="T:System.Data.SqlClient.SNI.SNIError">
            <summary>
            SNI error
            </summary>
        </member>
        <member name="T:System.Data.SqlClient.SNI.SNIHandle">
            <summary>
            SNI connection handle
            </summary>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIHandle.Dispose">
            <summary>
            Dispose class
            </summary>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIHandle.SetAsyncCallbacks(System.Data.SqlClient.SNI.SNIAsyncCallback,System.Data.SqlClient.SNI.SNIAsyncCallback)">
            <summary>
            Set async callbacks
            </summary>
            <param name="receiveCallback">Receive callback</param>
            <param name="sendCallback">Send callback</param>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIHandle.SetBufferSize(System.Int32)">
            <summary>
            Set buffer size
            </summary>
            <param name="bufferSize">Buffer size</param>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIHandle.Send(System.Data.SqlClient.SNI.SNIPacket)">
            <summary>
            Send a packet synchronously
            </summary>
            <param name="packet">SNI packet</param>
            <returns>SNI error code</returns>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIHandle.SendAsync(System.Data.SqlClient.SNI.SNIPacket,System.Boolean,System.Data.SqlClient.SNI.SNIAsyncCallback)">
            <summary>
            Send a packet asynchronously
            </summary>
            <param name="packet">SNI packet</param>
            <param name="disposePacketAfterSendAsync">Whether to dispose package after sending async</param>
            <param name="callback">Completion callback</param>
            <returns>SNI error code</returns>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIHandle.Receive(System.Data.SqlClient.SNI.SNIPacket@,System.Int32)">
            <summary>
            Receive a packet synchronously
            </summary>
            <param name="packet">SNI packet</param>
            <param name="timeoutInMilliseconds">Timeout in Milliseconds</param>
            <returns>SNI error code</returns>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIHandle.ReceiveAsync(System.Data.SqlClient.SNI.SNIPacket@)">
            <summary>
            Receive a packet asynchronously
            </summary>
            <param name="packet">SNI packet</param>
            <returns>SNI error code</returns>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIHandle.EnableSsl(System.UInt32)">
            <summary>
            Enable SSL
            </summary>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIHandle.DisableSsl">
            <summary>
            Disable SSL
            </summary>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIHandle.CheckConnection">
            <summary>
            Check connection status
            </summary>
            <returns>SNI error code</returns>
        </member>
        <member name="P:System.Data.SqlClient.SNI.SNIHandle.Status">
            <summary>
            Last handle status
            </summary>
        </member>
        <member name="P:System.Data.SqlClient.SNI.SNIHandle.ConnectionId">
            <summary>
            Connection ID
            </summary>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIHandle.KillConnection">
            <summary>
            Test handle for killing underlying connection
            </summary>
        </member>
        <member name="T:System.Data.SqlClient.SNI.SNILoadHandle">
            <summary>
            Global SNI settings and status
            </summary>
        </member>
        <member name="P:System.Data.SqlClient.SNI.SNILoadHandle.LastError">
            <summary>
            Last SNI error
            </summary>
        </member>
        <member name="P:System.Data.SqlClient.SNI.SNILoadHandle.Status">
            <summary>
            SNI library status
            </summary>
        </member>
        <member name="P:System.Data.SqlClient.SNI.SNILoadHandle.Options">
            <summary>
            Encryption options setting
            </summary>
        </member>
        <member name="T:System.Data.SqlClient.SNI.SNIMarsConnection">
            <summary>
            SNI MARS connection. Multiple MARS streams will be overlaid on this connection.
            </summary>
        </member>
        <member name="P:System.Data.SqlClient.SNI.SNIMarsConnection.ConnectionId">
            <summary>
            Connection ID
            </summary>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIMarsConnection.#ctor(System.Data.SqlClient.SNI.SNIHandle)">
            <summary>
            Constructor
            </summary>
            <param name="lowerHandle">Lower handle</param>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIMarsConnection.StartReceive">
            <summary>
            Start receiving
            </summary>
            <returns></returns>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIMarsConnection.Send(System.Data.SqlClient.SNI.SNIPacket)">
            <summary>
            Send a packet synchronously
            </summary>
            <param name="packet">SNI packet</param>
            <returns>SNI error code</returns>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIMarsConnection.SendAsync(System.Data.SqlClient.SNI.SNIPacket,System.Data.SqlClient.SNI.SNIAsyncCallback)">
            <summary>
            Send a packet asynchronously
            </summary>
            <param name="packet">SNI packet</param>
            <param name="callback">Completion callback</param>
            <returns>SNI error code</returns>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIMarsConnection.ReceiveAsync(System.Data.SqlClient.SNI.SNIPacket@)">
            <summary>
            Receive a packet asynchronously
            </summary>
            <param name="packet">SNI packet</param>
            <returns>SNI error code</returns>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIMarsConnection.CheckConnection">
            <summary>
            Check SNI handle connection
            </summary>
            <returns>SNI error status</returns>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIMarsConnection.HandleReceiveError(System.Data.SqlClient.SNI.SNIPacket)">
            <summary>
            Process a receive error
            </summary>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIMarsConnection.HandleSendComplete(System.Data.SqlClient.SNI.SNIPacket,System.UInt32)">
            <summary>
            Process a send completion
            </summary>
            <param name="packet">SNI packet</param>
            <param name="sniErrorCode">SNI error code</param>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIMarsConnection.HandleReceiveComplete(System.Data.SqlClient.SNI.SNIPacket,System.UInt32)">
            <summary>
            Process a receive completion
            </summary>
            <param name="packet">SNI packet</param>
            <param name="sniErrorCode">SNI error code</param>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIMarsConnection.EnableSsl(System.UInt32)">
            <summary>
            Enable SSL
            </summary>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIMarsConnection.DisableSsl">
            <summary>
            Disable SSL
            </summary>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIMarsConnection.KillConnection">
            <summary>
            Test handle for killing underlying connection
            </summary>
        </member>
        <member name="T:System.Data.SqlClient.SNI.SNIMarsHandle">
            <summary>
            MARS handle
            </summary>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIMarsHandle.Dispose">
            <summary>
            Dispose object
            </summary>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIMarsHandle.#ctor(System.Data.SqlClient.SNI.SNIMarsConnection,System.UInt16,System.Object,System.Boolean)">
            <summary>
            Constructor
            </summary>
            <param name="connection">MARS connection</param>
            <param name="sessionId">MARS session ID</param>
            <param name="callbackObject">Callback object</param>
            <param name="async">true if connection is asynchronous</param>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIMarsHandle.SendControlPacket(System.Data.SqlClient.SNI.SNISMUXFlags)">
            <summary>
            Send control packet
            </summary>
            <param name="flags">SMUX header flags</param>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIMarsHandle.SetPacketSMUXHeader(System.Data.SqlClient.SNI.SNIPacket)">
            <summary>
            Generate a packet with SMUX header
            </summary>
            <param name="packet">SNI packet</param>
            <returns>The packet with the SMUx header set.</returns>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIMarsHandle.Send(System.Data.SqlClient.SNI.SNIPacket)">
            <summary>
            Send a packet synchronously
            </summary>
            <param name="packet">SNI packet</param>
            <returns>SNI error code</returns>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIMarsHandle.InternalSendAsync(System.Data.SqlClient.SNI.SNIPacket,System.Data.SqlClient.SNI.SNIAsyncCallback)">
            <summary>
            Send packet asynchronously
            </summary>
            <param name="packet">SNI packet</param>
            <param name="callback">Completion callback</param>
            <returns>SNI error code</returns>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIMarsHandle.SendPendingPackets">
            <summary>
            Send pending packets
            </summary>
            <returns>SNI error code</returns>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIMarsHandle.SendAsync(System.Data.SqlClient.SNI.SNIPacket,System.Boolean,System.Data.SqlClient.SNI.SNIAsyncCallback)">
            <summary>
            Send a packet asynchronously
            </summary>
            <param name="packet">SNI packet</param>
            <param name="disposePacketAfterSendAsync">Whether to dispose packet after sending async</param>
            <param name="callback">Completion callback</param>
            <returns>SNI error code</returns>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIMarsHandle.ReceiveAsync(System.Data.SqlClient.SNI.SNIPacket@)">
            <summary>
            Receive a packet asynchronously
            </summary>
            <param name="packet">SNI packet</param>
            <returns>SNI error code</returns>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIMarsHandle.HandleReceiveError(System.Data.SqlClient.SNI.SNIPacket)">
            <summary>
            Handle receive error
            </summary>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIMarsHandle.HandleSendComplete(System.Data.SqlClient.SNI.SNIPacket,System.UInt32)">
            <summary>
            Handle send completion
            </summary>
            <param name="packet">SNI packet</param>
            <param name="sniErrorCode">SNI error code</param>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIMarsHandle.HandleAck(System.UInt32)">
            <summary>
            Handle SMUX acknowledgement
            </summary>
            <param name="highwater">Send highwater mark</param>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIMarsHandle.HandleReceiveComplete(System.Data.SqlClient.SNI.SNIPacket,System.Data.SqlClient.SNI.SNISMUXHeader)">
            <summary>
            Handle receive completion
            </summary>
            <param name="packet">SNI packet</param>
            <param name="header">SMUX header</param>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIMarsHandle.SendAckIfNecessary">
            <summary>
            Send ACK if we've hit highwater threshold
            </summary>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIMarsHandle.Receive(System.Data.SqlClient.SNI.SNIPacket@,System.Int32)">
            <summary>
            Receive a packet synchronously
            </summary>
            <param name="packet">SNI packet</param>
            <param name="timeoutInMilliseconds">Timeout in Milliseconds</param>
            <returns>SNI error code</returns>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIMarsHandle.CheckConnection">
            <summary>
            Check SNI handle connection
            </summary>
            <returns>SNI error status</returns>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIMarsHandle.SetAsyncCallbacks(System.Data.SqlClient.SNI.SNIAsyncCallback,System.Data.SqlClient.SNI.SNIAsyncCallback)">
            <summary>
            Set async callbacks
            </summary>
            <param name="receiveCallback">Receive callback</param>
            <param name="sendCallback">Send callback</param>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIMarsHandle.SetBufferSize(System.Int32)">
            <summary>
            Set buffer size
            </summary>
            <param name="bufferSize">Buffer size</param>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIMarsHandle.EnableSsl(System.UInt32)">
            <summary>
            Enable SSL
            </summary>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIMarsHandle.DisableSsl">
            <summary>
            Disable SSL
            </summary>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIMarsHandle.KillConnection">
            <summary>
            Test handle for killing underlying connection
            </summary>
        </member>
        <member name="T:System.Data.SqlClient.SNI.SNIMarsQueuedPacket">
            <summary>
            Mars queued packet
            </summary>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIMarsQueuedPacket.#ctor(System.Data.SqlClient.SNI.SNIPacket,System.Data.SqlClient.SNI.SNIAsyncCallback)">
            <summary>
            Constructor
            </summary>
            <param name="packet">SNI packet</param>
            <param name="callback">Completion callback</param>
        </member>
        <member name="P:System.Data.SqlClient.SNI.SNIMarsQueuedPacket.Packet">
            <summary>
            SNI packet
            </summary>
        </member>
        <member name="P:System.Data.SqlClient.SNI.SNIMarsQueuedPacket.Callback">
            <summary>
            Completion callback
            </summary>
        </member>
        <member name="T:System.Data.SqlClient.SNI.SNINpHandle">
            <summary>
            Named Pipe connection handle
            </summary>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNINpHandle.ValidateServerCertificate(System.Object,System.Security.Cryptography.X509Certificates.X509Certificate,System.Security.Cryptography.X509Certificates.X509Chain,System.Net.Security.SslPolicyErrors)">
            <summary>
            Validate server certificate
            </summary>
            <param name="sender">Sender object</param>
            <param name="cert">X.509 certificate</param>
            <param name="chain">X.509 chain</param>
            <param name="policyErrors">Policy errors</param>
            <returns>true if valid</returns>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNINpHandle.SetBufferSize(System.Int32)">
            <summary>
            Set buffer size
            </summary>
            <param name="bufferSize">Buffer size</param>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNINpHandle.KillConnection">
            <summary>
            Test handle for killing underlying connection
            </summary>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIPacket.Dispose">
            <summary>
            Dispose Packet data
            </summary>
        </member>
        <member name="P:System.Data.SqlClient.SNI.SNIPacket.DataLeft">
            <summary>
            Length of data left to process
            </summary>
        </member>
        <member name="P:System.Data.SqlClient.SNI.SNIPacket.Length">
            <summary>
            Length of data
            </summary>
        </member>
        <member name="P:System.Data.SqlClient.SNI.SNIPacket.IsInvalid">
            <summary>
            Packet validity
            </summary>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIPacket.SetCompletionCallback(System.Data.SqlClient.SNI.SNIAsyncCallback)">
            <summary>
            Set async completion callback
            </summary>
            <param name="completionCallback">Completion callback</param>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIPacket.InvokeCompletionCallback(System.UInt32)">
            <summary>
            Invoke the completion callback 
            </summary>
            <param name="sniErrorCode">SNI error</param>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIPacket.Allocate(System.Int32,System.Int32)">
            <summary>
            Allocate space for data
            </summary>
            <param name="headerLength">Length of header</param>
            <param name="dataLength">Length of byte array to be allocated</param>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIPacket.GetData(System.Byte[],System.Int32@)">
            <summary>
            Read packet data into a buffer without removing it from the packet
            </summary>
            <param name="buffer">Buffer</param>
            <param name="dataSize">Number of bytes read from the packet into the buffer</param>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIPacket.TakeData(System.Data.SqlClient.SNI.SNIPacket,System.Int32)">
            <summary>
            Take data from another packet
            </summary>
            <param name="packet">Packet</param>
            <param name="size">Data to take</param>
            <returns>Amount of data taken</returns>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIPacket.AppendData(System.Byte[],System.Int32)">
            <summary>
            Append data
            </summary>
            <param name="data">Data</param>
            <param name="size">Size</param>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIPacket.TakeData(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Read data from the packet into the buffer at dataOffset for zize and then remove that data from the packet
            </summary>
            <param name="buffer">Buffer</param>
            <param name="dataOffset">Data offset to write data at</param>
            <param name="size">Number of bytes to read from the packet into the buffer</param>
            <returns></returns>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIPacket.Release">
            <summary>
            Release packet
            </summary>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIPacket.ReadFromStreamAsync(System.IO.Stream,System.Data.SqlClient.SNI.SNIAsyncCallback)">
            <summary>
            Read data from a stream asynchronously
            </summary>
            <param name="stream">Stream to read from</param>
            <param name="callback">Completion callback</param>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIPacket.ReadFromStream(System.IO.Stream)">
            <summary>
            Read data from a stream synchronously
            </summary>
            <param name="stream">Stream to read from</param>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIPacket.WriteToStreamAsync(System.IO.Stream,System.Data.SqlClient.SNI.SNIAsyncCallback,System.Data.SqlClient.SNI.SNIProviders,System.Boolean)">
            <summary>
            Write data to a stream asynchronously
            </summary>
            <param name="stream">Stream to write to</param>
            <param name="callback">SNI Asynchronous Callback</param>
            <param name="provider">SNI provider identifier</param>
            <param name="disposeAfterWriteAsync">Bool flag to decide whether or not to dispose after Write Async operation</param>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIPacket.WriteToStream(System.IO.Stream)">
            <summary>
            Write data to a stream synchronously
            </summary>
            <param name="stream">Stream to write to</param>
        </member>
        <member name="T:System.Data.SqlClient.SNI.SNIProxy">
            <summary>
            Managed SNI proxy implementation. Contains many SNI entry points used by SqlClient.
            </summary>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIProxy.Terminate">
            <summary>
            Terminate SNI
            </summary>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIProxy.EnableSsl(System.Data.SqlClient.SNI.SNIHandle,System.UInt32)">
            <summary>
            Enable SSL on a connection
            </summary>
            <param name="handle">Connection handle</param>
            <param name="options">SSL options</param>
            <returns>SNI error code</returns>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIProxy.DisableSsl(System.Data.SqlClient.SNI.SNIHandle)">
            <summary>
            Disable SSL on a connection
            </summary>
            <param name="handle">Connection handle</param>
            <returns>SNI error code</returns>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIProxy.GenSspiClientContext(System.Data.SqlClient.SNI.SspiClientContextStatus,System.Byte[],System.Byte[]@,System.Byte[])">
            <summary>
            Generate SSPI context
            </summary>
            <param name="sspiClientContextStatus">SSPI client context status</param>
            <param name="receivedBuff">Receive buffer</param>
            <param name="sendBuff">Send buffer</param>
            <param name="serverName">Service Principal Name buffer</param>
            <returns>SNI error code</returns>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIProxy.InitializeSspiPackage(System.UInt32@)">
            <summary>
            Initialize SSPI
            </summary>
            <param name="maxLength">Max length of SSPI packet</param>
            <returns>SNI error code</returns>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIProxy.SetConnectionBufferSize(System.Data.SqlClient.SNI.SNIHandle,System.UInt32)">
            <summary>
            Set connection buffer size
            </summary>
            <param name="handle">SNI handle</param>
            <param name="bufferSize">Buffer size</param>
            <returns>SNI error code</returns>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIProxy.PacketGetData(System.Data.SqlClient.SNI.SNIPacket,System.Byte[],System.UInt32@)">
            <summary>
            Copies data in SNIPacket to given byte array parameter
            </summary>
            <param name="packet">SNIPacket object containing data packets</param>
            <param name="inBuff">Destination byte array where data packets are copied to</param>
            <param name="dataSize">Length of data packets</param>
            <returns>SNI error status</returns>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIProxy.ReadSyncOverAsync(System.Data.SqlClient.SNI.SNIHandle,System.Data.SqlClient.SNI.SNIPacket@,System.Int32)">
            <summary>
            Read synchronously
            </summary>
            <param name="handle">SNI handle</param>
            <param name="packet">SNI packet</param>
            <param name="timeout">Timeout</param>
            <returns>SNI error status</returns>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIProxy.GetConnectionId(System.Data.SqlClient.SNI.SNIHandle,System.Guid@)">
            <summary>
            Get SNI connection ID
            </summary>
            <param name="handle">SNI handle</param>
            <param name="clientConnectionId">Client connection ID</param>
            <returns>SNI error status</returns>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIProxy.WritePacket(System.Data.SqlClient.SNI.SNIHandle,System.Data.SqlClient.SNI.SNIPacket,System.Boolean)">
            <summary>
            Send a packet
            </summary>
            <param name="handle">SNI handle</param>
            <param name="packet">SNI packet</param>
            <param name="sync">true if synchronous, false if asynchronous</param>
            <returns>SNI error status</returns>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIProxy.CreateConnectionHandle(System.Object,System.String,System.Boolean,System.Int64,System.Byte[]@,System.Byte[]@,System.Boolean,System.Boolean,System.Boolean,System.Boolean)">
            <summary>
            Create a SNI connection handle
            </summary>
            <param name="callbackObject">Asynchronous I/O callback object</param>
            <param name="fullServerName">Full server name from connection string</param>
            <param name="ignoreSniOpenTimeout">Ignore open timeout</param>
            <param name="timerExpire">Timer expiration</param>
            <param name="instanceName">Instance name</param>
            <param name="spnBuffer">SPN</param>
            <param name="flushCache">Flush packet cache</param>
            <param name="async">Asynchronous connection</param>
            <param name="parallel">Attempt parallel connects</param>
            <param name="isIntegratedSecurity">Whether integrated security is enabled</param>
            <returns>SNI handle</returns>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIProxy.CreateTcpHandle(System.Data.SqlClient.SNI.DataSource,System.Int64,System.Object,System.Boolean)">
            <summary>
            Creates an SNITCPHandle object
            </summary>
            <param name="details">Data source</param>
            <param name="timerExpire">Timer expiration</param>
            <param name="callbackObject">Asynchronous I/O callback object</param>
            <param name="parallel">Should MultiSubnetFailover be used</param>
            <returns>SNITCPHandle</returns>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIProxy.CreateNpHandle(System.Data.SqlClient.SNI.DataSource,System.Int64,System.Object,System.Boolean)">
            <summary>
            Creates an SNINpHandle object
            </summary>
            <param name="details">Data source</param>
            <param name="timerExpire">Timer expiration</param>
            <param name="callbackObject">Asynchronous I/O callback object</param>
            <param name="parallel">Should MultiSubnetFailover be used. Only returns an error for named pipes.</param>
            <returns>SNINpHandle</returns>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIProxy.ReadAsync(System.Data.SqlClient.SNI.SNIHandle,System.Data.SqlClient.SNI.SNIPacket@)">
            <summary>
            Read packet asynchronously
            </summary>
            <param name="handle">SNI handle</param>
            <param name="packet">Packet</param>
            <returns>SNI error status</returns>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIProxy.PacketSetData(System.Data.SqlClient.SNI.SNIPacket,System.Byte[],System.Int32)">
            <summary>
            Set packet data
            </summary>
            <param name="packet">SNI packet</param>
            <param name="data">Data</param>
            <param name="length">Length</param>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIProxy.PacketRelease(System.Data.SqlClient.SNI.SNIPacket)">
            <summary>
            Release packet
            </summary>
            <param name="packet">SNI packet</param>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIProxy.CheckConnection(System.Data.SqlClient.SNI.SNIHandle)">
            <summary>
            Check SNI handle connection
            </summary>
            <param name="handle"></param>
            <returns>SNI error status</returns>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIProxy.GetLastError">
            <summary>
            Get last SNI error on this thread
            </summary>
            <returns></returns>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNIProxy.GetLocalDBDataSource(System.String,System.Boolean@)">
            <summary>
            Gets the Local db Named pipe data source if the input is a localDB server. 
            </summary>
            <param name="fullServerName">The data source</param>
            <param name="error">Set true when an error occurred while getting LocalDB up</param>
            <returns></returns>
        </member>
        <member name="P:System.Data.SqlClient.SNI.DataSource.ServerName">
            <summary>
            Provides the HostName of the server to connect to for TCP protocol. 
            This information is also used for finding the SPN of SqlServer
            </summary>
        </member>
        <member name="P:System.Data.SqlClient.SNI.DataSource.Port">
            <summary>
            Provides the port on which the TCP connection should be made if one was specified in Data Source
            </summary>
        </member>
        <member name="P:System.Data.SqlClient.SNI.DataSource.InstanceName">
            <summary>
            Provides the inferred Instance Name from Server Data Source
            </summary>
        </member>
        <member name="P:System.Data.SqlClient.SNI.DataSource.PipeName">
            <summary>
            Provides the pipe name in case of Named Pipes
            </summary>
        </member>
        <member name="P:System.Data.SqlClient.SNI.DataSource.PipeHostName">
            <summary>
            Provides the HostName to connect to in case of Named pipes Data Source
            </summary>
        </member>
        <member name="T:System.Data.SqlClient.SNI.SNITCPHandle">
            <summary>
            TCP connection handle
            </summary>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNITCPHandle.Dispose">
            <summary>
            Dispose object
            </summary>
        </member>
        <member name="P:System.Data.SqlClient.SNI.SNITCPHandle.ConnectionId">
            <summary>
            Connection ID
            </summary>
        </member>
        <member name="P:System.Data.SqlClient.SNI.SNITCPHandle.Status">
            <summary>
            Connection status
            </summary>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNITCPHandle.#ctor(System.String,System.Int32,System.Int64,System.Object,System.Boolean)">
            <summary>
            Constructor
            </summary>
            <param name="serverName">Server name</param>
            <param name="port">TCP port number</param>
            <param name="timerExpire">Connection timer expiration</param>
            <param name="callbackObject">Callback object</param>
            <param name="parallel">Open in parallel</param>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNITCPHandle.EnableSsl(System.UInt32)">
            <summary>
            Enable SSL
            </summary>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNITCPHandle.DisableSsl">
            <summary>
            Disable SSL
            </summary>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNITCPHandle.ValidateServerCertificate(System.Object,System.Security.Cryptography.X509Certificates.X509Certificate,System.Security.Cryptography.X509Certificates.X509Chain,System.Net.Security.SslPolicyErrors)">
            <summary>
            Validate server certificate callback
            </summary>
            <param name="sender">Sender object</param>
            <param name="cert">X.509 certificate</param>
            <param name="chain">X.509 chain</param>
            <param name="policyErrors">Policy errors</param>
            <returns>True if certificate is valid</returns>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNITCPHandle.SetBufferSize(System.Int32)">
            <summary>
            Set buffer size
            </summary>
            <param name="bufferSize">Buffer size</param>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNITCPHandle.Send(System.Data.SqlClient.SNI.SNIPacket)">
            <summary>
            Send a packet synchronously
            </summary>
            <param name="packet">SNI packet</param>
            <returns>SNI error code</returns>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNITCPHandle.Receive(System.Data.SqlClient.SNI.SNIPacket@,System.Int32)">
            <summary>
            Receive a packet synchronously
            </summary>
            <param name="packet">SNI packet</param>
            <param name="timeoutInMilliseconds">Timeout in Milliseconds</param>
            <returns>SNI error code</returns>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNITCPHandle.SetAsyncCallbacks(System.Data.SqlClient.SNI.SNIAsyncCallback,System.Data.SqlClient.SNI.SNIAsyncCallback)">
            <summary>
            Set async callbacks
            </summary>
            <param name="receiveCallback">Receive callback</param>
            <param name="sendCallback">Send callback</param>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNITCPHandle.SendAsync(System.Data.SqlClient.SNI.SNIPacket,System.Boolean,System.Data.SqlClient.SNI.SNIAsyncCallback)">
            <summary>
            Send a packet asynchronously
            </summary>
            <param name="packet">SNI packet</param>
            <param name="disposePacketAfterSendAsync">Whether to dispose packet after sending async</param>
            <param name="callback">Completion callback</param>
            <returns>SNI error code</returns>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNITCPHandle.ReceiveAsync(System.Data.SqlClient.SNI.SNIPacket@)">
            <summary>
            Receive a packet asynchronously
            </summary>
            <param name="packet">SNI packet</param>
            <returns>SNI error code</returns>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNITCPHandle.CheckConnection">
            <summary>
            Check SNI handle connection
            </summary>
            <returns>SNI error status</returns>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNITCPHandle.KillConnection">
            <summary>
            Test handle for killing underlying connection
            </summary>
        </member>
        <member name="T:System.Data.SqlClient.SNI.SslOverTdsStream">
            <summary>
            SSL encapsulated over TDS transport. During SSL handshake, SSL packets are
            transported in TDS packet type 0x12. Once SSL handshake has completed, SSL
            packets are sent transparently.
            </summary>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SslOverTdsStream.#ctor(System.IO.Stream)">
            <summary>
            Constructor
            </summary>
            <param name="stream">Underlying stream</param>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SslOverTdsStream.FinishHandshake">
            <summary>
            Finish SSL handshake. Stop encapsulating in TDS.
            </summary>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SslOverTdsStream.Read(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Read buffer
            </summary>
            <param name="buffer">Buffer</param>
            <param name="offset">Offset</param>
            <param name="count">Byte count</param>
            <returns>Bytes read</returns>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SslOverTdsStream.Write(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Write Buffer
            </summary>
            <param name="buffer"></param>
            <param name="offset"></param>
            <param name="count"></param>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SslOverTdsStream.WriteAsync(System.Byte[],System.Int32,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Write Buffer Asynchronosly
            </summary>
            <param name="buffer"></param>
            <param name="offset"></param>
            <param name="count"></param>
            <param name="token"></param>
            <returns></returns>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SslOverTdsStream.ReadAsync(System.Byte[],System.Int32,System.Int32,System.Threading.CancellationToken)">
            <summary>
            Read Buffer Asynchronosly
            </summary>
            <param name="buffer"></param>
            <param name="offset"></param>
            <param name="count"></param>
            <param name="token"></param>
            <returns></returns>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SslOverTdsStream.ReadInternal(System.Byte[],System.Int32,System.Int32,System.Threading.CancellationToken,System.Boolean)">
            <summary>
            Read Internal is called synchronosly when async is false
            </summary>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SslOverTdsStream.WriteInternal(System.Byte[],System.Int32,System.Int32,System.Threading.CancellationToken,System.Boolean)">
            <summary>
            The internal write method calls Sync APIs when Async flag is false
            </summary>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SslOverTdsStream.SetLength(System.Int64)">
            <summary>
            Set stream length. 
            </summary>
            <param name="value">Length</param>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SslOverTdsStream.Flush">
            <summary>
            Flush stream
            </summary>
        </member>
        <member name="P:System.Data.SqlClient.SNI.SslOverTdsStream.Position">
            <summary>
            Get/set stream position
            </summary>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SslOverTdsStream.Seek(System.Int64,System.IO.SeekOrigin)">
            <summary>
            Seek in stream
            </summary>
            <param name="offset">Offset</param>
            <param name="origin">Origin</param>
            <returns>Position</returns>
        </member>
        <member name="P:System.Data.SqlClient.SNI.SslOverTdsStream.CanRead">
            <summary>
            Check if stream can be read from
            </summary>
        </member>
        <member name="P:System.Data.SqlClient.SNI.SslOverTdsStream.CanWrite">
            <summary>
            Check if stream can be written to
            </summary>
        </member>
        <member name="P:System.Data.SqlClient.SNI.SslOverTdsStream.CanSeek">
            <summary>
            Check if stream can be seeked
            </summary>
        </member>
        <member name="P:System.Data.SqlClient.SNI.SslOverTdsStream.Length">
            <summary>
            Get stream length
            </summary>
        </member>
        <member name="T:System.Data.SqlClient.SNI.SNIAsyncCallback">
            <summary>
            SNI Asynchronous callback
            </summary>
            <param name="packet">SNI packet</param>
            <param name="sniErrorCode">SNI error code</param>
        </member>
        <member name="T:System.Data.SqlClient.SNI.SNIProviders">
            <summary>
            SNI provider identifiers
            </summary>
        </member>
        <member name="T:System.Data.SqlClient.SNI.SNISMUXHeader">
            <summary>
            SMUX packet header
            </summary>
        </member>
        <member name="T:System.Data.SqlClient.SNI.SNISMUXFlags">
            <summary>
            SMUX packet flags
            </summary>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNICommon.ValidateSslServerCertificate(System.String,System.Object,System.Security.Cryptography.X509Certificates.X509Certificate,System.Security.Cryptography.X509Certificates.X509Chain,System.Net.Security.SslPolicyErrors)">
            <summary>
            Validate server certificate callback for SSL
            </summary>
            <param name="targetServerName">Server that client is expecting to connect to</param>
            <param name="sender">Sender object</param>
            <param name="cert">X.509 certificate</param>
            <param name="chain">X.509 chain</param>
            <param name="policyErrors">Policy errors</param>
            <returns>True if certificate is valid</returns>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNICommon.ReportSNIError(System.Data.SqlClient.SNI.SNIProviders,System.UInt32,System.UInt32,System.String)">
            <summary>
            Sets last error encountered for SNI
            </summary>
            <param name="provider">SNI provider</param>
            <param name="nativeError">Native error code</param>
            <param name="sniError">SNI error code</param>
            <param name="errorMessage">Error message</param>
            <returns></returns>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNICommon.ReportSNIError(System.Data.SqlClient.SNI.SNIProviders,System.UInt32,System.Exception)">
            <summary>
            Sets last error encountered for SNI
            </summary>
            <param name="provider">SNI provider</param>
            <param name="sniError">SNI error code</param>
            <param name="sniException">SNI Exception</param>
            <returns></returns>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SNICommon.ReportSNIError(System.Data.SqlClient.SNI.SNIError)">
            <summary>
            Sets last error encountered for SNI
            </summary>
            <param name="error">SNI error</param>
            <returns></returns>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SSRP.GetPortByInstanceName(System.String,System.String)">
            <summary>
            Finds instance port number for given instance name.
            </summary>
            <param name="browserHostName">SQL Sever Browser hostname</param>
            <param name="instanceName">instance name to find port number</param>
            <returns>port number for given instance name</returns>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SSRP.CreateInstanceInfoRequest(System.String)">
            <summary>
            Creates instance port lookup request (CLNT_UCAST_INST) for given instance name.
            </summary>
            <param name="instanceName">instance name to lookup port</param>
            <returns>Byte array of instance port lookup request (CLNT_UCAST_INST)</returns>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SSRP.GetDacPortByInstanceName(System.String,System.String)">
            <summary>
            Finds DAC port for given instance name.
            </summary>
            <param name="browserHostName">SQL Sever Browser hostname</param>
            <param name="instanceName">instance name to lookup DAC port</param>
            <returns>DAC port for given instance name</returns>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SSRP.CreateDacPortInfoRequest(System.String)">
            <summary>
            Creates DAC port lookup request (CLNT_UCAST_DAC) for given instance name.
            </summary>
            <param name="instanceName">instance name to lookup DAC port</param>
            <returns>Byte array of DAC port lookup request (CLNT_UCAST_DAC)</returns>
        </member>
        <member name="M:System.Data.SqlClient.SNI.SSRP.SendUDPRequest(System.String,System.Int32,System.Byte[])">
            <summary>
            Sends request to server, and receives response from server by UDP.
            </summary>
            <param name="browserHostname">UDP server hostname</param>
            <param name="port">UDP server port</param>
            <param name="requestPacket">request packet</param>
            <returns>response packet from UDP server</returns>
        </member>
        <member name="M:System.Data.SqlClient.SNI.LocalDB.LoadUserInstanceDll">
            <summary>
            Loads the User Instance dll.
            </summary>
        </member>
        <member name="M:System.Data.SqlClient.SNI.LocalDB.GetUserInstanceDllPath(System.Data.SqlClient.SNI.LocalDB.LocalDBErrorState@)">
            <summary>
            Retrieves the part of the sqlUserInstance.dll from the registry
            </summary>
            <param name="errorState">In case the dll path is not found, the error is set here.</param>
            <returns></returns>
        </member>
        <member name="T:System.Data.Common.ActivityCorrelator">
            <summary>
            This class defines the data structure for ActivityId used for correlated tracing between client (bid trace event) and server (XEvent).
            It also includes all the APIs used to access the ActivityId. Note: ActivityId is thread based which is stored in TLS.
            </summary>
        </member>
        <member name="P:System.Data.Common.ActivityCorrelator.Current">
            <summary>
            Get the current ActivityId
            </summary>
        </member>
        <member name="M:System.Data.Common.ActivityCorrelator.Next">
            <summary>
            Increment the sequence number and generate the new ActivityId
            </summary>
            <returns>ActivityId</returns>
        </member>
        <member name="M:System.Data.Common.DbConnectionStringBuilderUtil.ConvertToApplicationIntent(System.String,System.Object)">
            <summary>
            This method attempts to convert the given value tp ApplicationIntent enum. The algorithm is:
            * if the value is from type string, it will be matched against ApplicationIntent enum names only, using ordinal, case-insensitive comparer
            * if the value is from type ApplicationIntent, it will be used as is
            * if the value is from integral type (SByte, Int16, Int32, Int64, Byte, UInt16, UInt32, or UInt64), it will be converted to enum
            * if the value is another enum or any other type, it will be blocked with an appropriate ArgumentException
            
            in any case above, if the converted value is out of valid range, the method raises ArgumentOutOfRangeException.
            </summary>
            <returns>application intent value in the valid range</returns>
        </member>
        <member name="M:System.Data.Common.DbConnectionStringBuilderUtil.ConvertToPoolBlockingPeriod(System.String,System.Object)">
            <summary>
            This method attempts to convert the given value to a PoolBlockingPeriod enum. The algorithm is:
            * if the value is from type string, it will be matched against PoolBlockingPeriod enum names only, using ordinal, case-insensitive comparer
            * if the value is from type PoolBlockingPeriod, it will be used as is
            * if the value is from integral type (SByte, Int16, Int32, Int64, Byte, UInt16, UInt32, or UInt64), it will be converted to enum
            * if the value is another enum or any other type, it will be blocked with an appropriate ArgumentException
            
            in any case above, if the conerted value is out of valid range, the method raises ArgumentOutOfRangeException.
            </summary>
            <returns>PoolBlockingPeriod value in the valid range</returns>
        </member>
        <member name="M:System.Data.ProviderBase.DbConnectionInternal.DoomThisConnection">
            <devdoc>Ensure that this connection cannot be put back into the pool.</devdoc>
        </member>
        <member name="M:System.Data.ProviderBase.DbConnectionInternal.TryOpenConnection(System.Data.Common.DbConnection,System.Data.ProviderBase.DbConnectionFactory,System.Threading.Tasks.TaskCompletionSource{System.Data.ProviderBase.DbConnectionInternal},System.Data.Common.DbConnectionOptions)">
            <devdoc>The default implementation is for the open connection objects, and
            it simply throws.  Our private closed-state connection objects
            override this and do the correct thing.</devdoc>
        </member>
        <member name="M:System.Data.ProviderBase.DbConnectionInternal.IsConnectionAlive(System.Boolean)">
            <summary>
            When overridden in a derived class, will check if the underlying connection is still actually alive
            </summary>
            <param name="throwOnException">If true an exception will be thrown if the connection is dead instead of returning true\false
            (this allows the caller to have the real reason that the connection is not alive (e.g. network error, etc))</param>
            <returns>True if the connection is still alive, otherwise false (If not overridden, then always true)</returns>
        </member>
        <member name="P:System.Data.ProviderBase.DbConnectionInternal.EnlistedTransactionDisposed">
            <summary>
            Get boolean value that indicates whether the enlisted transaction has been disposed.
            </summary>
            <value>
            True if there is an enlisted transaction, and it has been disposed.
            False if there is an enlisted transaction that has not been disposed, or if the transaction reference is null.
            </value>
            <remarks>
            This method must be called while holding a lock on the DbConnectionInternal instance.
            </remarks>
        </member>
        <member name="M:System.Data.ProviderBase.DbConnectionPool.ReplaceConnection(System.Data.Common.DbConnection,System.Data.Common.DbConnectionOptions,System.Data.ProviderBase.DbConnectionInternal)">
            <summary>
            Creates a new connection to replace an existing connection
            </summary>
            <param name="owningObject">Outer connection that currently owns <paramref name="oldConnection"/></param>
            <param name="userOptions">Options used to create the new connection</param>
            <param name="oldConnection">Inner connection that will be replaced</param>
            <returns>A new inner connection that is attached to the <paramref name="owningObject"/></returns>
        </member>
        <member name="T:System.Data.SqlTypes.SqlTypeWorkarounds">
            <summary>
            This type provides workarounds for the separation between System.Data.Common
            and System.Data.SqlClient.  The latter wants to access internal members of the former, and
            this class provides ways to do that.  We must review and update this implementation any time the
            implementation of the corresponding types in System.Data.Common change.
            </summary>
        </member>
        <member name="M:System.Data.SqlTypes.SqlTypeWorkarounds.SqlMoneyCtor(System.Int64,System.Int32)">
            <summary>
            Constructs a SqlMoney from a long value without scaling. The ignored parameter exists
            only to distinguish this constructor from the constructor that takes a long.
            Used only internally.
            </summary>
        </member>
        <member name="P:System.SR.ADP_CollectionIndexInt32">
            <summary>Invalid index {0} for this {1} with Count={2}.</summary>
        </member>
        <member name="P:System.SR.ADP_CollectionIndexString">
            <summary>An {0} with {1} '{2}' is not contained by this {3}.</summary>
        </member>
        <member name="P:System.SR.ADP_CollectionInvalidType">
            <summary>The {0} only accepts non-null {1} type objects, not {2} objects.</summary>
        </member>
        <member name="P:System.SR.ADP_CollectionIsNotParent">
            <summary>The {0} is already contained by another {1}.</summary>
        </member>
        <member name="P:System.SR.ADP_CollectionNullValue">
            <summary>The {0} only accepts non-null {1} type objects.</summary>
        </member>
        <member name="P:System.SR.ADP_CollectionRemoveInvalidObject">
            <summary>Attempted to remove an {0} that is not contained by this {1}.</summary>
        </member>
        <member name="P:System.SR.ADP_ConnectionAlreadyOpen">
            <summary>The connection was not closed. {0}</summary>
        </member>
        <member name="P:System.SR.ADP_ConnectionStateMsg_Closed">
            <summary>The connection's current state is closed.</summary>
        </member>
        <member name="P:System.SR.ADP_ConnectionStateMsg_Connecting">
            <summary>The connection's current state is connecting.</summary>
        </member>
        <member name="P:System.SR.ADP_ConnectionStateMsg_Open">
            <summary>The connection's current state is open.</summary>
        </member>
        <member name="P:System.SR.ADP_ConnectionStateMsg_OpenExecuting">
            <summary>The connection's current state is executing.</summary>
        </member>
        <member name="P:System.SR.ADP_ConnectionStateMsg_OpenFetching">
            <summary>The connection's current state is fetching.</summary>
        </member>
        <member name="P:System.SR.ADP_ConnectionStateMsg">
            <summary>The connection's current state: {0}.</summary>
        </member>
        <member name="P:System.SR.ADP_ConnectionStringSyntax">
            <summary>Format of the initialization string does not conform to specification starting at index {0}.</summary>
        </member>
        <member name="P:System.SR.ADP_DataReaderClosed">
            <summary>Invalid attempt to call {0} when reader is closed.</summary>
        </member>
        <member name="P:System.SR.ADP_InternalConnectionError">
            <summary>Internal DbConnection Error: {0}</summary>
        </member>
        <member name="P:System.SR.ADP_InvalidEnumerationValue">
            <summary>The {0} enumeration value, {1}, is invalid.</summary>
        </member>
        <member name="P:System.SR.ADP_NotSupportedEnumerationValue">
            <summary>The {0} enumeration value, {1}, is not supported by the {2} method.</summary>
        </member>
        <member name="P:System.SR.ADP_InvalidOffsetValue">
            <summary>Invalid parameter Offset value '{0}'. The value must be greater than or equal to 0.</summary>
        </member>
        <member name="P:System.SR.ADP_TransactionPresent">
            <summary>Connection currently has transaction enlisted.  Finish current transaction and retry.</summary>
        </member>
        <member name="P:System.SR.ADP_LocalTransactionPresent">
            <summary>Cannot enlist in the transaction because a local transaction is in progress on the connection.  Finish local transaction and retry.</summary>
        </member>
        <member name="P:System.SR.ADP_NoConnectionString">
            <summary>The ConnectionString property has not been initialized.</summary>
        </member>
        <member name="P:System.SR.ADP_OpenConnectionPropertySet">
            <summary>Not allowed to change the '{0}' property. {1}</summary>
        </member>
        <member name="P:System.SR.ADP_PendingAsyncOperation">
            <summary>Can not start another operation while there is an asynchronous operation pending.</summary>
        </member>
        <member name="P:System.SR.ADP_PooledOpenTimeout">
            <summary>Timeout expired.  The timeout period elapsed prior to obtaining a connection from the pool.  This may have occurred because all pooled connections were in use and max pool size was reached.</summary>
        </member>
        <member name="P:System.SR.ADP_NonPooledOpenTimeout">
            <summary>Timeout attempting to open the connection.  The time period elapsed prior to attempting to open the connection has been exceeded.  This may have occurred because of too many simultaneous non-pooled connection attempts.</summary>
        </member>
        <member name="P:System.SR.ADP_SingleValuedProperty">
            <summary>The only acceptable value for the property '{0}' is '{1}'.</summary>
        </member>
        <member name="P:System.SR.ADP_DoubleValuedProperty">
            <summary>The acceptable values for the property '{0}' are '{1}' or '{2}'.</summary>
        </member>
        <member name="P:System.SR.ADP_InvalidPrefixSuffix">
            <summary>Specified QuotePrefix and QuoteSuffix values do not match.</summary>
        </member>
        <member name="P:System.SR.Arg_ArrayPlusOffTooSmall">
            <summary>Destination array is not long enough to copy all the items in the collection. Check array index and length.</summary>
        </member>
        <member name="P:System.SR.Arg_RankMultiDimNotSupported">
            <summary>Only single dimensional arrays are supported for the requested action.</summary>
        </member>
        <member name="P:System.SR.Arg_RemoveArgNotFound">
            <summary>Cannot remove the specified item because it was not found in the specified Collection.</summary>
        </member>
        <member name="P:System.SR.ArgumentOutOfRange_NeedNonNegNum">
            <summary>Non-negative number required.</summary>
        </member>
        <member name="P:System.SR.Data_InvalidOffsetLength">
            <summary>Offset and length were out of bounds for the array or count is greater than the number of elements from index to the end of the source collection.</summary>
        </member>
        <member name="P:System.SR.SqlConvert_ConvertFailed">
            <summary>Cannot convert object of type '{0}' to object of type '{1}'.</summary>
        </member>
        <member name="P:System.SR.SQL_WrongType">
            <summary>Expecting argument of type {1}, but received type {0}.</summary>
        </member>
        <member name="P:System.SR.ADP_DeriveParametersNotSupported">
            <summary>{0} DeriveParameters only supports CommandType.StoredProcedure, not CommandType. {1}.</summary>
        </member>
        <member name="P:System.SR.ADP_NoStoredProcedureExists">
            <summary>The stored procedure '{0}' doesn't exist.</summary>
        </member>
        <member name="P:System.SR.ADP_InvalidConnectionOptionValue">
            <summary>Invalid value for key '{0}'.</summary>
        </member>
        <member name="P:System.SR.ADP_MissingConnectionOptionValue">
            <summary>Use of key '{0}' requires the key '{1}' to be present.</summary>
        </member>
        <member name="P:System.SR.ADP_InvalidConnectionOptionValueLength">
            <summary>The value's length for key '{0}' exceeds it's limit of '{1}'.</summary>
        </member>
        <member name="P:System.SR.ADP_KeywordNotSupported">
            <summary>Keyword not supported: '{0}'.</summary>
        </member>
        <member name="P:System.SR.ADP_InternalProviderError">
            <summary>Internal .NET Framework Data Provider error {0}.</summary>
        </member>
        <member name="P:System.SR.ADP_InvalidMultipartName">
            <summary>{0} '{1}'.</summary>
        </member>
        <member name="P:System.SR.ADP_InvalidMultipartNameQuoteUsage">
            <summary>{0} '{1}', incorrect usage of quotes.</summary>
        </member>
        <member name="P:System.SR.ADP_InvalidMultipartNameToManyParts">
            <summary>{0} '{1}', the current limit of '{2}' is insufficient.</summary>
        </member>
        <member name="P:System.SR.SQL_SqlCommandCommandText">
            <summary>SqlCommand.DeriveParameters failed because the SqlCommand.CommandText property value is an invalid multipart name</summary>
        </member>
        <member name="P:System.SR.SQL_BatchedUpdatesNotAvailableOnContextConnection">
            <summary>Batching updates is not supported on the context connection.</summary>
        </member>
        <member name="P:System.SR.SQL_BulkCopyDestinationTableName">
            <summary>SqlBulkCopy.WriteToServer failed because the SqlBulkCopy.DestinationTableName is an invalid multipart name</summary>
        </member>
        <member name="P:System.SR.SQL_TDSParserTableName">
            <summary>Processing of results from SQL Server failed because of an invalid multipart name</summary>
        </member>
        <member name="P:System.SR.SQL_TypeName">
            <summary>SqlParameter.TypeName is an invalid multipart name</summary>
        </member>
        <member name="P:System.SR.SQLMSF_FailoverPartnerNotSupported">
            <summary>Connecting to a mirrored SQL Server instance using the MultiSubnetFailover connection option is not supported.</summary>
        </member>
        <member name="P:System.SR.SQL_NotSupportedEnumerationValue">
            <summary>The {0} enumeration value, {1}, is not supported by the .NET Framework SqlClient Data Provider.</summary>
        </member>
        <member name="P:System.SR.ADP_CommandTextRequired">
            <summary>{0}: CommandText property has not been initialized</summary>
        </member>
        <member name="P:System.SR.ADP_ConnectionRequired">
            <summary>{0}: Connection property has not been initialized.</summary>
        </member>
        <member name="P:System.SR.ADP_OpenConnectionRequired">
            <summary>{0} requires an open and available Connection. {1}</summary>
        </member>
        <member name="P:System.SR.ADP_TransactionConnectionMismatch">
            <summary>The transaction is either not associated with the current connection or has been completed.</summary>
        </member>
        <member name="P:System.SR.ADP_TransactionRequired">
            <summary>{0} requires the command to have a transaction when the connection assigned to the command is in a pending local transaction.  The Transaction property of the command has not been initialized.</summary>
        </member>
        <member name="P:System.SR.ADP_OpenReaderExists">
            <summary>There is already an open DataReader associated with this Command which must be closed first.</summary>
        </member>
        <member name="P:System.SR.ADP_CalledTwice">
            <summary>The method '{0}' cannot be called more than once for the same execution.</summary>
        </member>
        <member name="P:System.SR.ADP_InvalidCommandTimeout">
            <summary>Invalid CommandTimeout value {0}; the value must be &gt;= 0.</summary>
        </member>
        <member name="P:System.SR.ADP_UninitializedParameterSize">
            <summary>{1}[{0}]: the Size property has an invalid size of 0.</summary>
        </member>
        <member name="P:System.SR.ADP_PrepareParameterType">
            <summary>{0}.Prepare method requires all parameters to have an explicitly set type.</summary>
        </member>
        <member name="P:System.SR.ADP_PrepareParameterSize">
            <summary>{0}.Prepare method requires all variable length parameters to have an explicitly set non-zero Size.</summary>
        </member>
        <member name="P:System.SR.ADP_PrepareParameterScale">
            <summary>{0}.Prepare method requires parameters of type '{1}' have an explicitly set Precision and Scale.</summary>
        </member>
        <member name="P:System.SR.ADP_MismatchedAsyncResult">
            <summary>Mismatched end method call for asyncResult.  Expected call to {0} but {1} was called instead.</summary>
        </member>
        <member name="P:System.SR.ADP_ClosedConnectionError">
            <summary>Invalid operation. The connection is closed.</summary>
        </member>
        <member name="P:System.SR.ADP_ConnectionIsDisabled">
            <summary>The connection has been disabled.</summary>
        </member>
        <member name="P:System.SR.ADP_EmptyDatabaseName">
            <summary>Database cannot be null, the empty string, or string of only whitespace.</summary>
        </member>
        <member name="P:System.SR.ADP_InvalidSourceBufferIndex">
            <summary>Invalid source buffer (size of {0}) offset: {1}</summary>
        </member>
        <member name="P:System.SR.ADP_InvalidDestinationBufferIndex">
            <summary>Invalid destination buffer (size of {0}) offset: {1}</summary>
        </member>
        <member name="P:System.SR.ADP_StreamClosed">
            <summary>Invalid attempt to {0} when stream is closed.</summary>
        </member>
        <member name="P:System.SR.ADP_InvalidSeekOrigin">
            <summary>Specified SeekOrigin value is invalid.</summary>
        </member>
        <member name="P:System.SR.ADP_NonSequentialColumnAccess">
            <summary>Invalid attempt to read from column ordinal '{0}'.  With CommandBehavior.SequentialAccess, you may only read from column ordinal '{1}' or greater.</summary>
        </member>
        <member name="P:System.SR.ADP_InvalidDataType">
            <summary>The parameter data type of {0} is invalid.</summary>
        </member>
        <member name="P:System.SR.ADP_UnknownDataType">
            <summary>No mapping exists from object type {0} to a known managed provider native type.</summary>
        </member>
        <member name="P:System.SR.ADP_UnknownDataTypeCode">
            <summary>Unable to handle an unknown TypeCode {0} returned by Type {1}.</summary>
        </member>
        <member name="P:System.SR.ADP_DbTypeNotSupported">
            <summary>No mapping exists from DbType {0} to a known {1}.</summary>
        </member>
        <member name="P:System.SR.ADP_VersionDoesNotSupportDataType">
            <summary>The version of SQL Server in use does not support datatype '{0}'.</summary>
        </member>
        <member name="P:System.SR.ADP_ParameterValueOutOfRange">
            <summary>Parameter value '{0}' is out of range.</summary>
        </member>
        <member name="P:System.SR.ADP_BadParameterName">
            <summary>Specified parameter name '{0}' is not valid.</summary>
        </member>
        <member name="P:System.SR.ADP_InvalidSizeValue">
            <summary>Invalid parameter Size value '{0}'. The value must be greater than or equal to 0.</summary>
        </member>
        <member name="P:System.SR.ADP_NegativeParameter">
            <summary>Invalid value for argument '{0}'. The value must be greater than or equal to 0.</summary>
        </member>
        <member name="P:System.SR.ADP_InvalidMetaDataValue">
            <summary>Invalid value for this metadata.</summary>
        </member>
        <member name="P:System.SR.ADP_ParameterConversionFailed">
            <summary>Failed to convert parameter value from a {0} to a {1}.</summary>
        </member>
        <member name="P:System.SR.ADP_ParallelTransactionsNotSupported">
            <summary>{0} does not support parallel transactions.</summary>
        </member>
        <member name="P:System.SR.ADP_TransactionZombied">
            <summary>This {0} has completed; it is no longer usable.</summary>
        </member>
        <member name="P:System.SR.ADP_InvalidDataLength2">
            <summary>Specified length '{0}' is out of range.</summary>
        </member>
        <member name="P:System.SR.ADP_NonSeqByteAccess">
            <summary>Invalid {2} attempt at dataIndex '{0}'.  With CommandBehavior.SequentialAccess, you may only read from dataIndex '{1}' or greater.</summary>
        </member>
        <member name="P:System.SR.ADP_InvalidMinMaxPoolSizeValues">
            <summary>Invalid min or max pool size values, min pool size cannot be greater than the max pool size.</summary>
        </member>
        <member name="P:System.SR.SQL_InvalidPacketSizeValue">
            <summary>Invalid 'Packet Size'.  The value must be an integer &gt;= 512 and &lt;= 32768.</summary>
        </member>
        <member name="P:System.SR.SQL_NullEmptyTransactionName">
            <summary>Invalid transaction or invalid name for a point at which to save within the transaction.</summary>
        </member>
        <member name="P:System.SR.SQL_UserInstanceFailoverNotCompatible">
            <summary>User Instance and Failover are not compatible options.  Please choose only one of the two in the connection string.</summary>
        </member>
        <member name="P:System.SR.SQL_EncryptionNotSupportedByClient">
            <summary>The instance of SQL Server you attempted to connect to requires encryption but this machine does not support it.</summary>
        </member>
        <member name="P:System.SR.SQL_EncryptionNotSupportedByServer">
            <summary>The instance of SQL Server you attempted to connect to does not support encryption.</summary>
        </member>
        <member name="P:System.SR.SQL_InvalidSQLServerVersionUnknown">
            <summary>Unsupported SQL Server version.  The .NET Framework SqlClient Data Provider can only be used with SQL Server versions 7.0 and later.</summary>
        </member>
        <member name="P:System.SR.SQL_CannotCreateNormalizer">
            <summary>Cannot create normalizer for '{0}'.</summary>
        </member>
        <member name="P:System.SR.SQL_CannotModifyPropertyAsyncOperationInProgress">
            <summary>{0} cannot be changed while async operation is in progress.</summary>
        </member>
        <member name="P:System.SR.SQL_InstanceFailure">
            <summary>Instance failure.</summary>
        </member>
        <member name="P:System.SR.SQL_InvalidPartnerConfiguration">
            <summary>Server {0}, database {1} is not configured for database mirroring.</summary>
        </member>
        <member name="P:System.SR.SQL_MarsUnsupportedOnConnection">
            <summary>The connection does not support MultipleActiveResultSets.</summary>
        </member>
        <member name="P:System.SR.SQL_NonLocalSSEInstance">
            <summary>SSE Instance re-direction is not supported for non-local user instances.</summary>
        </member>
        <member name="P:System.SR.SQL_PendingBeginXXXExists">
            <summary>The command execution cannot proceed due to a pending asynchronous operation already in progress.</summary>
        </member>
        <member name="P:System.SR.SQL_NonXmlResult">
            <summary>Invalid command sent to ExecuteXmlReader.  The command must return an Xml result.</summary>
        </member>
        <member name="P:System.SR.SQL_InvalidParameterTypeNameFormat">
            <summary>Invalid 3 part name format for TypeName.</summary>
        </member>
        <member name="P:System.SR.SQL_InvalidParameterNameLength">
            <summary>The length of the parameter '{0}' exceeds the limit of 128 characters.</summary>
        </member>
        <member name="P:System.SR.SQL_PrecisionValueOutOfRange">
            <summary>Precision value '{0}' is either less than 0 or greater than the maximum allowed precision of 38.</summary>
        </member>
        <member name="P:System.SR.SQL_ScaleValueOutOfRange">
            <summary>Scale value '{0}' is either less than 0 or greater than the maximum allowed scale of 38.</summary>
        </member>
        <member name="P:System.SR.SQL_TimeScaleValueOutOfRange">
            <summary>Scale value '{0}' is either less than 0 or greater than the maximum allowed scale of 7.</summary>
        </member>
        <member name="P:System.SR.SQL_ParameterInvalidVariant">
            <summary>Parameter '{0}' exceeds the size limit for the sql_variant datatype.</summary>
        </member>
        <member name="P:System.SR.SQL_ParameterTypeNameRequired">
            <summary>The {0} type parameter '{1}' must have a valid type name.</summary>
        </member>
        <member name="P:System.SR.SQL_InvalidInternalPacketSize">
            <summary>Invalid internal packet size:</summary>
        </member>
        <member name="P:System.SR.SQL_InvalidTDSVersion">
            <summary>The SQL Server instance returned an invalid or unsupported protocol version during login negotiation.</summary>
        </member>
        <member name="P:System.SR.SQL_InvalidTDSPacketSize">
            <summary>Invalid Packet Size.</summary>
        </member>
        <member name="P:System.SR.SQL_ParsingError">
            <summary>Internal connection fatal error.</summary>
        </member>
        <member name="P:System.SR.SQL_ConnectionLockedForBcpEvent">
            <summary>The connection cannot be used because there is an ongoing operation that must be finished.</summary>
        </member>
        <member name="P:System.SR.SQL_SNIPacketAllocationFailure">
            <summary>Memory allocation for internal connection failed.</summary>
        </member>
        <member name="P:System.SR.SQL_SmallDateTimeOverflow">
            <summary>SqlDbType.SmallDateTime overflow.  Value '{0}' is out of range.  Must be between 1/1/1900 12:00:00 AM and 6/6/2079 11:59:59 PM.</summary>
        </member>
        <member name="P:System.SR.SQL_TimeOverflow">
            <summary>SqlDbType.Time overflow.  Value '{0}' is out of range.  Must be between 00:00:00.0000000 and 23:59:59.9999999.</summary>
        </member>
        <member name="P:System.SR.SQL_MoneyOverflow">
            <summary>SqlDbType.SmallMoney overflow.  Value '{0}' is out of range.  Must be between -214,748.3648 and 214,748.3647.</summary>
        </member>
        <member name="P:System.SR.SQL_CultureIdError">
            <summary>The Collation specified by SQL Server is not supported.</summary>
        </member>
        <member name="P:System.SR.SQL_OperationCancelled">
            <summary>Operation cancelled by user.</summary>
        </member>
        <member name="P:System.SR.SQL_SevereError">
            <summary>A severe error occurred on the current command.  The results, if any, should be discarded.</summary>
        </member>
        <member name="P:System.SR.SQL_SSPIGenerateError">
            <summary>Failed to generate SSPI context.</summary>
        </member>
        <member name="P:System.SR.SQL_KerberosTicketMissingError">
            <summary>Cannot authenticate using Kerberos. Ensure Kerberos has been initialized on the client with 'kinit' and a Service Principal Name has been registered for the SQL Server to allow Kerberos authentication.</summary>
        </member>
        <member name="P:System.SR.SQL_SqlServerBrowserNotAccessible">
            <summary>Cannot connect to SQL Server Browser. Ensure SQL Server Browser has been started.</summary>
        </member>
        <member name="P:System.SR.SQL_InvalidSSPIPacketSize">
            <summary>Invalid SSPI packet size.</summary>
        </member>
        <member name="P:System.SR.SQL_SSPIInitializeError">
            <summary>Cannot initialize SSPI package.</summary>
        </member>
        <member name="P:System.SR.SQL_Timeout">
            <summary>Timeout expired.  The timeout period elapsed prior to completion of the operation or the server is not responding.</summary>
        </member>
        <member name="P:System.SR.SQL_Timeout_PreLogin_Begin">
            <summary>Connection Timeout Expired.  The timeout period elapsed at the start of the pre-login phase.  This could be because of insufficient time provided for connection timeout.</summary>
        </member>
        <member name="P:System.SR.SQL_Timeout_PreLogin_InitializeConnection">
            <summary>Connection Timeout Expired.  The timeout period elapsed while attempting to create and initialize a socket to the server.  This could be either because the server was unreachable or unable to respond back in time.</summary>
        </member>
        <member name="P:System.SR.SQL_Timeout_PreLogin_SendHandshake">
            <summary>Connection Timeout Expired.  The timeout period elapsed while making a pre-login handshake request.  This could be because the server was unable to respond back in time.</summary>
        </member>
        <member name="P:System.SR.SQL_Timeout_PreLogin_ConsumeHandshake">
            <summary>Connection Timeout Expired.  The timeout period elapsed while attempting to consume the pre-login handshake acknowledgement.  This could be because the pre-login handshake failed or the server was unable to respond back in time.</summary>
        </member>
        <member name="P:System.SR.SQL_Timeout_Login_Begin">
            <summary>Connection Timeout Expired.  The timeout period elapsed at the start of the login phase.  This could be because of insufficient time provided for connection timeout.</summary>
        </member>
        <member name="P:System.SR.SQL_Timeout_Login_ProcessConnectionAuth">
            <summary>Connection Timeout Expired.  The timeout period elapsed while attempting to authenticate the login.  This could be because the server failed to authenticate the user or the server was unable to respond back in time.</summary>
        </member>
        <member name="P:System.SR.SQL_Timeout_PostLogin">
            <summary>Connection Timeout Expired.  The timeout period elapsed during the post-login phase.  The connection could have timed out while waiting for server to complete the login process and respond; Or it could have timed out while attempting to create multiple act ...</summary>
        </member>
        <member name="P:System.SR.SQL_Timeout_FailoverInfo">
            <summary>This failure occurred while attempting to connect to the {0} server.</summary>
        </member>
        <member name="P:System.SR.SQL_Timeout_RoutingDestinationInfo">
            <summary>This failure occurred while attempting to connect to the routing destination. The duration spent while attempting to connect to the original server was - [Pre-Login] initialization={0}; handshake={1}; [Login] initialization={2}; authentication={3}; [Post-L ...</summary>
        </member>
        <member name="P:System.SR.SQL_Duration_PreLogin_Begin">
            <summary>The duration spent while attempting to connect to this server was - [Pre-Login] initialization={0};</summary>
        </member>
        <member name="P:System.SR.SQL_Duration_PreLoginHandshake">
            <summary>The duration spent while attempting to connect to this server was - [Pre-Login] initialization={0}; handshake={1};</summary>
        </member>
        <member name="P:System.SR.SQL_Duration_Login_Begin">
            <summary>The duration spent while attempting to connect to this server was - [Pre-Login] initialization={0}; handshake={1}; [Login] initialization={2};</summary>
        </member>
        <member name="P:System.SR.SQL_Duration_Login_ProcessConnectionAuth">
            <summary>The duration spent while attempting to connect to this server was - [Pre-Login] initialization={0}; handshake={1}; [Login] initialization={2}; authentication={3};</summary>
        </member>
        <member name="P:System.SR.SQL_Duration_PostLogin">
            <summary>The duration spent while attempting to connect to this server was - [Pre-Login] initialization={0}; handshake={1}; [Login] initialization={2}; authentication={3}; [Post-Login] complete={4};</summary>
        </member>
        <member name="P:System.SR.SQL_UserInstanceFailure">
            <summary>A user instance was requested in the connection string but the server specified does not support this option.</summary>
        </member>
        <member name="P:System.SR.SQL_InvalidRead">
            <summary>Invalid attempt to read when no data is present.</summary>
        </member>
        <member name="P:System.SR.SQL_NonBlobColumn">
            <summary>Invalid attempt to GetBytes on column '{0}'.  The GetBytes function can only be used on columns of type Text, NText, or Image.</summary>
        </member>
        <member name="P:System.SR.SQL_NonCharColumn">
            <summary>Invalid attempt to GetChars on column '{0}'.  The GetChars function can only be used on columns of type Text, NText, Xml, VarChar or NVarChar.</summary>
        </member>
        <member name="P:System.SR.SQL_StreamNotSupportOnColumnType">
            <summary>Invalid attempt to GetStream on column '{0}'. The GetStream function can only be used on columns of type Binary, Image, Udt or VarBinary.</summary>
        </member>
        <member name="P:System.SR.SQL_TextReaderNotSupportOnColumnType">
            <summary>Invalid attempt to GetTextReader on column '{0}'. The GetTextReader function can only be used on columns of type Char, NChar, NText, NVarChar, Text or VarChar.</summary>
        </member>
        <member name="P:System.SR.SQL_XmlReaderNotSupportOnColumnType">
            <summary>Invalid attempt to GetXmlReader on column '{0}'. The GetXmlReader function can only be used on columns of type Xml.</summary>
        </member>
        <member name="P:System.SR.SqlDelegatedTransaction_PromotionFailed">
            <summary>Failure while attempting to promote transaction.</summary>
        </member>
        <member name="P:System.SR.SQL_InvalidBufferSizeOrIndex">
            <summary>Buffer offset '{1}' plus the bytes available '{0}' is greater than the length of the passed in buffer.</summary>
        </member>
        <member name="P:System.SR.SQL_InvalidDataLength">
            <summary>Data length '{0}' is less than 0.</summary>
        </member>
        <member name="P:System.SR.SQL_BulkLoadMappingInaccessible">
            <summary>The mapped collection is in use and cannot be accessed at this time;</summary>
        </member>
        <member name="P:System.SR.SQL_BulkLoadMappingsNamesOrOrdinalsOnly">
            <summary>Mappings must be either all name or all ordinal based.</summary>
        </member>
        <member name="P:System.SR.SQL_BulkLoadCannotConvertValue">
            <summary>The given value of type {0} from the data source cannot be converted to type {1} of the specified target column.</summary>
        </member>
        <member name="P:System.SR.SQL_BulkLoadNonMatchingColumnMapping">
            <summary>The given ColumnMapping does not match up with any column in the source or destination.</summary>
        </member>
        <member name="P:System.SR.SQL_BulkLoadNonMatchingColumnName">
            <summary>The given ColumnName '{0}' does not match up with any column in data source.</summary>
        </member>
        <member name="P:System.SR.SQL_BulkLoadStringTooLong">
            <summary>String or binary data would be truncated.</summary>
        </member>
        <member name="P:System.SR.SQL_BulkLoadInvalidTimeout">
            <summary>Timeout Value '{0}' is less than 0.</summary>
        </member>
        <member name="P:System.SR.SQL_BulkLoadInvalidVariantValue">
            <summary>Value cannot be converted to SqlVariant.</summary>
        </member>
        <member name="P:System.SR.SQL_BulkLoadExistingTransaction">
            <summary>Unexpected existing transaction.</summary>
        </member>
        <member name="P:System.SR.SQL_BulkLoadNoCollation">
            <summary>Failed to obtain column collation information for the destination table. If the table is not in the current database the name must be qualified using the database name (e.g. [mydb]..[mytable](e.g. [mydb]..[mytable]); this also applies to temporary-tables ( ...</summary>
        </member>
        <member name="P:System.SR.SQL_BulkLoadConflictingTransactionOption">
            <summary>Must not specify SqlBulkCopyOption.UseInternalTransaction and pass an external Transaction at the same time.</summary>
        </member>
        <member name="P:System.SR.SQL_BulkLoadInvalidOperationInsideEvent">
            <summary>Function must not be called during event.</summary>
        </member>
        <member name="P:System.SR.SQL_BulkLoadMissingDestinationTable">
            <summary>The DestinationTableName property must be set before calling this method.</summary>
        </member>
        <member name="P:System.SR.SQL_BulkLoadInvalidDestinationTable">
            <summary>Cannot access destination table '{0}'.</summary>
        </member>
        <member name="P:System.SR.SQL_BulkLoadNotAllowDBNull">
            <summary>Column '{0}' does not allow DBNull.Value.</summary>
        </member>
        <member name="P:System.SR.Sql_BulkLoadLcidMismatch">
            <summary>The locale id '{0}' of the source column '{1}' and the locale id '{2}' of the destination column '{3}' do not match.</summary>
        </member>
        <member name="P:System.SR.SQL_BulkLoadPendingOperation">
            <summary>Attempt to invoke bulk copy on an object that has a pending operation.</summary>
        </member>
        <member name="P:System.SR.SQL_CannotGetDTCAddress">
            <summary>Unable to get the address of the distributed transaction coordinator for the server, from the server.  Is DTC enabled on the server?</summary>
        </member>
        <member name="P:System.SR.SQL_ConnectionDoomed">
            <summary>The requested operation cannot be completed because the connection has been broken.</summary>
        </member>
        <member name="P:System.SR.SQL_OpenResultCountExceeded">
            <summary>Open result count exceeded.</summary>
        </member>
        <member name="P:System.SR.SQL_StreamWriteNotSupported">
            <summary>The Stream does not support writing.</summary>
        </member>
        <member name="P:System.SR.SQL_StreamReadNotSupported">
            <summary>The Stream does not support reading.</summary>
        </member>
        <member name="P:System.SR.SQL_StreamSeekNotSupported">
            <summary>The Stream does not support seeking.</summary>
        </member>
        <member name="P:System.SR.SQL_ExClientConnectionId">
            <summary>ClientConnectionId:{0}</summary>
        </member>
        <member name="P:System.SR.SQL_ExErrorNumberStateClass">
            <summary>Error Number:{0},State:{1},Class:{2}</summary>
        </member>
        <member name="P:System.SR.SQL_ExOriginalClientConnectionId">
            <summary>ClientConnectionId before routing:{0}</summary>
        </member>
        <member name="P:System.SR.SQL_ExRoutingDestination">
            <summary>Routing Destination:{0}</summary>
        </member>
        <member name="P:System.SR.SQL_UnsupportedSysTxVersion">
            <summary>The currently loaded System.Transactions.dll does not support Global Transactions.</summary>
        </member>
        <member name="P:System.SR.SqlMisc_NullString">
            <summary>Null</summary>
        </member>
        <member name="P:System.SR.SqlMisc_MessageString">
            <summary>Message</summary>
        </member>
        <member name="P:System.SR.SqlMisc_ArithOverflowMessage">
            <summary>Arithmetic Overflow.</summary>
        </member>
        <member name="P:System.SR.SqlMisc_DivideByZeroMessage">
            <summary>Divide by zero error encountered.</summary>
        </member>
        <member name="P:System.SR.SqlMisc_NullValueMessage">
            <summary>Data is Null. This method or property cannot be called on Null values.</summary>
        </member>
        <member name="P:System.SR.SqlMisc_TruncationMessage">
            <summary>Numeric arithmetic causes truncation.</summary>
        </member>
        <member name="P:System.SR.SqlMisc_DateTimeOverflowMessage">
            <summary>SqlDateTime overflow. Must be between 1/1/1753 12:00:00 AM and 12/31/9999 11:59:59 PM.</summary>
        </member>
        <member name="P:System.SR.SqlMisc_ConcatDiffCollationMessage">
            <summary>Two strings to be concatenated have different collation.</summary>
        </member>
        <member name="P:System.SR.SqlMisc_CompareDiffCollationMessage">
            <summary>Two strings to be compared have different collation.</summary>
        </member>
        <member name="P:System.SR.SqlMisc_InvalidFlagMessage">
            <summary>Invalid flag value.</summary>
        </member>
        <member name="P:System.SR.SqlMisc_NumeToDecOverflowMessage">
            <summary>Conversion from SqlDecimal to Decimal overflows.</summary>
        </member>
        <member name="P:System.SR.SqlMisc_ConversionOverflowMessage">
            <summary>Conversion overflows.</summary>
        </member>
        <member name="P:System.SR.SqlMisc_InvalidDateTimeMessage">
            <summary>Invalid SqlDateTime.</summary>
        </member>
        <member name="P:System.SR.SqlMisc_TimeZoneSpecifiedMessage">
            <summary>A time zone was specified. SqlDateTime does not support time zones.</summary>
        </member>
        <member name="P:System.SR.SqlMisc_InvalidArraySizeMessage">
            <summary>Invalid array size.</summary>
        </member>
        <member name="P:System.SR.SqlMisc_InvalidPrecScaleMessage">
            <summary>Invalid numeric precision/scale.</summary>
        </member>
        <member name="P:System.SR.SqlMisc_FormatMessage">
            <summary>The input wasn't in a correct format.</summary>
        </member>
        <member name="P:System.SR.SqlMisc_StreamErrorMessage">
            <summary>An error occurred while reading.</summary>
        </member>
        <member name="P:System.SR.SqlMisc_TruncationMaxDataMessage">
            <summary>Data returned is larger than 2Gb in size. Use SequentialAccess command behavior in order to get all of the data.</summary>
        </member>
        <member name="P:System.SR.SqlMisc_NotFilledMessage">
            <summary>SQL Type has not been loaded with data.</summary>
        </member>
        <member name="P:System.SR.SqlMisc_AlreadyFilledMessage">
            <summary>SQL Type has already been loaded with data.</summary>
        </member>
        <member name="P:System.SR.SqlMisc_ClosedXmlReaderMessage">
            <summary>Invalid attempt to access a closed XmlReader.</summary>
        </member>
        <member name="P:System.SR.SqlMisc_InvalidOpStreamClosed">
            <summary>Invalid attempt to call {0} when the stream is closed.</summary>
        </member>
        <member name="P:System.SR.SqlMisc_InvalidOpStreamNonWritable">
            <summary>Invalid attempt to call {0} when the stream non-writable.</summary>
        </member>
        <member name="P:System.SR.SqlMisc_InvalidOpStreamNonReadable">
            <summary>Invalid attempt to call {0} when the stream non-readable.</summary>
        </member>
        <member name="P:System.SR.SqlMisc_InvalidOpStreamNonSeekable">
            <summary>Invalid attempt to call {0} when the stream is non-seekable.</summary>
        </member>
        <member name="P:System.SR.SqlMisc_SubclassMustOverride">
            <summary>Subclass did not override a required method.</summary>
        </member>
        <member name="P:System.SR.SqlUdtReason_NoUdtAttribute">
            <summary>no UDT attribute</summary>
        </member>
        <member name="P:System.SR.SQLUDT_InvalidSqlType">
            <summary>Specified type is not registered on the target server. {0}.</summary>
        </member>
        <member name="P:System.SR.Sql_InternalError">
            <summary>Internal Error</summary>
        </member>
        <member name="P:System.SR.ADP_OperationAborted">
            <summary>Operation aborted.</summary>
        </member>
        <member name="P:System.SR.ADP_OperationAbortedExceptionMessage">
            <summary>Operation aborted due to an exception (see InnerException for details).</summary>
        </member>
        <member name="P:System.SR.ADP_TransactionCompletedButNotDisposed">
            <summary>The transaction associated with the current connection has completed but has not been disposed.  The transaction must be disposed before the connection can be used to execute SQL statements.</summary>
        </member>
        <member name="P:System.SR.SqlParameter_UnsupportedTVPOutputParameter">
            <summary>ParameterDirection '{0}' specified for parameter '{1}' is not supported. Table-valued parameters only support ParameterDirection.Input.</summary>
        </member>
        <member name="P:System.SR.SqlParameter_DBNullNotSupportedForTVP">
            <summary>DBNull value for parameter '{0}' is not supported. Table-valued parameters cannot be DBNull.</summary>
        </member>
        <member name="P:System.SR.SqlParameter_UnexpectedTypeNameForNonStruct">
            <summary>TypeName specified for parameter '{0}'.  TypeName must only be set for Structured parameters.</summary>
        </member>
        <member name="P:System.SR.NullSchemaTableDataTypeNotSupported">
            <summary>DateType column for field '{0}' in schema table is null.  DataType must be non-null.</summary>
        </member>
        <member name="P:System.SR.InvalidSchemaTableOrdinals">
            <summary>Invalid column ordinals in schema table.  ColumnOrdinals, if present, must not have duplicates or gaps.</summary>
        </member>
        <member name="P:System.SR.SQL_EnumeratedRecordMetaDataChanged">
            <summary>Metadata for field '{0}' of record '{1}' did not match the original record's metadata.</summary>
        </member>
        <member name="P:System.SR.SQL_EnumeratedRecordFieldCountChanged">
            <summary>Number of fields in record '{0}' does not match the number in the original record.</summary>
        </member>
        <member name="P:System.SR.GT_Disabled">
            <summary>Global Transactions are not enabled for this Azure SQL Database. Please contact Azure SQL Database support for assistance.</summary>
        </member>
        <member name="P:System.SR.SQL_UnknownSysTxIsolationLevel">
            <summary>Unrecognized System.Transactions.IsolationLevel enumeration value: {0}.</summary>
        </member>
        <member name="P:System.SR.SQLNotify_AlreadyHasCommand">
            <summary>This SqlCommand object is already associated with another SqlDependency object.</summary>
        </member>
        <member name="P:System.SR.SqlDependency_DatabaseBrokerDisabled">
            <summary>The SQL Server Service Broker for the current database is not enabled, and as a result query notifications are not supported.  Please enable the Service Broker for this database if you wish to use notifications.</summary>
        </member>
        <member name="P:System.SR.SqlDependency_DefaultOptionsButNoStart">
            <summary>When using SqlDependency without providing an options value, SqlDependency.Start() must be called prior to execution of a command added to the SqlDependency instance.</summary>
        </member>
        <member name="P:System.SR.SqlDependency_NoMatchingServerStart">
            <summary>When using SqlDependency without providing an options value, SqlDependency.Start() must be called for each server that is being executed against.</summary>
        </member>
        <member name="P:System.SR.SqlDependency_NoMatchingServerDatabaseStart">
            <summary>SqlDependency.Start has been called for the server the command is executing against more than once, but there is no matching server/user/database Start() call for current command.</summary>
        </member>
        <member name="P:System.SR.SqlDependency_EventNoDuplicate">
            <summary>SqlDependency.OnChange does not support multiple event registrations for the same delegate.</summary>
        </member>
        <member name="P:System.SR.SqlDependency_IdMismatch">
            <summary>No SqlDependency exists for the key.</summary>
        </member>
        <member name="P:System.SR.SqlDependency_InvalidTimeout">
            <summary>Timeout specified is invalid. Timeout cannot be &lt; 0.</summary>
        </member>
        <member name="P:System.SR.SqlDependency_DuplicateStart">
            <summary>SqlDependency does not support calling Start() with different connection strings having the same server, user, and database in the same app domain.</summary>
        </member>
        <member name="P:System.SR.SqlMetaData_InvalidSqlDbTypeForConstructorFormat">
            <summary>The dbType {0} is invalid for this constructor.</summary>
        </member>
        <member name="P:System.SR.SqlMetaData_NameTooLong">
            <summary>The name is too long.</summary>
        </member>
        <member name="P:System.SR.SqlMetaData_SpecifyBothSortOrderAndOrdinal">
            <summary>The sort order and ordinal must either both be specified, or neither should be specified (SortOrder.Unspecified and -1).  The values given were: order = {0}, ordinal = {1}.</summary>
        </member>
        <member name="P:System.SR.SqlProvider_InvalidDataColumnType">
            <summary>The type of column '{0}' is not supported.  The type is '{1}'</summary>
        </member>
        <member name="P:System.SR.SqlProvider_NotEnoughColumnsInStructuredType">
            <summary>There are not enough fields in the Structured type.  Structured types must have at least one field.</summary>
        </member>
        <member name="P:System.SR.SqlProvider_DuplicateSortOrdinal">
            <summary>The sort ordinal {0} was specified twice.</summary>
        </member>
        <member name="P:System.SR.SqlProvider_MissingSortOrdinal">
            <summary>The sort ordinal {0} was not specified.</summary>
        </member>
        <member name="P:System.SR.SqlProvider_SortOrdinalGreaterThanFieldCount">
            <summary>The sort ordinal {0} on field {1} exceeds the total number of fields.</summary>
        </member>
        <member name="P:System.SR.SQLUDT_MaxByteSizeValue">
            <summary>range: 0-8000</summary>
        </member>
        <member name="P:System.SR.SQLUDT_Unexpected">
            <summary>unexpected error encountered in SqlClient data provider. {0}</summary>
        </member>
        <member name="P:System.SR.SQLUDT_UnexpectedUdtTypeName">
            <summary>UdtTypeName property must be set only for UDT parameters.</summary>
        </member>
        <member name="P:System.SR.SQLUDT_InvalidUdtTypeName">
            <summary>UdtTypeName property must be set for UDT parameters.</summary>
        </member>
        <member name="P:System.SR.SqlUdt_InvalidUdtMessage">
            <summary>'{0}' is an invalid user defined type, reason: {1}.</summary>
        </member>
        <member name="P:System.SR.SQL_UDTTypeName">
            <summary>SqlParameter.UdtTypeName is an invalid multipart name</summary>
        </member>
        <member name="P:System.SR.SQL_InvalidUdt3PartNameFormat">
            <summary>Invalid 3 part name format for UdtTypeName.</summary>
        </member>
        <member name="P:System.SR.IEnumerableOfSqlDataRecordHasNoRows">
            <summary>There are no records in the SqlDataRecord enumeration. To send a table-valued parameter with no rows, use a null reference for the value instead.</summary>
        </member>
        <member name="P:System.SR.SNI_ERROR_1">
            <summary>I/O Error detected in read/write operation</summary>
        </member>
        <member name="P:System.SR.SNI_ERROR_2">
            <summary>Connection was terminated</summary>
        </member>
        <member name="P:System.SR.SNI_ERROR_3">
            <summary>Asynchronous operations not supported</summary>
        </member>
        <member name="P:System.SR.SNI_ERROR_5">
            <summary>Invalid parameter(s) found</summary>
        </member>
        <member name="P:System.SR.SNI_ERROR_6">
            <summary>Unsupported protocol specified</summary>
        </member>
        <member name="P:System.SR.SNI_ERROR_7">
            <summary>Invalid connection found when setting up new session protocol</summary>
        </member>
        <member name="P:System.SR.SNI_ERROR_8">
            <summary>Protocol not supported</summary>
        </member>
        <member name="P:System.SR.SNI_ERROR_9">
            <summary>Associating port with I/O completion mechanism failed</summary>
        </member>
        <member name="P:System.SR.SNI_ERROR_11">
            <summary>Timeout error</summary>
        </member>
        <member name="P:System.SR.SNI_ERROR_12">
            <summary>No server name supplied</summary>
        </member>
        <member name="P:System.SR.SNI_ERROR_13">
            <summary>TerminateListener() has been called</summary>
        </member>
        <member name="P:System.SR.SNI_ERROR_14">
            <summary>Win9x not supported</summary>
        </member>
        <member name="P:System.SR.SNI_ERROR_15">
            <summary>Function not supported</summary>
        </member>
        <member name="P:System.SR.SNI_ERROR_16">
            <summary>Shared-Memory heap error</summary>
        </member>
        <member name="P:System.SR.SNI_ERROR_17">
            <summary>Cannot find an ip/ipv6 type address to connect</summary>
        </member>
        <member name="P:System.SR.SNI_ERROR_18">
            <summary>Connection has been closed by peer</summary>
        </member>
        <member name="P:System.SR.SNI_ERROR_19">
            <summary>Physical connection is not usable</summary>
        </member>
        <member name="P:System.SR.SNI_ERROR_20">
            <summary>Connection has been closed</summary>
        </member>
        <member name="P:System.SR.SNI_ERROR_21">
            <summary>Encryption is enforced but there is no valid certificate</summary>
        </member>
        <member name="P:System.SR.SNI_ERROR_22">
            <summary>Couldn't load library</summary>
        </member>
        <member name="P:System.SR.SNI_ERROR_23">
            <summary>Cannot open a new thread in server process</summary>
        </member>
        <member name="P:System.SR.SNI_ERROR_24">
            <summary>Cannot post event to completion port</summary>
        </member>
        <member name="P:System.SR.SNI_ERROR_25">
            <summary>Connection string is not valid</summary>
        </member>
        <member name="P:System.SR.SNI_ERROR_26">
            <summary>Error Locating Server/Instance Specified</summary>
        </member>
        <member name="P:System.SR.SNI_ERROR_27">
            <summary>Error getting enabled protocols list from registry</summary>
        </member>
        <member name="P:System.SR.SNI_ERROR_28">
            <summary>Server doesn't support requested protocol</summary>
        </member>
        <member name="P:System.SR.SNI_ERROR_29">
            <summary>Shared Memory is not supported for clustered server connectivity</summary>
        </member>
        <member name="P:System.SR.SNI_ERROR_30">
            <summary>Invalid attempt bind to shared memory segment</summary>
        </member>
        <member name="P:System.SR.SNI_ERROR_31">
            <summary>Encryption(ssl/tls) handshake failed</summary>
        </member>
        <member name="P:System.SR.SNI_ERROR_32">
            <summary>Packet size too large for SSL Encrypt/Decrypt operations</summary>
        </member>
        <member name="P:System.SR.SNI_ERROR_33">
            <summary>SSRP error</summary>
        </member>
        <member name="P:System.SR.SNI_ERROR_34">
            <summary>Could not connect to the Shared Memory pipe</summary>
        </member>
        <member name="P:System.SR.SNI_ERROR_35">
            <summary>An internal exception was caught</summary>
        </member>
        <member name="P:System.SR.SNI_ERROR_36">
            <summary>The Shared Memory dll used to connect to SQL Server 2000 was not found</summary>
        </member>
        <member name="P:System.SR.SNI_ERROR_37">
            <summary>The SQL Server 2000 Shared Memory client dll appears to be invalid/corrupted</summary>
        </member>
        <member name="P:System.SR.SNI_ERROR_38">
            <summary>Cannot open a Shared Memory connection to SQL Server 2000</summary>
        </member>
        <member name="P:System.SR.SNI_ERROR_39">
            <summary>Shared memory connectivity to SQL Server 2000 is either disabled or not available on this machine</summary>
        </member>
        <member name="P:System.SR.SNI_ERROR_40">
            <summary>Could not open a connection to SQL Server</summary>
        </member>
        <member name="P:System.SR.SNI_ERROR_41">
            <summary>Cannot open a Shared Memory connection to a remote SQL server</summary>
        </member>
        <member name="P:System.SR.SNI_ERROR_42">
            <summary>Could not establish dedicated administrator connection (DAC) on default port. Make sure that DAC is enabled</summary>
        </member>
        <member name="P:System.SR.SNI_ERROR_43">
            <summary>An error occurred while obtaining the dedicated administrator connection (DAC) port. Make sure that SQL Browser is running, or check the error log for the port number</summary>
        </member>
        <member name="P:System.SR.SNI_ERROR_44">
            <summary>Could not compose Service Principal Name (SPN) for Windows Integrated Authentication. Possible causes are server(s) incorrectly specified to connection API calls, Domain Name System (DNS) lookup failure or memory shortage</summary>
        </member>
        <member name="P:System.SR.SNI_ERROR_47">
            <summary>Connecting with the MultiSubnetFailover connection option to a SQL Server instance configured with more than 64 IP addresses is not supported.</summary>
        </member>
        <member name="P:System.SR.SNI_ERROR_48">
            <summary>Connecting to a named SQL Server instance using the MultiSubnetFailover connection option is not supported.</summary>
        </member>
        <member name="P:System.SR.SNI_ERROR_49">
            <summary>Connecting to a SQL Server instance using the MultiSubnetFailover connection option is only supported when using the TCP protocol.</summary>
        </member>
        <member name="P:System.SR.SNI_ERROR_50">
            <summary>Local Database Runtime error occurred.</summary>
        </member>
        <member name="P:System.SR.SNI_ERROR_51">
            <summary>An instance name was not specified while connecting to a Local Database Runtime. Specify an instance name in the format (localdb)\instance_name.</summary>
        </member>
        <member name="P:System.SR.SNI_ERROR_52">
            <summary>Unable to locate a Local Database Runtime installation. Verify that SQL Server Express is properly installed and that the Local Database Runtime feature is enabled.</summary>
        </member>
        <member name="P:System.SR.SNI_ERROR_53">
            <summary>Invalid Local Database Runtime registry configuration found. Verify that SQL Server Express is properly installed.</summary>
        </member>
        <member name="P:System.SR.SNI_ERROR_54">
            <summary>Unable to locate the registry entry for SQLUserInstance.dll file path. Verify that the Local Database Runtime feature of SQL Server Express is properly installed.</summary>
        </member>
        <member name="P:System.SR.SNI_ERROR_55">
            <summary>Registry value contains an invalid SQLUserInstance.dll file path. Verify that the Local Database Runtime feature of SQL Server Express is properly installed.</summary>
        </member>
        <member name="P:System.SR.SNI_ERROR_56">
            <summary>Unable to load the SQLUserInstance.dll from the location specified in the registry. Verify that the Local Database Runtime feature of SQL Server Express is properly installed.</summary>
        </member>
        <member name="P:System.SR.SNI_ERROR_57">
            <summary>Invalid SQLUserInstance.dll found at the location specified in the registry. Verify that the Local Database Runtime feature of SQL Server Express is properly installed.</summary>
        </member>
        <member name="P:System.SR.Snix_Connect">
            <summary>A network-related or instance-specific error occurred while establishing a connection to SQL Server. The server was not found or was not accessible. Verify that the instance name is correct and that SQL Server is configured to allow remote connections.</summary>
        </member>
        <member name="P:System.SR.Snix_PreLoginBeforeSuccessfulWrite">
            <summary>The client was unable to establish a connection because of an error during connection initialization process before login. Possible causes include the following:  the client tried to connect to an unsupported version of SQL Server; the server was too busy  ...</summary>
        </member>
        <member name="P:System.SR.Snix_PreLogin">
            <summary>A connection was successfully established with the server, but then an error occurred during the pre-login handshake.</summary>
        </member>
        <member name="P:System.SR.Snix_LoginSspi">
            <summary>A connection was successfully established with the server, but then an error occurred when obtaining the security/SSPI context information for integrated security login.</summary>
        </member>
        <member name="P:System.SR.Snix_Login">
            <summary>A connection was successfully established with the server, but then an error occurred during the login process.</summary>
        </member>
        <member name="P:System.SR.Snix_EnableMars">
            <summary>Connection open and login was successful, but then an error occurred while enabling MARS for this connection.</summary>
        </member>
        <member name="P:System.SR.Snix_AutoEnlist">
            <summary>Connection open and login was successful, but then an error occurred while enlisting the connection into the current distributed transaction.</summary>
        </member>
        <member name="P:System.SR.Snix_GetMarsSession">
            <summary>Failed to establish a MARS session in preparation to send the request to the server.</summary>
        </member>
        <member name="P:System.SR.Snix_Execute">
            <summary>A transport-level error has occurred when sending the request to the server.</summary>
        </member>
        <member name="P:System.SR.Snix_Read">
            <summary>A transport-level error has occurred when receiving results from the server.</summary>
        </member>
        <member name="P:System.SR.Snix_Close">
            <summary>A transport-level error has occurred during connection clean-up.</summary>
        </member>
        <member name="P:System.SR.Snix_SendRows">
            <summary>A transport-level error has occurred while sending information to the server.</summary>
        </member>
        <member name="P:System.SR.Snix_ProcessSspi">
            <summary>A transport-level error has occurred during SSPI handshake.</summary>
        </member>
        <member name="P:System.SR.LocalDB_FailedGetDLLHandle">
            <summary>Local Database Runtime: Cannot load SQLUserInstance.dll.</summary>
        </member>
        <member name="P:System.SR.LocalDB_MethodNotFound">
            <summary>Invalid SQLUserInstance.dll found at the location specified in the registry. Verify that the Local Database Runtime feature of SQL Server Express is properly installed.</summary>
        </member>
        <member name="P:System.SR.LocalDB_UnobtainableMessage">
            <summary>Cannot obtain Local Database Runtime error message</summary>
        </member>
        <member name="P:System.SR.SQLROR_RecursiveRoutingNotSupported">
            <summary>Two or more redirections have occurred. Only one redirection per login is allowed.</summary>
        </member>
        <member name="P:System.SR.SQLROR_FailoverNotSupported">
            <summary>Connecting to a mirrored SQL Server instance using the ApplicationIntent ReadOnly connection option is not supported.</summary>
        </member>
        <member name="P:System.SR.SQLROR_UnexpectedRoutingInfo">
            <summary>Unexpected routing information received.</summary>
        </member>
        <member name="P:System.SR.SQLROR_InvalidRoutingInfo">
            <summary>Invalid routing information received.</summary>
        </member>
        <member name="P:System.SR.SQLROR_TimeoutAfterRoutingInfo">
            <summary>Server provided routing information, but timeout already expired.</summary>
        </member>
        <member name="P:System.SR.SQLCR_InvalidConnectRetryCountValue">
            <summary>Invalid ConnectRetryCount value (should be 0-255).</summary>
        </member>
        <member name="P:System.SR.SQLCR_InvalidConnectRetryIntervalValue">
            <summary>Invalid ConnectRetryInterval value (should be 1-60).</summary>
        </member>
        <member name="P:System.SR.SQLCR_NextAttemptWillExceedQueryTimeout">
            <summary>Next reconnection attempt will exceed query timeout. Reconnection was terminated.</summary>
        </member>
        <member name="P:System.SR.SQLCR_EncryptionChanged">
            <summary>The server did not preserve SSL encryption during a recovery attempt, connection recovery is not possible.</summary>
        </member>
        <member name="P:System.SR.SQLCR_TDSVestionNotPreserved">
            <summary>The server did not preserve the exact client TDS version requested during a recovery attempt, connection recovery is not possible.</summary>
        </member>
        <member name="P:System.SR.SQLCR_AllAttemptsFailed">
            <summary>The connection is broken and recovery is not possible.  The client driver attempted to recover the connection one or more times and all attempts failed.  Increase the value of ConnectRetryCount to increase the number of recovery attempts.</summary>
        </member>
        <member name="P:System.SR.SQLCR_UnrecoverableServer">
            <summary>The connection is broken and recovery is not possible.  The connection is marked by the server as unrecoverable.  No attempt was made to restore the connection.</summary>
        </member>
        <member name="P:System.SR.SQLCR_UnrecoverableClient">
            <summary>The connection is broken and recovery is not possible.  The connection is marked by the client driver as unrecoverable.  No attempt was made to restore the connection.</summary>
        </member>
        <member name="P:System.SR.SQLCR_NoCRAckAtReconnection">
            <summary>The server did not acknowledge a recovery attempt, connection recovery is not possible.</summary>
        </member>
        <member name="P:System.SR.SQL_UnsupportedKeyword">
            <summary>The keyword '{0}' is not supported on this platform.</summary>
        </member>
        <member name="P:System.SR.SQL_UnsupportedFeature">
            <summary>The server is attempting to use a feature that is not supported on this platform.</summary>
        </member>
        <member name="P:System.SR.SQL_UnsupportedToken">
            <summary>Received an unsupported token '{0}' while reading data from the server.</summary>
        </member>
        <member name="P:System.SR.SQL_DbTypeNotSupportedOnThisPlatform">
            <summary>Type {0} is not supported on this platform.</summary>
        </member>
        <member name="P:System.SR.SQL_NetworkLibraryNotSupported">
            <summary>The keyword 'Network Library' is not supported on this platform, prefix the 'Data Source' with the protocol desired instead ('tcp:' for a TCP connection, or 'np:' for a Named Pipe connection).</summary>
        </member>
        <member name="P:System.SR.SNI_PN0">
            <summary>HTTP Provider</summary>
        </member>
        <member name="P:System.SR.SNI_PN1">
            <summary>Named Pipes Provider</summary>
        </member>
        <member name="P:System.SR.SNI_PN2">
            <summary>Session Provider</summary>
        </member>
        <member name="P:System.SR.SNI_PN3">
            <summary>Sign Provider</summary>
        </member>
        <member name="P:System.SR.SNI_PN4">
            <summary>Shared Memory Provider</summary>
        </member>
        <member name="P:System.SR.SNI_PN5">
            <summary>SMux Provider</summary>
        </member>
        <member name="P:System.SR.SNI_PN6">
            <summary>SSL Provider</summary>
        </member>
        <member name="P:System.SR.SNI_PN7">
            <summary>TCP Provider</summary>
        </member>
        <member name="P:System.SR.SNI_PN8">
            <summary></summary>
        </member>
        <member name="P:System.SR.SNI_PN9">
            <summary>SQL Network Interfaces</summary>
        </member>
        <member name="P:System.SR.AZURESQL_GenericEndpoint">
            <summary>.database.windows.net</summary>
        </member>
        <member name="P:System.SR.AZURESQL_GermanEndpoint">
            <summary>.database.cloudapi.de</summary>
        </member>
        <member name="P:System.SR.AZURESQL_UsGovEndpoint">
            <summary>.database.usgovcloudapi.net</summary>
        </member>
        <member name="P:System.SR.AZURESQL_ChinaEndpoint">
            <summary>.database.chinacloudapi.cn</summary>
        </member>
        <member name="P:System.SR.net_gssapi_operation_failed_detailed">
            <summary>GSSAPI operation failed with error - {0} ({1}).</summary>
        </member>
        <member name="P:System.SR.net_gssapi_operation_failed">
            <summary>GSSAPI operation failed with status: {0} (Minor status: {1}).</summary>
        </member>
        <member name="P:System.SR.net_gssapi_operation_failed_detailed_majoronly">
            <summary>GSSAPI operation failed with error - {0}.</summary>
        </member>
        <member name="P:System.SR.net_gssapi_operation_failed_majoronly">
            <summary>GSSAPI operation failed with status: {0}.</summary>
        </member>
        <member name="P:System.SR.net_gssapi_ntlm_missing_plugin">
            <summary>NTLM authentication requires the GSSAPI plugin 'gss-ntlmssp'.</summary>
        </member>
        <member name="P:System.SR.net_ntlm_not_possible_default_cred">
            <summary>NTLM authentication is not possible with default credentials on this platform.</summary>
        </member>
        <member name="P:System.SR.net_nego_not_supported_empty_target_with_defaultcreds">
            <summary>Target name should be non empty if default credentials are passed.</summary>
        </member>
        <member name="P:System.SR.net_nego_server_not_supported">
            <summary>Server implementation is not supported</summary>
        </member>
        <member name="P:System.SR.net_nego_protection_level_not_supported">
            <summary>Requested protection level is not supported with the GSSAPI implementation currently installed.</summary>
        </member>
        <member name="P:System.SR.net_context_buffer_too_small">
            <summary>Insufficient buffer space. Required: {0} Actual: {1}.</summary>
        </member>
        <member name="P:System.SR.net_auth_message_not_encrypted">
            <summary>Protocol error: A received message contains a valid signature but it was not encrypted as required by the effective Protection Level.</summary>
        </member>
        <member name="P:System.SR.net_securitypackagesupport">
            <summary>The requested security package is not supported.</summary>
        </member>
        <member name="P:System.SR.net_log_operation_failed_with_error">
            <summary>{0} failed with error {1}.</summary>
        </member>
        <member name="P:System.SR.net_MethodNotImplementedException">
            <summary>This method is not implemented by this class.</summary>
        </member>
        <member name="P:System.SR.event_OperationReturnedSomething">
            <summary>{0} returned {1}.</summary>
        </member>
        <member name="P:System.SR.net_invalid_enum">
            <summary>The specified value is not valid in the '{0}' enumeration.</summary>
        </member>
        <member name="P:System.SR.SSPIInvalidHandleType">
            <summary>'{0}' is not a supported handle type.</summary>
        </member>
        <member name="P:System.SR.LocalDBNotSupported">
            <summary>LocalDB is not supported on this platform.</summary>
        </member>
        <member name="P:System.SR.PlatformNotSupported_DataSqlClient">
            <summary>System.Data.SqlClient is not supported on this platform.</summary>
        </member>
        <member name="P:System.SR.SqlParameter_InvalidTableDerivedPrecisionForTvp">
            <summary>Precision '{0}' required to send all values in column '{1}' exceeds the maximum supported precision '{2}'. The values must all fit in a single precision.</summary>
        </member>
        <member name="P:System.SR.SqlProvider_InvalidDataColumnMaxLength">
            <summary>The size of column '{0}' is not supported. The size is {1}.</summary>
        </member>
        <member name="P:System.SR.MDF_InvalidXmlInvalidValue">
            <summary>The metadata XML is invalid. The {1} column of the {0} collection must contain a non-empty string.</summary>
        </member>
        <member name="P:System.SR.MDF_CollectionNameISNotUnique">
            <summary>There are multiple collections named '{0}'.</summary>
        </member>
        <member name="P:System.SR.MDF_InvalidXmlMissingColumn">
            <summary>The metadata XML is invalid. The {0} collection must contain a {1} column and it must be a string column.</summary>
        </member>
        <member name="P:System.SR.MDF_InvalidXml">
            <summary>The metadata XML is invalid.</summary>
        </member>
        <member name="P:System.SR.MDF_NoColumns">
            <summary>The schema table contains no columns.</summary>
        </member>
        <member name="P:System.SR.MDF_QueryFailed">
            <summary>Unable to build the '{0}' collection because execution of the SQL query failed. See the inner exception for details.</summary>
        </member>
        <member name="P:System.SR.MDF_TooManyRestrictions">
            <summary>More restrictions were provided than the requested schema ('{0}') supports.</summary>
        </member>
        <member name="P:System.SR.MDF_DataTableDoesNotExist">
            <summary>The collection '{0}' is missing from the metadata XML.</summary>
        </member>
        <member name="P:System.SR.MDF_UndefinedCollection">
            <summary>The requested collection ({0}) is not defined.</summary>
        </member>
        <member name="P:System.SR.MDF_UnsupportedVersion">
            <summary>The requested collection ({0}) is not supported by this version of the provider.</summary>
        </member>
        <member name="P:System.SR.MDF_MissingRestrictionColumn">
            <summary>One or more of the required columns of the restrictions collection is missing.</summary>
        </member>
        <member name="P:System.SR.MDF_MissingRestrictionRow">
            <summary>A restriction exists for which there is no matching row in the restrictions collection.</summary>
        </member>
        <member name="P:System.SR.MDF_IncorrectNumberOfDataSourceInformationRows">
            <summary>The DataSourceInformation table must contain exactly one row.</summary>
        </member>
        <member name="P:System.SR.MDF_MissingDataSourceInformationColumn">
            <summary>One of the required DataSourceInformation tables columns is missing.</summary>
        </member>
        <member name="P:System.SR.MDF_AmbigousCollectionName">
            <summary>The collection name '{0}' matches at least two collections with the same name but with different case, but does not match any of them exactly.</summary>
        </member>
        <member name="P:System.SR.MDF_UnableToBuildCollection">
            <summary>Unable to build schema collection '{0}';</summary>
        </member>
        <member name="P:System.SR.ADP_InvalidArgumentLength">
            <summary>The length of argument '{0}' exceeds its limit of '{1}'.</summary>
        </member>
        <member name="P:System.SR.ADP_MustBeReadOnly">
            <summary>{0} must be marked as read only.</summary>
        </member>
        <member name="P:System.SR.ADP_InvalidMixedUsageOfSecureAndClearCredential">
            <summary>Cannot use Credential with UserID, UID, Password, or PWD connection string keywords.</summary>
        </member>
        <member name="P:System.SR.ADP_InvalidMixedUsageOfSecureCredentialAndIntegratedSecurity">
            <summary>Cannot use Credential with Integrated Security connection string keyword.</summary>
        </member>
        <member name="P:System.SR.SQL_ChangePasswordArgumentMissing">
            <summary>The '{0}' argument must not be null or empty.</summary>
        </member>
        <member name="P:System.SR.SQL_ChangePasswordConflictsWithSSPI">
            <summary>ChangePassword can only be used with SQL authentication, not with integrated security.</summary>
        </member>
        <member name="P:System.SR.SQL_ChangePasswordRequiresYukon">
            <summary>ChangePassword requires SQL Server 9.0 or later.</summary>
        </member>
        <member name="P:System.SR.SQL_ChangePasswordUseOfUnallowedKey">
            <summary>The keyword '{0}' must not be specified in the connectionString argument to ChangePassword.</summary>
        </member>
        <member name="P:System.SR.SQL_ParsingErrorWithState">
            <summary>Internal connection fatal error. Error state: {0}.</summary>
        </member>
        <member name="P:System.SR.SQL_ParsingErrorValue">
            <summary>Internal connection fatal error. Error state: {0}, Value: {1}.</summary>
        </member>
        <member name="P:System.SR.ADP_InvalidMixedUsageOfAccessTokenAndIntegratedSecurity">
            <summary>Cannot set the AccessToken property if the 'Integrated Security' connection string keyword has been set to 'true' or 'SSPI'.</summary>
        </member>
        <member name="P:System.SR.ADP_InvalidMixedUsageOfAccessTokenAndUserIDPassword">
            <summary>Cannot set the AccessToken property if 'UserID', 'UID', 'Password', or 'PWD' has been specified in connection string.</summary>
        </member>
        <member name="P:System.SR.ADP_InvalidMixedUsageOfCredentialAndAccessToken">
            <summary>Cannot set the Credential property if the AccessToken property is already set.</summary>
        </member>
        <member name="P:System.SR.SQL_ParsingErrorFeatureId">
            <summary>Internal connection fatal error. Error state: {0}, Feature Id: {1}.</summary>
        </member>
        <member name="P:System.SR.SQL_ParsingErrorAuthLibraryType">
            <summary>Internal connection fatal error. Error state: {0}, Authentication Library Type: {1}.</summary>
        </member>
        <member name="P:System.SR.SqlFileStream_InvalidPath">
            <summary>The path name is not valid.</summary>
        </member>
        <member name="P:System.SR.SqlFileStream_PathNotValidDiskResource">
            <summary>The path name is invalid or does not point to a disk file.</summary>
        </member>
        <member name="P:System.SR.SqlFileStream_FileAlreadyInTransaction">
            <summary>The process cannot access the file specified because it has been opened in another transaction.</summary>
        </member>
        <member name="P:System.SR.SqlFileStream_InvalidParameter">
            <summary>An invalid parameter was passed to the function.</summary>
        </member>
        <member name="P:System.SR.SqlFileStream_NotSupported">
            <summary>SqlFileStream is not supported on this platform.</summary>
        </member>
        <member name="T:System.Threading.Tasks.TaskToApm">
            <summary>
            Provides support for efficiently using Tasks to implement the APM (Begin/End) pattern.
            </summary>
        </member>
        <member name="M:System.Threading.Tasks.TaskToApm.Begin(System.Threading.Tasks.Task,System.AsyncCallback,System.Object)">
            <summary>
            Marshals the Task as an IAsyncResult, using the supplied callback and state
            to implement the APM pattern.
            </summary>
            <param name="task">The Task to be marshaled.</param>
            <param name="callback">The callback to be invoked upon completion.</param>
            <param name="state">The state to be stored in the IAsyncResult.</param>
            <returns>An IAsyncResult to represent the task's asynchronous operation.</returns>
        </member>
        <member name="M:System.Threading.Tasks.TaskToApm.End(System.IAsyncResult)">
            <summary>Processes an IAsyncResult returned by Begin.</summary>
            <param name="asyncResult">The IAsyncResult to unwrap.</param>
        </member>
        <member name="M:System.Threading.Tasks.TaskToApm.End``1(System.IAsyncResult)">
            <summary>Processes an IAsyncResult returned by Begin.</summary>
            <param name="asyncResult">The IAsyncResult to unwrap.</param>
        </member>
        <member name="M:System.Threading.Tasks.TaskToApm.InvokeCallbackWhenTaskCompletes(System.Threading.Tasks.Task,System.AsyncCallback,System.IAsyncResult)">
            <summary>Invokes the callback asynchronously when the task has completed.</summary>
            <param name="antecedent">The Task to await.</param>
            <param name="callback">The callback to invoke when the Task completes.</param>
            <param name="asyncResult">The Task used as the IAsyncResult.</param>
        </member>
        <member name="T:System.Threading.Tasks.TaskToApm.TaskWrapperAsyncResult">
            <summary>
            Provides a simple IAsyncResult that wraps a Task.  This, in effect, allows
            for overriding what's seen for the CompletedSynchronously and AsyncState values.
            </summary>
        </member>
        <member name="F:System.Threading.Tasks.TaskToApm.TaskWrapperAsyncResult.Task">
            <summary>The wrapped Task.</summary>
        </member>
        <member name="F:System.Threading.Tasks.TaskToApm.TaskWrapperAsyncResult._state">
            <summary>The new AsyncState value.</summary>
        </member>
        <member name="F:System.Threading.Tasks.TaskToApm.TaskWrapperAsyncResult._completedSynchronously">
            <summary>The new CompletedSynchronously value.</summary>
        </member>
        <member name="M:System.Threading.Tasks.TaskToApm.TaskWrapperAsyncResult.#ctor(System.Threading.Tasks.Task,System.Object,System.Boolean)">
            <summary>Initializes the IAsyncResult with the Task to wrap and the overriding AsyncState and CompletedSynchronously values.</summary>
            <param name="task">The Task to wrap.</param>
            <param name="state">The new AsyncState value</param>
            <param name="completedSynchronously">The new CompletedSynchronously value.</param>
        </member>
        <member name="T:System.IO.PathInternal">
            <summary>Contains internal path helpers that are shared between many projects.</summary>
        </member>
        <member name="M:System.IO.PathInternal.IsValidDriveChar(System.Char)">
            <summary>
            Returns true if the given character is a valid drive letter
            </summary>
        </member>
        <member name="M:System.IO.PathInternal.EnsureExtendedPrefixIfNeeded(System.String)">
            <summary>
            Adds the extended path prefix (\\?\) if not already a device path, IF the path is not relative,
            AND the path is more than 259 characters. (> MAX_PATH + null). This will also insert the extended
            prefix if the path ends with a period or a space. Trailing periods and spaces are normally eaten
            away from paths during normalization, but if we see such a path at this point it should be
            normalized and has retained the final characters. (Typically from one of the *Info classes)
            </summary>
        </member>
        <member name="M:System.IO.PathInternal.EnsureExtendedPrefix(System.String)">
            <summary>
            Adds the extended path prefix (\\?\) if not relative or already a device path.
            </summary>
        </member>
        <member name="M:System.IO.PathInternal.IsDevice(System.ReadOnlySpan{System.Char})">
            <summary>
            Returns true if the path uses any of the DOS device path syntaxes. ("\\.\", "\\?\", or "\??\")
            </summary>
        </member>
        <member name="M:System.IO.PathInternal.IsDeviceUNC(System.ReadOnlySpan{System.Char})">
            <summary>
            Returns true if the path is a device UNC (\\?\UNC\, \\.\UNC\)
            </summary>
        </member>
        <member name="M:System.IO.PathInternal.IsExtended(System.ReadOnlySpan{System.Char})">
            <summary>
            Returns true if the path uses the canonical form of extended syntax ("\\?\" or "\??\"). If the
            path matches exactly (cannot use alternate directory separators) Windows will skip normalization
            and path length checks.
            </summary>
        </member>
        <member name="M:System.IO.PathInternal.HasWildCardCharacters(System.ReadOnlySpan{System.Char})">
            <summary>
            Check for known wildcard characters. '*' and '?' are the most common ones.
            </summary>
        </member>
        <member name="M:System.IO.PathInternal.GetRootLength(System.ReadOnlySpan{System.Char})">
            <summary>
            Gets the length of the root of the path (drive, share, etc.).
            </summary>
        </member>
        <member name="M:System.IO.PathInternal.IsPartiallyQualified(System.ReadOnlySpan{System.Char})">
            <summary>
            Returns true if the path specified is relative to the current drive or working directory.
            Returns false if the path is fixed to a specific drive or UNC path.  This method does no
            validation of the path (URIs will be returned as relative as a result).
            </summary>
            <remarks>
            Handles paths that use the alternate directory separator.  It is a frequent mistake to
            assume that rooted paths (Path.IsPathRooted) are not relative.  This isn't the case.
            "C:a" is drive relative- meaning that it will be resolved against the current directory
            for C: (rooted, but relative). "C:\a" is rooted and not relative (the current directory
            will not be used to modify the path).
            </remarks>
        </member>
        <member name="M:System.IO.PathInternal.IsDirectorySeparator(System.Char)">
            <summary>
            True if the given character is a directory separator.
            </summary>
        </member>
        <member name="M:System.IO.PathInternal.NormalizeDirectorySeparators(System.String)">
            <summary>
            Normalize separators in the given path. Converts forward slashes into back slashes and compresses slash runs, keeping initial 2 if present.
            Also trims initial whitespace in front of "rooted" paths (see PathStartSkip).
            
            This effectively replicates the behavior of the legacy NormalizePath when it was called with fullCheck=false and expandShortpaths=false.
            The current NormalizePath gets directory separator normalization from Win32's GetFullPathName(), which will resolve relative paths and as
            such can't be used here (and is overkill for our uses).
            
            Like the current NormalizePath this will not try and analyze periods/spaces within directory segments.
            </summary>
            <remarks>
            The only callers that used to use Path.Normalize(fullCheck=false) were Path.GetDirectoryName() and Path.GetPathRoot(). Both usages do
            not need trimming of trailing whitespace here.
            
            GetPathRoot() could technically skip normalizing separators after the second segment- consider as a future optimization.
            
            For legacy desktop behavior with ExpandShortPaths:
             - It has no impact on GetPathRoot() so doesn't need consideration.
             - It could impact GetDirectoryName(), but only if the path isn't relative (C:\ or \\Server\Share).
            
            In the case of GetDirectoryName() the ExpandShortPaths behavior was undocumented and provided inconsistent results if the path was
            fixed/relative. For example: "C:\PROGRA~1\A.TXT" would return "C:\Program Files" while ".\PROGRA~1\A.TXT" would return ".\PROGRA~1". If you
            ultimately call GetFullPath() this doesn't matter, but if you don't or have any intermediate string handling could easily be tripped up by
            this undocumented behavior.
            
            We won't match this old behavior because:
            
              1. It was undocumented
              2. It was costly (extremely so if it actually contained '~')
              3. Doesn't play nice with string logic
              4. Isn't a cross-plat friendly concept/behavior
            </remarks>
        </member>
        <member name="M:System.IO.PathInternal.IsEffectivelyEmpty(System.ReadOnlySpan{System.Char})">
            <summary>
            Returns true if the path is effectively empty for the current OS.
            For unix, this is empty or null. For Windows, this is empty, null, or 
            just spaces ((char)32).
            </summary>
        </member>
        <member name="M:System.Text.ValueStringBuilder.GetPinnableReference">
            <summary>
            Get a pinnable reference to the builder.
            Does not ensure there is a null char after <see cref="P:System.Text.ValueStringBuilder.Length"/>
            This overload is pattern matched in the C# 7.3+ compiler so you can omit
            the explicit method call, and write eg "fixed (char* c = builder)"
            </summary>
        </member>
        <member name="M:System.Text.ValueStringBuilder.GetPinnableReference(System.Boolean)">
            <summary>
            Get a pinnable reference to the builder.
            </summary>
            <param name="terminate">Ensures that the builder has a null char after <see cref="P:System.Text.ValueStringBuilder.Length"/></param>
        </member>
        <member name="P:System.Text.ValueStringBuilder.RawChars">
            <summary>Returns the underlying storage of the builder.</summary>
        </member>
        <member name="M:System.Text.ValueStringBuilder.AsSpan(System.Boolean)">
            <summary>
            Returns a span around the contents of the builder.
            </summary>
            <param name="terminate">Ensures that the builder has a null char after <see cref="P:System.Text.ValueStringBuilder.Length"/></param>
        </member>
        <member name="M:System.Text.ValueStringBuilder.Grow(System.Int32)">
            <summary>
            Resize the internal buffer either by doubling current buffer size or
            by adding <paramref name="additionalCapacityBeyondPos"/> to
            <see cref="F:System.Text.ValueStringBuilder._pos"/> whichever is greater.
            </summary>
            <param name="additionalCapacityBeyondPos">
            Number of chars requested beyond current position.
            </param>
        </member>
        <member name="T:System.Net.NetEventSource">
            <summary>Provides logging facilities for System.Net libraries.</summary>
        </member>
        <member name="F:System.Net.NetEventSource.Log">
            <summary>The single event source instance to use for all logging.</summary>
        </member>
        <member name="M:System.Net.NetEventSource.Enter(System.Object,System.FormattableString,System.String)">
            <summary>Logs entrance to a method.</summary>
            <param name="thisOrContextObject">`this`, or another object that serves to provide context for the operation.</param>
            <param name="formattableString">A description of the entrance, including any arguments to the call.</param>
            <param name="memberName">The calling member.</param>
        </member>
        <member name="M:System.Net.NetEventSource.Enter(System.Object,System.Object,System.String)">
            <summary>Logs entrance to a method.</summary>
            <param name="thisOrContextObject">`this`, or another object that serves to provide context for the operation.</param>
            <param name="arg0">The object to log.</param>
            <param name="memberName">The calling member.</param>
        </member>
        <member name="M:System.Net.NetEventSource.Enter(System.Object,System.Object,System.Object,System.String)">
            <summary>Logs entrance to a method.</summary>
            <param name="thisOrContextObject">`this`, or another object that serves to provide context for the operation.</param>
            <param name="arg0">The first object to log.</param>
            <param name="arg1">The second object to log.</param>
            <param name="memberName">The calling member.</param>
        </member>
        <member name="M:System.Net.NetEventSource.Enter(System.Object,System.Object,System.Object,System.Object,System.String)">
            <summary>Logs entrance to a method.</summary>
            <param name="thisOrContextObject">`this`, or another object that serves to provide context for the operation.</param>
            <param name="arg0">The first object to log.</param>
            <param name="arg1">The second object to log.</param>
            <param name="arg2">The third object to log.</param>
            <param name="memberName">The calling member.</param>
        </member>
        <member name="M:System.Net.NetEventSource.Exit(System.Object,System.FormattableString,System.String)">
            <summary>Logs exit from a method.</summary>
            <param name="thisOrContextObject">`this`, or another object that serves to provide context for the operation.</param>
            <param name="formattableString">A description of the exit operation, including any return values.</param>
            <param name="memberName">The calling member.</param>
        </member>
        <member name="M:System.Net.NetEventSource.Exit(System.Object,System.Object,System.String)">
            <summary>Logs exit from a method.</summary>
            <param name="thisOrContextObject">`this`, or another object that serves to provide context for the operation.</param>
            <param name="arg0">A return value from the member.</param>
            <param name="memberName">The calling member.</param>
        </member>
        <member name="M:System.Net.NetEventSource.Exit(System.Object,System.Object,System.Object,System.String)">
            <summary>Logs exit from a method.</summary>
            <param name="thisOrContextObject">`this`, or another object that serves to provide context for the operation.</param>
            <param name="arg0">A return value from the member.</param>
            <param name="arg1">A second return value from the member.</param>
            <param name="memberName">The calling member.</param>
        </member>
        <member name="M:System.Net.NetEventSource.Info(System.Object,System.FormattableString,System.String)">
            <summary>Logs an information message.</summary>
            <param name="thisOrContextObject">`this`, or another object that serves to provide context for the operation.</param>
            <param name="formattableString">The message to be logged.</param>
            <param name="memberName">The calling member.</param>
        </member>
        <member name="M:System.Net.NetEventSource.Info(System.Object,System.Object,System.String)">
            <summary>Logs an information message.</summary>
            <param name="thisOrContextObject">`this`, or another object that serves to provide context for the operation.</param>
            <param name="message">The message to be logged.</param>
            <param name="memberName">The calling member.</param>
        </member>
        <member name="M:System.Net.NetEventSource.Error(System.Object,System.FormattableString,System.String)">
            <summary>Logs an error message.</summary>
            <param name="thisOrContextObject">`this`, or another object that serves to provide context for the operation.</param>
            <param name="formattableString">The message to be logged.</param>
            <param name="memberName">The calling member.</param>
        </member>
        <member name="M:System.Net.NetEventSource.Error(System.Object,System.Object,System.String)">
            <summary>Logs an error message.</summary>
            <param name="thisOrContextObject">`this`, or another object that serves to provide context for the operation.</param>
            <param name="message">The message to be logged.</param>
            <param name="memberName">The calling member.</param>
        </member>
        <member name="M:System.Net.NetEventSource.Fail(System.Object,System.FormattableString,System.String)">
            <summary>Logs a fatal error and raises an assert.</summary>
            <param name="thisOrContextObject">`this`, or another object that serves to provide context for the operation.</param>
            <param name="formattableString">The message to be logged.</param>
            <param name="memberName">The calling member.</param>
        </member>
        <member name="M:System.Net.NetEventSource.Fail(System.Object,System.Object,System.String)">
            <summary>Logs a fatal error and raises an assert.</summary>
            <param name="thisOrContextObject">`this`, or another object that serves to provide context for the operation.</param>
            <param name="message">The message to be logged.</param>
            <param name="memberName">The calling member.</param>
        </member>
        <member name="M:System.Net.NetEventSource.DumpBuffer(System.Object,System.Byte[],System.String)">
            <summary>Logs the contents of a buffer.</summary>
            <param name="thisOrContextObject">`this`, or another object that serves to provide context for the operation.</param>
            <param name="buffer">The buffer to be logged.</param>
            <param name="memberName">The calling member.</param>
        </member>
        <member name="M:System.Net.NetEventSource.DumpBuffer(System.Object,System.Byte[],System.Int32,System.Int32,System.String)">
            <summary>Logs the contents of a buffer.</summary>
            <param name="thisOrContextObject">`this`, or another object that serves to provide context for the operation.</param>
            <param name="buffer">The buffer to be logged.</param>
            <param name="offset">The starting offset from which to log.</param>
            <param name="count">The number of bytes to log.</param>
            <param name="memberName">The calling member.</param>
        </member>
        <member name="M:System.Net.NetEventSource.DumpBuffer(System.Object,System.IntPtr,System.Int32,System.String)">
            <summary>Logs the contents of a buffer.</summary>
            <param name="thisOrContextObject">`this`, or another object that serves to provide context for the operation.</param>
            <param name="bufferPtr">The starting location of the buffer to be logged.</param>
            <param name="count">The number of bytes to log.</param>
            <param name="memberName">The calling member.</param>
        </member>
        <member name="M:System.Net.NetEventSource.Associate(System.Object,System.Object,System.String)">
            <summary>Logs a relationship between two objects.</summary>
            <param name="first">The first object.</param>
            <param name="second">The second object.</param>
            <param name="memberName">The calling member.</param>
        </member>
        <member name="M:System.Net.NetEventSource.Associate(System.Object,System.Object,System.Object,System.String)">
            <summary>Logs a relationship between two objects.</summary>
            <param name="thisOrContextObject">`this`, or another object that serves to provide context for the operation.</param>
            <param name="first">The first object.</param>
            <param name="second">The second object.</param>
            <param name="memberName">The calling member.</param>
        </member>
        <member name="P:Microsoft.SqlServer.Server.SqlFacetAttribute.IsFixedLength">
            <summary>
            Is this a fixed size field?
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Server.SqlFacetAttribute.MaxSize">
            <summary>
            The maximum size of the field (in bytes or characters depending on the field type)
            or -1 if the size can be unlimited.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Server.SqlFacetAttribute.Precision">
            <summary>
            Precision, only valid for numeric types.
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Server.SqlFacetAttribute.Scale">
            <summary>
            Scale, only valid for numeric types. 
            </summary>
        </member>
        <member name="P:Microsoft.SqlServer.Server.SqlFacetAttribute.IsNullable">
            <summary>
            Is this field nullable?
            </summary>
        </member>
        <member name="T:Interop.Kernel32.IoControlCodeAccess">
            <summary>
            <a href="https://docs.microsoft.com/en-us/windows-hardware/drivers/kernel/defining-i-o-control-codes">RequiredAccess</a>.
            Indicates the type of access that a caller must request when opening the file object that represents the device (see IRP_MJ_CREATE).
            </summary>
        </member>
        <member name="F:Interop.Kernel32.IoControlCodeAccess.FILE_ANY_ACCESS">
            <summary>
            The I/O manager sends the IRP for any caller that has a handle to the file object that represents the target device object.
            </summary>
        </member>
        <member name="F:Interop.Kernel32.IoControlCodeAccess.FILE_READ_DATA">
            <summary>
            The I/O manager sends the IRP only for a caller with read access rights, allowing the underlying device driver to transfer
            data from the device to system memory.
            </summary>
        </member>
        <member name="F:Interop.Kernel32.IoControlCodeAccess.FILE_WRITE_DATA">
            <summary>
            The I/O manager sends the IRP only for a caller with write access rights, allowing the underlying device driver to transfer 
            data from system memory to its device.
            </summary>
        </member>
        <member name="M:Interop.Kernel32.CTL_CODE(System.UInt16,System.UInt16,System.Byte,System.Byte)">
            <summary> <a href="https://docs.microsoft.com/en-us/windows-hardware/drivers/kernel/defining-i-o-control-codes">CTL_CODE</a> method.</summary>
            <param name="deviceType">Identifies the device type. This value must match the value that is set in the DeviceType member of the driver's DEVICE_OBJECT structure.</param>
            <param name="function">Identifies the function to be performed by the driver. Values of less than 0x800 are reserved for Microsoft. Values of 0x800 and higher can be used by vendors.</param>
            <param name="method">Indicates how the system will pass data between the caller of DeviceIoControl (or IoBuildDeviceIoControlRequest) and the driver that handles the IRP.</param>
            <param name="access">Indicates the type of access that a caller must request when opening the file object that represents the device (see IRP_MJ_CREATE).</param>
        </member>
        <member name="T:Interop.Kernel32.IoControlTransferType">
            <summary>
            <a href="https://docs.microsoft.com/en-us/windows-hardware/drivers/kernel/buffer-descriptions-for-i-o-control-codes">TransferType</a>.
            Indicates how the system will pass data between the caller of DeviceIoControl (or IoBuildDeviceIoControlRequest) and the driver that handles the IRP.
            </summary>
        </member>
        <member name="F:Interop.Kernel32.IoControlTransferType.METHOD_BUFFERED">
            <summary>
            Specifies the buffered I/O method, which is typically used for transferring small amounts of data per request. 
            Most I/O control codes for device and intermediate drivers use this TransferType value.
            </summary>
        </member>
        <member name="F:Interop.Kernel32.IoControlTransferType.METHOD_IN_DIRECT">
            <summary>
            Specifies the direct I/O method, which is typically used for reading or writing large amounts of data, using DMA or PIO, that must be transferred quickly.
            Specify METHOD_IN_DIRECT if the caller of DeviceIoControl or IoBuildDeviceIoControlRequest will pass data to the driver.
            </summary>
        </member>
        <member name="F:Interop.Kernel32.IoControlTransferType.METHOD_OUT_DIRECT">
            <summary>
            Specifies the direct I/O method, which is typically used for reading or writing large amounts of data, using DMA or PIO, that must be transferred quickly.
            Specify METHOD_OUT_DIRECT if the caller of DeviceIoControl or IoBuildDeviceIoControlRequest will receive data from the driver.
            </summary>
        </member>
        <member name="F:Interop.Kernel32.IoControlTransferType.METHOD_NEITHER">
            <summary>
            Specifies neither buffered nor direct I/O. The I/O manager does not provide any system buffers or MDLs. The IRP supplies the user-mode virtual addresses 
            of the input and output buffers that were specified to DeviceIoControl or IoBuildDeviceIoControlRequest, without validating or mapping them.
            </summary>
        </member>
        <member name="F:Interop.UNICODE_STRING.Length">
            <summary>
            Length in bytes, not including the null terminator, if any.
            </summary>
        </member>
        <member name="F:Interop.UNICODE_STRING.MaximumLength">
            <summary>
            Max size of the buffer in bytes
            </summary>
        </member>
        <member name="T:Interop.OBJECT_ATTRIBUTES">
            <summary>
            <a href="https://msdn.microsoft.com/en-us/library/windows/hardware/ff557749.aspx">OBJECT_ATTRIBUTES</a> structure.
            The OBJECT_ATTRIBUTES structure specifies attributes that can be applied to objects or object handles by routines 
            that create objects and/or return handles to objects.
            </summary>
        </member>
        <member name="F:Interop.OBJECT_ATTRIBUTES.RootDirectory">
            <summary>
            Optional handle to root object directory for the given ObjectName.
            Can be a file system directory or object manager directory.
            </summary>
        </member>
        <member name="F:Interop.OBJECT_ATTRIBUTES.ObjectName">
            <summary>
            Name of the object. Must be fully qualified if RootDirectory isn't set.
            Otherwise is relative to RootDirectory.
            </summary>
        </member>
        <member name="F:Interop.OBJECT_ATTRIBUTES.SecurityDescriptor">
            <summary>
            If null, object will receive default security settings.
            </summary>
        </member>
        <member name="F:Interop.OBJECT_ATTRIBUTES.SecurityQualityOfService">
            <summary>
            Optional quality of service to be applied to the object. Used to indicate
            security impersonation level and context tracking mode (dynamic or static).
            </summary>
        </member>
        <member name="M:Interop.OBJECT_ATTRIBUTES.#ctor(Interop.UNICODE_STRING*,Interop.ObjectAttributes,System.IntPtr)">
            <summary>
            Equivalent of InitializeObjectAttributes macro with the exception that you can directly set SQOS.
            </summary>
        </member>
        <member name="F:Interop.ObjectAttributes.OBJ_INHERIT">
            <summary>
            This handle can be inherited by child processes of the current process.
            </summary>
        </member>
        <member name="F:Interop.ObjectAttributes.OBJ_PERMANENT">
            <summary>
            This flag only applies to objects that are named within the object manager.
            By default, such objects are deleted when all open handles to them are closed.
            If this flag is specified, the object is not deleted when all open handles are closed.
            </summary>
        </member>
        <member name="F:Interop.ObjectAttributes.OBJ_EXCLUSIVE">
            <summary>
            Only a single handle can be open for this object.
            </summary>
        </member>
        <member name="F:Interop.ObjectAttributes.OBJ_CASE_INSENSITIVE">
            <summary>
            Lookups for this object should be case insensitive.
            </summary>
        </member>
        <member name="F:Interop.ObjectAttributes.OBJ_OPENIF">
            <summary>
            Create on existing object should open, not fail with STATUS_OBJECT_NAME_COLLISION.
            </summary>
        </member>
        <member name="F:Interop.ObjectAttributes.OBJ_OPENLINK">
            <summary>
            Open the symbolic link, not its target.
            </summary>
        </member>
        <member name="T:Interop.NtDll.FILE_FULL_EA_INFORMATION">
            <summary>
            <a href="https://docs.microsoft.com/en-us/windows-hardware/drivers/ddi/content/wdm/ns-wdm-_file_full_ea_information">FILE_FULL_EA_INFORMATION</a> structure.
            Provides extended attribute (EA) information. This structure is used primarily by network drivers.
            </summary>
        </member>
        <member name="F:Interop.NtDll.FILE_FULL_EA_INFORMATION.NextEntryOffset">
            <summary>
            The offset of the next FILE_FULL_EA_INFORMATION-type entry. This member is zero if no other entries follow this one.
            </summary>
        </member>
        <member name="F:Interop.NtDll.FILE_FULL_EA_INFORMATION.Flags">
            <summary>
            Can be zero or can be set with FILE_NEED_EA, indicating that the file to which the EA belongs cannot be interpreted without understanding the associated extended attributes.
            </summary>
        </member>
        <member name="F:Interop.NtDll.FILE_FULL_EA_INFORMATION.EaNameLength">
            <summary>
            The length in bytes of the EaName array. This value does not include a null-terminator to EaName.
            </summary>
        </member>
        <member name="F:Interop.NtDll.FILE_FULL_EA_INFORMATION.EaValueLength">
            <summary>
            The length in bytes of each EA value in the array.
            </summary>
        </member>
        <member name="F:Interop.NtDll.IO_STATUS_BLOCK.Status">
            <summary>
            Status
            </summary>
        </member>
        <member name="F:Interop.NtDll.IO_STATUS_BLOCK.Information">
            <summary>
            Request dependent value.
            </summary>
        </member>
        <member name="F:Interop.NtDll.IO_STATUS_BLOCK.IO_STATUS.Status">
            <summary>
            The completion status, either STATUS_SUCCESS if the operation was completed successfully or
            some other informational, warning, or error status.
            </summary>
        </member>
        <member name="F:Interop.NtDll.IO_STATUS_BLOCK.IO_STATUS.Pointer">
            <summary>
            Reserved for internal use.
            </summary>
        </member>
        <member name="T:Interop.NtDll.CreateDisposition">
            <summary>
            File creation disposition when calling directly to NT APIs.
            </summary>
        </member>
        <member name="F:Interop.NtDll.CreateDisposition.FILE_SUPERSEDE">
            <summary>
            Default. Replace or create. Deletes existing file instead of overwriting.
            </summary>
            <remarks>
            As this potentially deletes it requires that DesiredAccess must include Delete.
            This has no equivalent in CreateFile.
            </remarks>
        </member>
        <member name="F:Interop.NtDll.CreateDisposition.FILE_OPEN">
            <summary>
            Open if exists or fail if doesn't exist. Equivalent to OPEN_EXISTING or
            <see cref="F:System.IO.FileMode.Open"/>.
            </summary>
            <remarks>
            TruncateExisting also uses Open and then manually truncates the file
            by calling NtSetInformationFile with FileAllocationInformation and an
            allocation size of 0.
            </remarks>
        </member>
        <member name="F:Interop.NtDll.CreateDisposition.FILE_CREATE">
            <summary>
            Create if doesn't exist or fail if does exist. Equivalent to CREATE_NEW
            or <see cref="F:System.IO.FileMode.CreateNew"/>.
            </summary>
        </member>
        <member name="F:Interop.NtDll.CreateDisposition.FILE_OPEN_IF">
            <summary>
            Open if exists or create if doesn't exist. Equivalent to OPEN_ALWAYS or
            <see cref="F:System.IO.FileMode.OpenOrCreate"/>.
            </summary>
        </member>
        <member name="F:Interop.NtDll.CreateDisposition.FILE_OVERWRITE">
            <summary>
            Open and overwrite if exists or fail if doesn't exist. Equivalent to
            TRUNCATE_EXISTING or <see cref="F:System.IO.FileMode.Truncate"/>.
            </summary>
        </member>
        <member name="F:Interop.NtDll.CreateDisposition.FILE_OVERWRITE_IF">
            <summary>
            Open and overwrite if exists or create if doesn't exist. Equivalent to
            CREATE_ALWAYS or <see cref="F:System.IO.FileMode.Create"/>.
            </summary>
        </member>
        <member name="T:Interop.NtDll.CreateOptions">
            <summary>
            Options for creating/opening files with NtCreateFile.
            </summary>
        </member>
        <member name="F:Interop.NtDll.CreateOptions.FILE_DIRECTORY_FILE">
            <summary>
            File being created or opened must be a directory file. Disposition must be FILE_CREATE, FILE_OPEN,
            or FILE_OPEN_IF.
            </summary>
            <remarks>
            Can only be used with FILE_SYNCHRONOUS_IO_ALERT/NONALERT, FILE_WRITE_THROUGH, FILE_OPEN_FOR_BACKUP_INTENT,
            and FILE_OPEN_BY_FILE_ID flags.
            </remarks>
        </member>
        <member name="F:Interop.NtDll.CreateOptions.FILE_WRITE_THROUGH">
            <summary>
            Applications that write data to the file must actually transfer the data into
            the file before any requested write operation is considered complete. This flag
            is set automatically if FILE_NO_INTERMEDIATE_BUFFERING is set.
            </summary>
        </member>
        <member name="F:Interop.NtDll.CreateOptions.FILE_SEQUENTIAL_ONLY">
            <summary>
            All accesses to the file are sequential.
            </summary>
        </member>
        <member name="F:Interop.NtDll.CreateOptions.FILE_NO_INTERMEDIATE_BUFFERING">
            <summary>
            File cannot be cached in driver buffers. Cannot use with AppendData desired access.
            </summary>
        </member>
        <member name="F:Interop.NtDll.CreateOptions.FILE_SYNCHRONOUS_IO_ALERT">
            <summary>
            All operations are performed synchronously. Any wait on behalf of the caller is
            subject to premature termination from alerts.
            </summary>
            <remarks>
            Cannot be used with FILE_SYNCHRONOUS_IO_NONALERT.
            Synchronous DesiredAccess flag is required. I/O system will maintain file position context.
            </remarks>
        </member>
        <member name="F:Interop.NtDll.CreateOptions.FILE_SYNCHRONOUS_IO_NONALERT">
            <summary>
            All operations are performed synchronously. Waits in the system to synchronize I/O queuing
            and completion are not subject to alerts.
            </summary>
            <remarks>
            Cannot be used with FILE_SYNCHRONOUS_IO_ALERT.
            Synchronous DesiredAccess flag is required. I/O system will maintain file position context.
            </remarks>
        </member>
        <member name="F:Interop.NtDll.CreateOptions.FILE_NON_DIRECTORY_FILE">
            <summary>
            File being created or opened must not be a directory file. Can be a data file, device,
            or volume.
            </summary>
        </member>
        <member name="F:Interop.NtDll.CreateOptions.FILE_CREATE_TREE_CONNECTION">
            <summary>
            Create a tree connection for this file in order to open it over the network.
            </summary>
            <remarks>
            Not used by device and intermediate drivers.
            </remarks>
        </member>
        <member name="F:Interop.NtDll.CreateOptions.FILE_COMPLETE_IF_OPLOCKED">
            <summary>
            Complete the operation immediately with a success code of STATUS_OPLOCK_BREAK_IN_PROGRESS if
            the target file is oplocked.
            </summary>
            <remarks>
            Not compatible with ReserveOpfilter or OpenRequiringOplock.
            Not used by device and intermediate drivers.
            </remarks>
        </member>
        <member name="F:Interop.NtDll.CreateOptions.FILE_NO_EA_KNOWLEDGE">
            <summary>
            If the extended attributes on an existing file being opened indicate that the caller must
            understand extended attributes to properly interpret the file, fail the request.
            </summary>
            <remarks>
            Not used by device and intermediate drivers.
            </remarks>
        </member>
        <member name="F:Interop.NtDll.CreateOptions.FILE_RANDOM_ACCESS">
            <summary>
            Accesses to the file can be random, so no sequential read-ahead operations should be performed
            on the file by FSDs or the system.
            </summary>
        </member>
        <member name="F:Interop.NtDll.CreateOptions.FILE_DELETE_ON_CLOSE">
            <summary>
            Delete the file when the last handle to it is passed to NtClose. Requires Delete flag in
            DesiredAccess parameter.
            </summary>
        </member>
        <member name="F:Interop.NtDll.CreateOptions.FILE_OPEN_BY_FILE_ID">
            <summary>
            Open the file by reference number or object ID. The file name that is specified by the ObjectAttributes
            name parameter includes the 8 or 16 byte file reference number or ID for the file in the ObjectAttributes
            name field. The device name can optionally be prefixed.
            </summary>
            <remarks>
            NTFS supports both reference numbers and object IDs. 16 byte reference numbers are 8 byte numbers padded
            with zeros. ReFS only supports reference numbers (not object IDs). 8 byte and 16 byte reference numbers
            are not related. Note that as the UNICODE_STRING will contain raw byte data, it may not be a "valid" string.
            Not used by device and intermediate drivers.
            </remarks>
            <example>
            \??\C:\{8 bytes of binary FileID}
            \device\HardDiskVolume1\{16 bytes of binary ObjectID}
            {8 bytes of binary FileID}
            </example>
        </member>
        <member name="F:Interop.NtDll.CreateOptions.FILE_OPEN_FOR_BACKUP_INTENT">
            <summary>
            The file is being opened for backup intent. Therefore, the system should check for certain access rights
            and grant the caller the appropriate access to the file before checking the DesiredAccess parameter
            against the file's security descriptor.
            </summary>
            <remarks>
            Not used by device and intermediate drivers.
            </remarks>
        </member>
        <member name="F:Interop.NtDll.CreateOptions.FILE_NO_COMPRESSION">
            <summary>
            When creating a file, specifies that it should not inherit the compression bit from the parent directory.
            </summary>
        </member>
        <member name="F:Interop.NtDll.CreateOptions.FILE_OPEN_REQUIRING_OPLOCK">
            <summary>
            The file is being opened and an opportunistic lock (oplock) on the file is being requested as a single atomic
            operation.
            </summary>
            <remarks>
            The file system checks for oplocks before it performs the create operation and will fail the create with a
            return code of STATUS_CANNOT_BREAK_OPLOCK if the result would be to break an existing oplock.
            Not compatible with CompleteIfOplocked or ReserveOpFilter. Windows 7 and up.
            </remarks>
        </member>
        <member name="F:Interop.NtDll.CreateOptions.FILE_DISALLOW_EXCLUSIVE">
            <summary>
            CreateFile2 uses this flag to prevent opening a file that you don't have access to without specifying 
            FILE_SHARE_READ. (Preventing users that can only read a file from denying access to other readers.)
            </summary>
            <remarks>
            Windows 7 and up.
            </remarks>
        </member>
        <member name="F:Interop.NtDll.CreateOptions.FILE_SESSION_AWARE">
            <summary>
            The client opening the file or device is session aware and per session access is validated if necessary.
            </summary>
            <remarks>
            Windows 8 and up.
            </remarks>
        </member>
        <member name="F:Interop.NtDll.CreateOptions.FILE_RESERVE_OPFILTER">
            <summary>
            This flag allows an application to request a filter opportunistic lock (oplock) to prevent other applications
            from getting share violations.
            </summary>
            <remarks>
            Not compatible with CompleteIfOplocked or OpenRequiringOplock.
            If there are already open handles, the create request will fail with STATUS_OPLOCK_NOT_GRANTED.
            </remarks>
        </member>
        <member name="F:Interop.NtDll.CreateOptions.FILE_OPEN_REPARSE_POINT">
            <summary>
            Open a file with a reparse point attribute, bypassing the normal reparse point processing.
            </summary>
        </member>
        <member name="F:Interop.NtDll.CreateOptions.FILE_OPEN_NO_RECALL">
            <summary>
            Causes files that are marked with the Offline attribute not to be recalled from remote storage.
            </summary>
            <remarks>
            More details can be found in Remote Storage documentation (see Basic Concepts).
            https://technet.microsoft.com/en-us/library/cc938459.aspx
            </remarks>
        </member>
        <member name="T:Interop.NtDll.DesiredAccess">
            <summary>
            System.IO.FileAccess looks up these values when creating handles
            </summary>
            <remarks>
            File Security and Access Rights
            https://msdn.microsoft.com/en-us/library/windows/desktop/aa364399.aspx
            </remarks>
        </member>
        <member name="F:Interop.NtDll.DesiredAccess.FILE_READ_DATA">
            <summary>
            For a file, the right to read data from the file.
            </summary>
            <remarks>
            Directory version of this flag is <see cref="F:Interop.NtDll.DesiredAccess.FILE_LIST_DIRECTORY"/>.
            </remarks>
        </member>
        <member name="F:Interop.NtDll.DesiredAccess.FILE_LIST_DIRECTORY">
            <summary>
            For a directory, the right to list the contents.
            </summary>
            <remarks>
            File version of this flag is <see cref="F:Interop.NtDll.DesiredAccess.FILE_READ_DATA"/>.
            </remarks>
        </member>
        <member name="F:Interop.NtDll.DesiredAccess.FILE_WRITE_DATA">
            <summary>
            For a file, the right to write data to the file.
            </summary>
            <remarks>
            Directory version of this flag is <see cref="F:Interop.NtDll.DesiredAccess.FILE_ADD_FILE"/>.
            </remarks>
        </member>
        <member name="F:Interop.NtDll.DesiredAccess.FILE_ADD_FILE">
            <summary>
            For a directory, the right to create a file in a directory.
            </summary>
            <remarks>
            File version of this flag is <see cref="F:Interop.NtDll.DesiredAccess.FILE_WRITE_DATA"/>.
            </remarks>
        </member>
        <member name="F:Interop.NtDll.DesiredAccess.FILE_APPEND_DATA">
            <summary>
            For a file, the right to append data to a file. <see cref="F:Interop.NtDll.DesiredAccess.FILE_WRITE_DATA"/> is needed
            to overwrite existing data.
            </summary>
            <remarks>
            Directory version of this flag is <see cref="F:Interop.NtDll.DesiredAccess.FILE_ADD_SUBDIRECTORY"/>.
            </remarks>
        </member>
        <member name="F:Interop.NtDll.DesiredAccess.FILE_ADD_SUBDIRECTORY">
            <summary>
            For a directory, the right to create a subdirectory.
            </summary>
            <remarks>
            File version of this flag is <see cref="F:Interop.NtDll.DesiredAccess.FILE_APPEND_DATA"/>.
            </remarks>
        </member>
        <member name="F:Interop.NtDll.DesiredAccess.FILE_CREATE_PIPE_INSTANCE">
            <summary>
            For a named pipe, the right to create a pipe instance.
            </summary>
        </member>
        <member name="F:Interop.NtDll.DesiredAccess.FILE_READ_EA">
            <summary>
            The right to read extended attributes.
            </summary>
        </member>
        <member name="F:Interop.NtDll.DesiredAccess.FILE_WRITE_EA">
            <summary>
            The right to write extended attributes.
            </summary>
        </member>
        <member name="F:Interop.NtDll.DesiredAccess.FILE_EXECUTE">
            <summary>
            The right to execute the file.
            </summary>
            <remarks>
            Directory version of this flag is <see cref="F:Interop.NtDll.DesiredAccess.FILE_TRAVERSE"/>.
            </remarks>
        </member>
        <member name="F:Interop.NtDll.DesiredAccess.FILE_TRAVERSE">
            <summary>
            For a directory, the right to traverse the directory.
            </summary>
            <remarks>
            File version of this flag is <see cref="F:Interop.NtDll.DesiredAccess.FILE_EXECUTE"/>.
            </remarks>
        </member>
        <member name="F:Interop.NtDll.DesiredAccess.FILE_DELETE_CHILD">
            <summary>
            For a directory, the right to delete a directory and all
            the files it contains, including read-only files.
            </summary>
        </member>
        <member name="F:Interop.NtDll.DesiredAccess.FILE_READ_ATTRIBUTES">
            <summary>
            The right to read attributes.
            </summary>
        </member>
        <member name="F:Interop.NtDll.DesiredAccess.FILE_WRITE_ATTRIBUTES">
            <summary>
            The right to write attributes.
            </summary>
        </member>
        <member name="F:Interop.NtDll.DesiredAccess.FILE_ALL_ACCESS">
            <summary>
            All standard and specific rights. [FILE_ALL_ACCESS]
            </summary>
        </member>
        <member name="F:Interop.NtDll.DesiredAccess.DELETE">
            <summary>
            The right to delete the object.
            </summary>
        </member>
        <member name="F:Interop.NtDll.DesiredAccess.READ_CONTROL">
            <summary>
            The right to read the information in the object's security descriptor.
            Doesn't include system access control list info (SACL).
            </summary>
        </member>
        <member name="F:Interop.NtDll.DesiredAccess.WRITE_DAC">
            <summary>
            The right to modify the discretionary access control list (DACL) in the
            object's security descriptor.
            </summary>
        </member>
        <member name="F:Interop.NtDll.DesiredAccess.WRITE_OWNER">
            <summary>
            The right to change the owner in the object's security descriptor.
            </summary>
        </member>
        <member name="F:Interop.NtDll.DesiredAccess.SYNCHRONIZE">
            <summary>
            The right to use the object for synchronization. Enables a thread to wait until the object
            is in the signaled state. This is required if opening a synchronous handle.
            </summary>
        </member>
        <member name="F:Interop.NtDll.DesiredAccess.STANDARD_RIGHTS_READ">
            <summary>
            Same as READ_CONTROL.
            </summary>
        </member>
        <member name="F:Interop.NtDll.DesiredAccess.STANDARD_RIGHTS_WRITE">
            <summary>
            Same as READ_CONTROL.
            </summary>
        </member>
        <member name="F:Interop.NtDll.DesiredAccess.STANDARD_RIGHTS_EXECUTE">
            <summary>
            Same as READ_CONTROL.
            </summary>
        </member>
        <member name="F:Interop.NtDll.DesiredAccess.FILE_GENERIC_READ">
            <summary>
            Maps internally to <see cref="F:Interop.NtDll.DesiredAccess.FILE_READ_ATTRIBUTES"/> | <see cref="F:Interop.NtDll.DesiredAccess.FILE_READ_DATA"/> | <see cref="F:Interop.NtDll.DesiredAccess.FILE_READ_EA"/>
            | <see cref="F:Interop.NtDll.DesiredAccess.STANDARD_RIGHTS_READ"/> | <see cref="F:Interop.NtDll.DesiredAccess.SYNCHRONIZE"/>.
            (For directories, <see cref="F:Interop.NtDll.DesiredAccess.FILE_READ_ATTRIBUTES"/> | <see cref="F:Interop.NtDll.DesiredAccess.FILE_LIST_DIRECTORY"/> | <see cref="F:Interop.NtDll.DesiredAccess.FILE_READ_EA"/>
            | <see cref="F:Interop.NtDll.DesiredAccess.STANDARD_RIGHTS_READ"/> | <see cref="F:Interop.NtDll.DesiredAccess.SYNCHRONIZE"/>.)
            </summary>
        </member>
        <member name="F:Interop.NtDll.DesiredAccess.FILE_GENERIC_WRITE">
            <summary>
            Maps internally to <see cref="F:Interop.NtDll.DesiredAccess.FILE_APPEND_DATA"/> | <see cref="F:Interop.NtDll.DesiredAccess.FILE_WRITE_ATTRIBUTES"/> | <see cref="F:Interop.NtDll.DesiredAccess.FILE_WRITE_DATA"/>
            | <see cref="F:Interop.NtDll.DesiredAccess.FILE_WRITE_EA"/> | <see cref="F:Interop.NtDll.DesiredAccess.STANDARD_RIGHTS_READ"/> | <see cref="F:Interop.NtDll.DesiredAccess.SYNCHRONIZE"/>.
            (For directories, <see cref="F:Interop.NtDll.DesiredAccess.FILE_ADD_SUBDIRECTORY"/> | <see cref="F:Interop.NtDll.DesiredAccess.FILE_WRITE_ATTRIBUTES"/> | <see cref="F:Interop.NtDll.DesiredAccess.FILE_ADD_FILE"/> AddFile
            | <see cref="F:Interop.NtDll.DesiredAccess.FILE_WRITE_EA"/> | <see cref="F:Interop.NtDll.DesiredAccess.STANDARD_RIGHTS_READ"/> | <see cref="F:Interop.NtDll.DesiredAccess.SYNCHRONIZE"/>.)
            </summary>
        </member>
        <member name="F:Interop.NtDll.DesiredAccess.FILE_GENERIC_EXECUTE">
            <summary>
            Maps internally to <see cref="F:Interop.NtDll.DesiredAccess.FILE_EXECUTE"/> | <see cref="F:Interop.NtDll.DesiredAccess.FILE_READ_ATTRIBUTES"/> | <see cref="F:Interop.NtDll.DesiredAccess.STANDARD_RIGHTS_EXECUTE"/>
            | <see cref="F:Interop.NtDll.DesiredAccess.SYNCHRONIZE"/>.
            (For directories, <see cref="F:Interop.NtDll.DesiredAccess.FILE_DELETE_CHILD"/> | <see cref="F:Interop.NtDll.DesiredAccess.FILE_READ_ATTRIBUTES"/> | <see cref="F:Interop.NtDll.DesiredAccess.STANDARD_RIGHTS_EXECUTE"/>
            | <see cref="F:Interop.NtDll.DesiredAccess.SYNCHRONIZE"/>.)
            </summary>
        </member>
    </members>
</doc>
