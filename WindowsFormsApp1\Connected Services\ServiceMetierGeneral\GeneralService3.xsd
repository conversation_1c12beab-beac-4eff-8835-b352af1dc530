<?xml version="1.0" encoding="utf-8"?>
<xs:schema xmlns:tns="http://schemas.datacontract.org/2004/07/MetierRvMedical.Model" elementFormDefault="qualified" targetNamespace="http://schemas.datacontract.org/2004/07/MetierRvMedical.Model" xmlns:xs="http://www.w3.org/2001/XMLSchema">
  <xs:complexType name="ArrayOfSoin">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="Soin" nillable="true" type="tns:Soin" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfSoin" nillable="true" type="tns:ArrayOfSoin" />
  <xs:complexType name="Soin">
    <xs:sequence>
      <xs:element minOccurs="0" name="Category" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Duration" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="IdSoin" type="xs:int" />
      <xs:element minOccurs="0" name="NameSoin" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="Price" type="xs:int" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="Soin" nillable="true" type="tns:Soin" />
  <xs:complexType name="ArrayOfGroupeSanguin">
    <xs:sequence>
      <xs:element minOccurs="0" maxOccurs="unbounded" name="GroupeSanguin" nillable="true" type="tns:GroupeSanguin" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="ArrayOfGroupeSanguin" nillable="true" type="tns:ArrayOfGroupeSanguin" />
  <xs:complexType name="GroupeSanguin">
    <xs:sequence>
      <xs:element minOccurs="0" name="CodeGroupeSanguin" nillable="true" type="xs:string" />
      <xs:element minOccurs="0" name="IdGroupeSanguin" type="xs:int" />
      <xs:element minOccurs="0" name="NomGroupeSanguin" nillable="true" type="xs:string" />
    </xs:sequence>
  </xs:complexType>
  <xs:element name="GroupeSanguin" nillable="true" type="tns:GroupeSanguin" />
</xs:schema>