﻿//------------------------------------------------------------------------------
// <auto-generated>
//     Ce code a été généré par un outil.
//     Version du runtime :4.0.30319.42000
//
//     Les modifications apportées à ce fichier peuvent provoquer un comportement incorrect et seront perdues si
//     le code est régénéré.
// </auto-generated>
//------------------------------------------------------------------------------

namespace WindowsFormsApp1.ServiceMetierGeneral {
    using System.Runtime.Serialization;
    using System;
    
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="Soin", Namespace="http://schemas.datacontract.org/2004/07/MetierRvMedical.Model")]
    [System.SerializableAttribute()]
    public partial class Soin : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string CategoryField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string DurationField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private int IdSoinField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string NameSoinField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private int PriceField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Category {
            get {
                return this.CategoryField;
            }
            set {
                if ((object.ReferenceEquals(this.CategoryField, value) != true)) {
                    this.CategoryField = value;
                    this.RaisePropertyChanged("Category");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Duration {
            get {
                return this.DurationField;
            }
            set {
                if ((object.ReferenceEquals(this.DurationField, value) != true)) {
                    this.DurationField = value;
                    this.RaisePropertyChanged("Duration");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int IdSoin {
            get {
                return this.IdSoinField;
            }
            set {
                if ((this.IdSoinField.Equals(value) != true)) {
                    this.IdSoinField = value;
                    this.RaisePropertyChanged("IdSoin");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string NameSoin {
            get {
                return this.NameSoinField;
            }
            set {
                if ((object.ReferenceEquals(this.NameSoinField, value) != true)) {
                    this.NameSoinField = value;
                    this.RaisePropertyChanged("NameSoin");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int Price {
            get {
                return this.PriceField;
            }
            set {
                if ((this.PriceField.Equals(value) != true)) {
                    this.PriceField = value;
                    this.RaisePropertyChanged("Price");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="GroupeSanguin", Namespace="http://schemas.datacontract.org/2004/07/MetierRvMedical.Model")]
    [System.SerializableAttribute()]
    public partial class GroupeSanguin : object, System.Runtime.Serialization.IExtensibleDataObject, System.ComponentModel.INotifyPropertyChanged {
        
        [System.NonSerializedAttribute()]
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string CodeGroupeSanguinField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private int IdGroupeSanguinField;
        
        [System.Runtime.Serialization.OptionalFieldAttribute()]
        private string NomGroupeSanguinField;
        
        [global::System.ComponentModel.BrowsableAttribute(false)]
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData {
            get {
                return this.extensionDataField;
            }
            set {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string CodeGroupeSanguin {
            get {
                return this.CodeGroupeSanguinField;
            }
            set {
                if ((object.ReferenceEquals(this.CodeGroupeSanguinField, value) != true)) {
                    this.CodeGroupeSanguinField = value;
                    this.RaisePropertyChanged("CodeGroupeSanguin");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public int IdGroupeSanguin {
            get {
                return this.IdGroupeSanguinField;
            }
            set {
                if ((this.IdGroupeSanguinField.Equals(value) != true)) {
                    this.IdGroupeSanguinField = value;
                    this.RaisePropertyChanged("IdGroupeSanguin");
                }
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string NomGroupeSanguin {
            get {
                return this.NomGroupeSanguinField;
            }
            set {
                if ((object.ReferenceEquals(this.NomGroupeSanguinField, value) != true)) {
                    this.NomGroupeSanguinField = value;
                    this.RaisePropertyChanged("NomGroupeSanguin");
                }
            }
        }
        
        public event System.ComponentModel.PropertyChangedEventHandler PropertyChanged;
        
        protected void RaisePropertyChanged(string propertyName) {
            System.ComponentModel.PropertyChangedEventHandler propertyChanged = this.PropertyChanged;
            if ((propertyChanged != null)) {
                propertyChanged(this, new System.ComponentModel.PropertyChangedEventArgs(propertyName));
            }
        }
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ServiceModel.ServiceContractAttribute(ConfigurationName="ServiceMetierGeneral.IGeneralService")]
    public interface IGeneralService {
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IGeneralService/GetPhoneNumbersForAutoComplete", ReplyAction="http://tempuri.org/IGeneralService/GetPhoneNumbersForAutoCompleteResponse")]
        string[] GetPhoneNumbersForAutoComplete(int limit);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IGeneralService/GetPhoneNumbersForAutoComplete", ReplyAction="http://tempuri.org/IGeneralService/GetPhoneNumbersForAutoCompleteResponse")]
        System.Threading.Tasks.Task<string[]> GetPhoneNumbersForAutoCompleteAsync(int limit);
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IGeneralService/GetListSoins", ReplyAction="http://tempuri.org/IGeneralService/GetListSoinsResponse")]
        WindowsFormsApp1.ServiceMetierGeneral.Soin[] GetListSoins();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IGeneralService/GetListSoins", ReplyAction="http://tempuri.org/IGeneralService/GetListSoinsResponse")]
        System.Threading.Tasks.Task<WindowsFormsApp1.ServiceMetierGeneral.Soin[]> GetListSoinsAsync();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IGeneralService/GetListeGroupesSanguins", ReplyAction="http://tempuri.org/IGeneralService/GetListeGroupesSanguinsResponse")]
        WindowsFormsApp1.ServiceMetierGeneral.GroupeSanguin[] GetListeGroupesSanguins();
        
        [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/IGeneralService/GetListeGroupesSanguins", ReplyAction="http://tempuri.org/IGeneralService/GetListeGroupesSanguinsResponse")]
        System.Threading.Tasks.Task<WindowsFormsApp1.ServiceMetierGeneral.GroupeSanguin[]> GetListeGroupesSanguinsAsync();
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    public interface IGeneralServiceChannel : WindowsFormsApp1.ServiceMetierGeneral.IGeneralService, System.ServiceModel.IClientChannel {
    }
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    public partial class GeneralServiceClient : System.ServiceModel.ClientBase<WindowsFormsApp1.ServiceMetierGeneral.IGeneralService>, WindowsFormsApp1.ServiceMetierGeneral.IGeneralService {
        
        public GeneralServiceClient() {
        }
        
        public GeneralServiceClient(string endpointConfigurationName) : 
                base(endpointConfigurationName) {
        }
        
        public GeneralServiceClient(string endpointConfigurationName, string remoteAddress) : 
                base(endpointConfigurationName, remoteAddress) {
        }
        
        public GeneralServiceClient(string endpointConfigurationName, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(endpointConfigurationName, remoteAddress) {
        }
        
        public GeneralServiceClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
                base(binding, remoteAddress) {
        }
        
        public string[] GetPhoneNumbersForAutoComplete(int limit) {
            return base.Channel.GetPhoneNumbersForAutoComplete(limit);
        }
        
        public System.Threading.Tasks.Task<string[]> GetPhoneNumbersForAutoCompleteAsync(int limit) {
            return base.Channel.GetPhoneNumbersForAutoCompleteAsync(limit);
        }
        
        public WindowsFormsApp1.ServiceMetierGeneral.Soin[] GetListSoins() {
            return base.Channel.GetListSoins();
        }
        
        public System.Threading.Tasks.Task<WindowsFormsApp1.ServiceMetierGeneral.Soin[]> GetListSoinsAsync() {
            return base.Channel.GetListSoinsAsync();
        }
        
        public WindowsFormsApp1.ServiceMetierGeneral.GroupeSanguin[] GetListeGroupesSanguins() {
            return base.Channel.GetListeGroupesSanguins();
        }
        
        public System.Threading.Tasks.Task<WindowsFormsApp1.ServiceMetierGeneral.GroupeSanguin[]> GetListeGroupesSanguinsAsync() {
            return base.Channel.GetListeGroupesSanguinsAsync();
        }
    }
}
